{"name": "web", "version": "1.0.0-rc.173", "private": true, "scripts": {"build": "ng build", "build:prod:careers": "export NG_ENV='production' && ng build unifiedhub-careers --configuration=production --aot", "build:prod:main": "export NG_ENV='production' && ng build unifiedhub --configuration=production --aot", "build:staging:careers": "export NG_ENV='staging' && ng build unifiedhub-careers --configuration=staging --aot", "build:staging:main": "export NG_ENV='staging' && ng build unifiedhub --configuration=staging --aot", "ng": "ng", "start:careers": "ng serve --project=unifiedhub-careers --port=4300", "start:main": "ng serve --project=unifiedhub --port=4200"}, "dependencies": {"@angular/animations": "^17.3.0", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/google-maps": "^17.3.10", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@microsoft/signalr": "^8.0.7", "@ng-omar/translation": "^17.0.1", "@ng-select/ng-select": "^12.0.7", "@ngx-formly/core": "^6.3.0", "chart.js": "^4.4.3", "date-fns": "^3.6.0", "flatpickr": "^4.6.13", "quill": "^2.0.3", "quill-delta-to-html": "^0.12.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.6", "@angular-eslint/builder": "17.3.0", "@angular-eslint/eslint-plugin": "17.3.0", "@angular-eslint/eslint-plugin-template": "17.3.0", "@angular-eslint/schematics": "17.3.0", "@angular-eslint/template-parser": "17.3.0", "@angular/cli": "^17.3.3", "@angular/compiler-cli": "^17.3.0", "@tailwindcss/container-queries": "^0.1.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "7.2.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^17.3.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.3", "typescript": "~5.4.2"}, "engines": {"node": ">=20.12.0 <20.13.0", "npm": ">=10.5.0 <10.6.0"}}