import { EnvironmentProviders, Provider } from '@angular/core';
import { AUTH_CONFIG, AuthConfig } from '@unifiedhub/auth/core';

export const authConfig = (): Provider | EnvironmentProviders => {
    return {
        provide: AUTH_CONFIG,
        useFactory: () =>
            ({
                uaepass: {
                    enabled: true,
                    registerIfDoesNotExist: true,
                },
            }) satisfies AuthConfig,
    };
};
