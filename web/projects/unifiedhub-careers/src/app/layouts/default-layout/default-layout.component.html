<div class="flex h-full w-full flex-col">
    <div class="z-10 shrink-0 bg-white p-4 shadow">
        @if (identity()) {
            <div class="flex flex-row items-center gap-2">
                <div class="flex grow flex-row items-center gap-2">
                    <!-- Home page -->
                    <a [routerLink]="['']" class="btn btn-sm btn-primary">
                        {{ 'translate_home_page' | translate }}
                    </a>

                    <!-- Profile -->
                    <a
                        [routerLink]="['', 'profile']"
                        class="btn btn-sm btn-primary"
                    >
                        {{ 'translate_profile' | translate }}
                    </a>

                    <!-- Applications -->
                    <a
                        [routerLink]="['', 'applications']"
                        class="btn btn-sm btn-primary"
                    >
                        {{ 'translate_applications' | translate }}
                    </a>

                    <!-- Account -->
                    <a
                        [routerLink]="['', 'account']"
                        class="btn btn-sm btn-primary"
                    >
                        {{ 'translate_account' | translate }}
                    </a>
                </div>
                <div class="flex shrink-0 flex-row items-center gap-2">
                    <span>
                        {{
                            identity()!.user.name | translateMultilingualString
                        }}
                    </span>
                    <button class="shrink-0" (click)="logout()">
                        <i class="fa-solid fa-right-from-bracket"></i>
                    </button>
                </div>
            </div>
        } @else {
            <a
                [routerLink]="['', 'auth', 'login']"
                class="btn btn-sm btn-primary"
            >
                {{ 'translate_login' | translate }}
            </a>
        }
    </div>
    <main class="block h-full w-full grow overflow-y-auto bg-white p-4">
        <router-outlet />
    </main>
</div>
