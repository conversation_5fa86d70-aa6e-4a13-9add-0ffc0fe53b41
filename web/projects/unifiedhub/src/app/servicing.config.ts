import { EnvironmentProviders, Provider } from '@angular/core';
import { SERVICING_CONFIG, ServicingConfig } from '@unifiedhub/servicing/core';
import { MultilingualStringTranslatorService } from '@unifiedhub/core';
import { p } from '@unifiedhub/lib';
import { Employee, formatEmployeeName } from '@unifiedhub/employment/core';

export const servicingConfig = (): Provider | EnvironmentProviders => {
    return {
        provide: SERVICING_CONFIG,
        deps: [MultilingualStringTranslatorService],
        useFactory: () =>
            ({
                formatUserFn: user => {
                    const employee = user.globalExtraData?.[
                        'employee'
                    ] as Employee;
                    return {
                        value: formatEmployeeName(employee) ?? user.name,
                        linkConfig: employee && {
                            permissionId: p.employees.read,
                            value: ['', 'employees', employee.id],
                        },
                    };
                },
                additionalDetailComponent: async request => {
                    if (
                        request.service.builtInId ===
                        'services:apply_for_vacancy'
                    )
                        return {
                            component: await import(
                                '@unifiedhub/recruitment/core'
                            ).then(x => x.VacancyApplicationDetailComponent),
                            inputs: {
                                vacancyId: (
                                    request.data['vacancy'] as Record<
                                        string,
                                        string
                                    >
                                )['id'],

                                applicantId: (
                                    request.data['applicant'] as Record<
                                        string,
                                        string
                                    >
                                )['id'],
                            },
                            permissionId: p.recruitment.applications.read,
                        };

                    return undefined;
                },
            }) satisfies ServicingConfig,
    };
};
