import { ChangeDetectionStrategy, Component, output } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslationService } from '@ng-omar/translation';
import { FormsModule } from '@angular/forms';
import { SelectInputComponent } from '@unifiedhub/ui';
import { UserCardComponent } from '../user-card/user-card.component';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgSelectModule,
        FormsModule,
        SelectInputComponent,
        UserCardComponent,
    ],
})
export class HeaderComponent {
    public readonly sidebarToggle = output<void>();

    public constructor(
        protected readonly translationService: TranslationService,
    ) {}
}
