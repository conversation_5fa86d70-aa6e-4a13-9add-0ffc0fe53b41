<div class="flex shrink-0 flex-row items-center gap-2">
    <!-- User profile -->
    <div class="relative">
        <div
            class="h-10 w-10 shrink-0 overflow-hidden rounded-full bg-gray-300"
        >
            <a [routerLink]="['', 'account']">
                <lib-image
                    class="h-full w-full"
                    [imageObservable]="userPhoto$"
                    [isObjectFitContain]="false"
                />
            </a>
        </div>
        <div
            class="absolute -bottom-0.5 right-0 z-[1] h-3 w-3 rounded-full"
            [ngClass]="{
                'bg-gray-400': signalrStatus() === 'disconnected',
                'bg-amber-500': signalrStatus() === 'connecting',
                'bg-emerald-500': signalrStatus() === 'connected'
            }"
        ></div>
    </div>

    <!-- User name -->
    <div class="flex grow flex-col gap-1">
        <div>
            {{
                (authService.identity$ | async)?.user?.name
                    | translateMultilingualString
            }}
        </div>
        <div class="text-xs text-emerald-500">
            <ng-container [ngSwitch]="signalrStatus()">
                <div class="text-emerald-500" *ngSwitchCase="'connected'">
                    {{ 'translate_connected_now' | translate }}
                </div>

                <div class="text-amber-500" *ngSwitchCase="'connecting'">
                    {{ 'translate_connecting_in_progress' | translate }}
                </div>

                <div class="text-gray-400" *ngSwitchCase="'disconnected'">
                    {{ 'translate_you_are_offline' | translate }}
                </div>
            </ng-container>
        </div>
    </div>

    <!-- Logout button -->
    <button class="shrink-0" (click)="logout()">
        <i class="fa-solid fa-right-from-bracket"></i>
    </button>
</div>
