import { ApplicationConfig } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { registerLocaleData } from '@angular/common';
import localeAr from '@angular/common/locales/ar';
import { environment } from '../environments/environment';

import { locale as arLocale } from './i18n/app/ar';
import { locale as enLocale } from './i18n/app/en';
import { servicingConfig } from './servicing.config';
import { appProviders } from '@unifiedhub/lib';
import { authConfig } from './auth.config';

registerLocaleData(localeAr, 'ar');

export const appConfig: ApplicationConfig = {
    providers: [
        [
            ...appProviders({
                apiUrl: environment.apiUrl,
                translation: {
                    endpoint: environment.apiUrl,
                    strings: [arLocale, enLocale],
                },
                uaepass: environment.uaepass,
            }),

            authConfig(),
            servicingConfig(),

            provideRouter(routes),
        ],
    ],
};
