import { ILocale } from '@ng-omar/translation';
import { InjectionToken } from '@angular/core';

// eslint-disable-next-line @typescript-eslint/naming-convention
export const APP_CONFIG = new InjectionToken<AppConfig>('config');

export type AppConfig = {
    apiUrl: string;
    translation: {
        strings: ILocale[];
        endpoint: string;
    };
    uaepass: {
        clientId: string;
        redirectUri: string;
        endpoints: {
            authorize: string;
        };
    };
};
