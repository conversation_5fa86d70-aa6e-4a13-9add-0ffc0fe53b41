import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { LeaveListComponent } from '../../components';
import { p } from '../../../../../permissions';
import { RouterLink } from '@angular/router';
import { PageContainerComponent } from '../../../../../ui';
import { RbacDirective } from '../../../../../core';

@Component({
    selector: 'lib-leave-list-page',
    templateUrl: './leave-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PageContainerComponent,
        TranslateModule,
        LeaveListComponent,
        RbacDirective,
        RouterLink,
    ],
})
export class LeaveListPageComponent {
    protected readonly p = p;
}
