import { Leave, LeaveFilterAttrs } from '../../../types';
import { getEmployeeListConfig } from '../../../../employment/core';
import { Injector } from '@angular/core';
import { LeavesService } from '../../leaves.service';
import { Observable } from 'rxjs';
import { p } from '../../../../../permissions';
import { Filter, PaginatedResult } from '../../../../../common';
import { DynamicListFullConfig } from '../../../../../features';

export const config = (
    injector: Injector,
    filter?: LeaveFilterAttrs,
    fetcher?: (
        filter: Filter<LeaveFilterAttrs>,
    ) => Observable<PaginatedResult<Leave>>,
    hiddenColumns?: {
        employee?: boolean;
    },
    hiddenFilters?: {
        employee?: boolean;
    },
): DynamicListFullConfig<Leave, LeaveFilterAttrs> => {
    const leavesService = injector.get(LeavesService);
    const employeeListConfig = getEmployeeListConfig();

    return {
        defaultFilter: filter,
        columnConfigFn: items => [
            ...(hiddenColumns?.employee
                ? []
                : employeeListConfig.columnConfigFn(
                      'name',
                      'number',
                      'department',
                      'job_level',
                  )(items.map(x => x.employee))),

            {
                label: 'translate_leave_type',
            },

            {
                label: 'translate_from',
            },

            {
                label: 'translate_to',
            },

            {
                label: 'translate_total_days',
            },

            {
                label: 'translate_days_balance_before_leave',
            },

            {
                label: 'translate_leave_request',
                permissionId: p.servicing.requests.read,
            },
        ],

        valueConfigFn: item => [
            ...(hiddenColumns?.employee
                ? []
                : employeeListConfig.valueConfigFn(
                      'name',
                      'number',
                      'department',
                      'job_level',
                  )(item.employee)),

            {
                type: 'id_to_name',
                value: item.type,
                idToName$: leavesService.leaveTypes(),
            },

            {
                value: item.from,
                type: 'date',
            },

            {
                value: item.to,
                type: 'date',
            },

            {
                value: item.dayCount,
                type: 'number',
                round: 2,
            },

            {
                value: item.daysBalanceBeforeLeave,
                type: 'number',
                round: 2,
            },

            {
                value: item.serviceRequest
                    ? 'translate_go_to_request_details'
                    : 'translate_no_request',
                linkConfig: item.serviceRequest && {
                    value: [
                        '',
                        'servicing',
                        'requests',
                        item.serviceRequest?.id,
                    ],
                },
            },
        ],

        rowConfigFn: (item: Leave) => ({
            editButtonConfig: {
                permissionId: p.leaves.write,
                link: ['', 'leaves', 'edit', item.id],
            },
        }),

        filter: [
            ...(hiddenFilters?.employee
                ? []
                : employeeListConfig.filterConfigFn(
                      injector,
                      'keyword',
                      'number',
                      'department',
                      'job_title',
                      'job_classification',
                  )),

            {
                id: 'types',
                type: 'select',
                label: 'translate_leave_type',
                bindValue: 'id',
                bindLabel: 'name',
                items$: leavesService.leaveTypes(),
                isMulti: true,
            },

            {
                id: 'from',
                type: 'datetime',
                label: 'translate_from',
            },

            {
                id: 'to',
                type: 'datetime',
                label: 'translate_to',
            },
        ],

        fetcher: fetcher ?? leavesService.list.bind(leavesService),
        deleter: leavesService.delete.bind(leavesService),

        actionsFn: item => [
            {
                type: 'success',
                iconClasses: 'fa fa-eye',
                linkConfig: {
                    value: ['', 'leaves', item.id],
                },
            },
        ],
    };
};
