import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    signal,
    TemplateRef,
    viewChild,
} from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { LeaveBalancesService } from '../../../leave-balances/leave-balances.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'lib-leave-new-page',
    templateUrl: './leave-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent, TranslateModule],
})
export class LeaveNewPageComponent {
    protected readonly configFn = computed(() => {
        return configFn(
            this.injector,
            this.leaveBalanceTemplate()!,
            this.updateLeaveBalance.bind(this),
        );
    });

    protected leaveBalance = signal<number | undefined>(undefined);

    private readonly leaveBalanceTemplate = viewChild<TemplateRef<any>>(
        'leaveBalanceTemplate',
    );

    public constructor(
        private readonly injector: Injector,
        private readonly leaveBalancesService: LeaveBalancesService,
    ) {}

    private updateLeaveBalance(employeeId?: string, type?: string): void {
        if (!employeeId || !type) {
            this.leaveBalance.set(undefined);
            return;
        }

        this.leaveBalancesService
            .getBalanceForEmployee(employeeId!, type!)
            .subscribe(data => {
                this.leaveBalance.set(data.balance);
            });
    }
}
