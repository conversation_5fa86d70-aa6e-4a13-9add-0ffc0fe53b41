import { Routes } from '@angular/router';
import { LeavesComponent } from './leaves.component';
import { LeaveListPageComponent } from './pages/leave-list-page/leave-list-page.component';
import { LeaveDetailPageComponent } from './pages/leave-detail-page/leave-detail-page.component';
import { LeaveNewPageComponent } from './pages/leave-new-page/leave-new-page.component';

export const routes = [
    {
        path: '',
        component: LeavesComponent,
        children: [
            {
                path: '',
                component: LeaveListPageComponent,
                data: {
                    title: 'translate_leaves',
                },
            },

            {
                path: 'new',
                component: LeaveNewPageComponent,
                data: {
                    title: 'translate_add_new_leave',
                },
            },

            {
                path: 'edit/:id',
                component: LeaveNewPageComponent,
                data: {
                    title: 'translate_update_leave_details',
                },
            },

            {
                path: ':id',
                component: LeaveDetailPageComponent,
                data: {
                    title: 'translate_leave_details',
                },
            },
        ],
    },
] satisfies Routes;
