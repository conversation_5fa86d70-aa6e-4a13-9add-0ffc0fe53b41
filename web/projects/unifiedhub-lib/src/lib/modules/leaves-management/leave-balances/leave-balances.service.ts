import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import {
    LeaveBalance,
    LeaveBalanceFilterAttrs,
    LeaveBalanceTransaction,
    LeaveBalanceTransactionFilterAttrs,
} from '../types';
import { Observable } from 'rxjs';
import { Employee } from '../../employment/core';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import {
    Filter,
    Item,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';

@Injectable()
export class LeaveBalancesService extends CrudService<
    LeaveBalance,
    LeaveBalanceFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public leaveTypesWithBalances(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(`${this.getBaseEndpoint()}/types`, {
            context: new HttpContext().set(CACHE_INTERCEPTOR, true),
        });
    }

    public transactions(
        employeeId: string,
        type: string,
        filter: Filter<LeaveBalanceTransactionFilterAttrs>,
    ): Observable<PaginatedResult<LeaveBalanceTransaction>> {
        return this.httpClient.get<PaginatedResult<LeaveBalanceTransaction>>(
            `${this.getBaseEndpoint()}/${employeeId}/${type}/transactions`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }

    public addOrSubtract(
        employeeId: string,
        type: string,
        data: { days: number; notes?: string },
    ): Observable<unknown> {
        return this.httpClient.post(
            `${this.getBaseEndpoint()}/${employeeId}/${type}/add-or-subtract`,
            data,
        );
    }

    public getBalanceForEmployee(
        employeeId: string,
        type: string,
    ): Observable<{ employee: Employee; balance?: number }> {
        return this.httpClient.get<{ employee: Employee; balance?: number }>(
            `${this.getBaseEndpoint()}/employees/${employeeId}/${type}`,
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/leaves/balances`;
    }
}
