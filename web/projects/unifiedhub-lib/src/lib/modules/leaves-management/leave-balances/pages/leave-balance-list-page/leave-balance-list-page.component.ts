import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
    OutputRefSubscription,
    viewChild,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { config } from './config/list.config';
import { transactionListConfig } from './config/transaction-list.config';
import { addOrSubtractNewConfig } from './config/add-or-subtract-new.config';
import {
    DynamicListFullComponent,
    DynamicListPageComponent,
    DynamicNewFormComponent,
    ModalService,
} from '../../../../../features';
import { CitiesService } from '../../../../foundation/cities/cities.service';

@Component({
    selector: 'lib-leave-balance-list-page',
    templateUrl: './leave-balance-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent, TranslateModule],
    providers: [CitiesService],
})
export class LeaveBalanceListPageComponent {
    protected readonly config = config(
        inject(Injector),
        this.showTransactions.bind(this),
        this.addOrSubtract.bind(this),
    );

    private readonly list =
        viewChild<DynamicListPageComponent<unknown, unknown>>('list');

    public constructor(
        private readonly modalService: ModalService,
        private readonly injector: Injector,
        private readonly translateService: TranslateService,
    ) {}

    private async showTransactions(
        employeeId: string,
        type: string,
    ): Promise<void> {
        this.translateService
            .get('translate_balance_transactions')
            .subscribe(async title => {
                await this.modalService.show(DynamicListFullComponent, {
                    title,
                    // size: { width: '80%' },
                    inputs: {
                        config: transactionListConfig(
                            this.injector,
                            employeeId,
                            type,
                        ),
                    },
                });
            });
    }

    private async addOrSubtract(
        employeeId: string,
        type: string,
    ): Promise<void> {
        this.translateService
            .get('translate_add_or_subtract_balance')
            .subscribe(async title => {
                const subscriptions: OutputRefSubscription[] = [];

                const component = await this.modalService.show(
                    DynamicNewFormComponent,
                    {
                        title,
                        inputs: {
                            config: addOrSubtractNewConfig(
                                this.injector,
                                employeeId,
                                type,
                            ),
                        },

                        onDismiss: () => {
                            subscriptions.forEach(x => x.unsubscribe());
                        },
                    },
                );

                subscriptions.push(
                    component.createOrUpdate.subscribe(() => {
                        this.list()!.reload();
                        this.modalService.dismiss(component);
                    }),
                );
            });
    }
}
