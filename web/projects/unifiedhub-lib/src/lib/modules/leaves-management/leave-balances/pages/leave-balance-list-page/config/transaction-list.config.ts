import {
    LeaveBalanceTransaction,
    LeaveBalanceTransactionFilterAttrs,
} from '../../../../types';
import { Injector } from '@angular/core';
import { LeaveBalancesService } from '../../../leave-balances.service';
import { p } from '../../../../../../permissions';
import { DynamicListFullConfig } from '../../../../../../features';

export const transactionListConfig = (
    injector: Injector,
    employeeId: string,
    type: string,
): DynamicListFullConfig<
    LeaveBalanceTransaction,
    LeaveBalanceTransactionFilterAttrs
> => {
    const leaveBalancesService = injector.get(LeaveBalancesService);

    return {
        isEditLinkHidden: true,
        fetcher: filter =>
            leaveBalancesService.transactions(employeeId, type, filter),
        columnConfigFn: () => [
            {
                label: 'translate_created_time',
            },
            {
                label: 'translate_days',
            },
            {
                label: 'translate_notes',
            },
            {
                label: 'translate_service_request',
                permissionId: p.servicing.requests.read,
            },
        ],
        valueConfigFn: item => [
            {
                value: item.createdTime,
                type: 'datetime',
            },
            {
                value: item.days,
            },
            {
                value: item.notes,
            },
            {
                value: item.serviceRequest
                    ? 'translate_go_to_request_details'
                    : 'translate_no_request',
                linkConfig: item.serviceRequest && {
                    value: [
                        '',
                        'servicing',
                        'requests',
                        item.serviceRequest?.id,
                    ],
                },
            },
        ],

        filter: [
            {
                id: 'from',
                type: 'datetime',
                label: 'translate_from',
            },

            {
                id: 'to',
                type: 'datetime',
                label: 'translate_to',
            },
        ],
    };
};
