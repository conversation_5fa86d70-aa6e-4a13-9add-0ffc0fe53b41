import { Injector } from '@angular/core';
import { LeaveBalancesService } from '../../../leave-balances.service';
import { DynamicNewFormConfig } from '../../../../../../features';

export const addOrSubtractNewConfig = (
    injector: Injector,
    employeeId: string,
    type: string,
): DynamicNewFormConfig<{ days: number; notes?: string }> => {
    const leaveBalancesService = injector.get(LeaveBalancesService);

    return {
        formConfig: [
            {
                type: 'group',
                config: [
                    {
                        id: 'days',
                        type: 'input',
                        label: 'translate_days_to_add_or_subtract',
                        required: true,
                        props: {
                            type: 'number',
                        },
                    },

                    {
                        id: 'notes',
                        type: 'textarea',
                        label: 'translate_notes',
                    },
                ],
            },
        ],
        creator: item =>
            leaveBalancesService.addOrSubtract(employeeId, type, item),
    };
};
