import { Routes } from '@angular/router';
import { loadTranslationsResolver } from '@ng-omar/translation';
import { p } from '../../permissions';

export const routes = [
    {
        path: '',
        children: [
            {
                path: 'balances',
                loadChildren: () =>
                    import('./leave-balances/routes').then(x => x.routes),
                resolve: { translations: loadTranslationsResolver },
                data: {
                    permissionIds: [p.leaves.read],
                },
            },

            {
                path: '',
                loadChildren: () =>
                    import('./leaves/routes').then(x => x.routes),
                resolve: { translations: loadTranslationsResolver },
                data: {
                    permissionIds: [p.leaves.read],
                },
            },
        ],
    },
] satisfies Routes;
