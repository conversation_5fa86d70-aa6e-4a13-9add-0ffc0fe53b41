import { DynamicListPageConfig, ModalService } from '../../../../../features';
import { Log, LogFilterAttrs } from '../../../core';
import { Injector } from '@angular/core';
import { LogsService } from '../../logs.service';
import { UsersService } from '../../../../users/users.service';
import { map } from 'rxjs';
import { LogTypeValueComponent } from './components/log-type-value/log-type-value.component';
import { LogTextComponent } from './components/log-text/log-text.component';
import { LogDetailComponent } from './components/log-detail/log-detail.component';

export const config = (
    injector: Injector,
): DynamicListPageConfig<Log, LogFilterAttrs> => {
    const logsService = injector.get(LogsService);

    return {
        title: 'translate_logs',
        newButtonConfig: {
            isHidden: true,
        },
        listConfig: {
            columnConfigFn: () => [
                { label: 'translate_timestamp' },

                { label: 'translate_type' },

                { label: 'translate_title' },

                { label: 'translate_description' },

                { label: 'translate_user' },
            ],

            valueConfigFn: item => [
                {
                    value: item.timestamp,
                    type: 'datetime',
                },

                {
                    type: 'component',
                    value: LogTypeValueComponent,
                    inputs: {
                        type: item.type,
                    },
                },

                {
                    direction: 'ltr',
                    type: 'component',
                    value: LogTextComponent,
                    inputs: {
                        text: item.title,
                    },
                },

                {
                    value: item.description,
                    direction: 'ltr',
                },

                {
                    value: item.user?.name,
                },
            ],

            actionsFn: item => [
                {
                    type: 'success',
                    iconClasses: 'fa fa-eye',
                    onClickFn: async () => {
                        await injector
                            .get(ModalService)
                            .show(LogDetailComponent, {
                                title: 'translate_details',
                                inputs: {
                                    log: item,
                                },
                            });
                    },
                },
            ],

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },

                {
                    id: 'from',
                    type: 'datetime',
                    label: 'translate_from',
                },

                {
                    id: 'to',
                    type: 'datetime',
                    label: 'translate_to',
                },

                {
                    id: 'userIds',
                    type: 'select',
                    label: 'translate_users',
                    bindValue: 'id',
                    bindLabel: 'name',
                    isMulti: true,
                    loaderFetcher: keyword =>
                        injector
                            .get(UsersService)
                            .simpleList({ attrs: { keyword } })
                            .pipe(map(x => x.items)),
                },
            ],
            fetcher: logsService.list.bind(logsService),

            isEditLinkHidden: true,
        },
    };
};
