import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { Log } from '../../../../../core';
import { NgClass } from '@angular/common';

@Component({
    selector: 'lib-log-type-value',
    templateUrl: './log-type-value.component.html',
    styleUrl: './log-type-value.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass],
})
export class LogTypeValueComponent {
    public readonly type = input.required<Log['type']>();
}
