import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
    Filter,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';
import { Log, LogFilterAttrs } from '../core';
import { Observable } from 'rxjs';
import { APP_CONFIG, AppConfig } from '../../../config';

@Injectable()
export class LogsService {
    public constructor(
        private readonly httpClient: HttpClient,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {}

    public list(
        filter: Filter<LogFilterAttrs>,
    ): Observable<PaginatedResult<Log>> {
        return this.httpClient.get<PaginatedResult<Log>>(
            `${this.config.apiUrl}/logs`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }
}
