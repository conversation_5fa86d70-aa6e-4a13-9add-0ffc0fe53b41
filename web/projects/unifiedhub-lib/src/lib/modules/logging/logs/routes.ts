import { Routes } from '@angular/router';
import { LogsComponent } from './logs.component';
import { LogListPageComponent } from './pages/log-list-page/log-list-page.component';
import { p } from '../../../permissions';

export const routes = [
    {
        path: '',
        component: LogsComponent,
        children: [
            {
                path: '',
                component: LogListPageComponent,
                data: {
                    title: 'translate_logs',
                    permissionIds: [p.logging.logs.read],
                },
            },
        ],
    },
] satisfies Routes;
