import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { User, UserFilterAttrs } from './core/types';
import { Observable } from 'rxjs';
import { Role } from '../roles';
import { CrudService } from '../../features';
import { APP_CONFIG, AppConfig } from '../../config';
import { Filter, mapFilterToQueryParams, PaginatedResult } from '../../common';
import { ERROR_INTERCEPTOR_STEALTH } from '../../core';

@Injectable()
export class UsersService extends CrudService<User, UserFilterAttrs> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public addRole(id: string, roleId: string): Observable<any> {
        return this.httpClient.post(
            `${this.getBaseEndpoint()}/${id}/roles/${roleId}`,
            null,
        );
    }

    public getRoles(
        id: string,
        filter: Filter<{ keyword?: string }>,
    ): Observable<PaginatedResult<Role>> {
        return this.httpClient.get<PaginatedResult<Role>>(
            `${this.getBaseEndpoint()}/${id}/roles`,
            { params: mapFilterToQueryParams(filter) },
        );
    }

    public getOtherRoles(
        id: string,
        filter: Filter<{ keyword?: string }>,
    ): Observable<PaginatedResult<Role>> {
        return this.httpClient.get<PaginatedResult<Role>>(
            `${this.getBaseEndpoint()}/${id}/roles/others`,
            { params: mapFilterToQueryParams(filter) },
        );
    }

    public removeRole(id: string, roleId: string): Observable<any> {
        return this.httpClient.delete(
            `${this.getBaseEndpoint()}/${id}/roles/${roleId}`,
        );
    }

    public downloadPhoto(id: string): Observable<Blob> {
        return this.httpClient.get(`${this.config.apiUrl}/users/${id}/photo`, {
            responseType: 'blob',
            context: new HttpContext().set(ERROR_INTERCEPTOR_STEALTH, true),
        });
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/users`;
    }
}
