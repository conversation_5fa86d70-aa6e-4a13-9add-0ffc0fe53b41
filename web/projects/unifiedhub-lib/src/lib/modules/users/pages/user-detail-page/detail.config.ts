import { User } from '../../core';
import { UsersService } from '../../users.service';
import { p } from '../../../../permissions';
import { DetailPageConfig, LinkerConfig } from '../../../../features';
import { Filter } from '../../../../common';
import { Role, RolesService } from '../../../roles';

export const config = (
    usersService: UsersService,
    rolesService: RolesService,
): DetailPageConfig<User> => {
    return {
        loader: usersService.get.bind(usersService),
        config: item => ({
            title: 'translate_users',
            subtitle: 'translate_user_details',
            editButtonConfig: {
                permissionId: p.users.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_basic_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_email_address',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.email,
                        },
                    ],
                },

                {
                    type: 'linker',
                    title: 'translate_roles_for_the_user',
                    config: {
                        linked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_role',
                                },
                            ],

                            valueConfigFn: role => [
                                {
                                    value: role.name,
                                },
                            ],
                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_name',
                                },
                            ],
                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                usersService.getRoles(item!.id, filter),
                            transfer: roles =>
                                rolesService.removeUsersFromRoles(
                                    roles.map(x => x.id),
                                    [item!.id],
                                ),
                            transferPermissionId: p.users.write,
                        },
                        unlinked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_role',
                                },
                            ],
                            valueConfigFn: role => [
                                {
                                    value: role.name,
                                },
                            ],
                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_name',
                                },
                            ],
                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                usersService.getOtherRoles(item!.id, filter),
                            transfer: roles =>
                                rolesService.addUsersToRoles(
                                    roles.map(x => x.id),
                                    [item!.id],
                                ),
                        },
                    } satisfies LinkerConfig<Role>,
                },
            ],
        }),
    };
};
