import { UsersService } from '../../users.service';
import { User, UserFilterAttrs } from '../../core';
import { inject } from '@angular/core';
import { p } from '../../../../permissions';
import { DynamicListPageConfig } from '../../../../features';

export const config = (): DynamicListPageConfig<User, UserFilterAttrs> => {
    const usersService = inject(UsersService);

    return {
        title: 'translate_users',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
                {
                    label: 'translate_email_address',
                },
                {
                    label: 'translate_department',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: { value: ['', 'users', item.id] },
                },
                {
                    value: item.email,
                },
                {
                    value: item.department?.name,
                    linkConfig: item.department && {
                        value: ['', 'departments', item.department.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.users.write,
                },
                deleteButtonConfig: {
                    permissionId: p.users.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },

                {
                    id: 'email',
                    type: 'text',
                    label: 'translate_email_address',
                },
            ],

            fetcher: usersService.list.bind(usersService),
            deleter: usersService.delete.bind(usersService),
        },
        newButtonConfig: {
            permissionId: p.users.write,
        },
    };
};
