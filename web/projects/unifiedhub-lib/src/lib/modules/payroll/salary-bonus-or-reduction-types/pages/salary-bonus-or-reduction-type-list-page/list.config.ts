import { SalaryBonusOrReductionTypesService } from '../../salary-bonus-or-reduction-types.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import {
    SalaryBonusOrReductionType,
    SalaryBonusOrReductionTypeFilterAttrs,
} from '../../../core';
import { Injector } from '@angular/core';
import { SalaryBonusOrReductionsService } from '../../../salary-bonus-or-reductions';

export const config = (
    injector: Injector,
): DynamicListPageConfig<
    SalaryBonusOrReductionType,
    SalaryBonusOrReductionTypeFilterAttrs
> => {
    const salaryBonusOrReductionTypesService = injector.get(
        SalaryBonusOrReductionTypesService,
    );

    return {
        title: 'translate_bonus_or_reduction_types',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_category',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: [
                            '',
                            'payroll',
                            'salaries',
                            'bonus-or-reduction-types',
                            item.id,
                        ],
                    },
                },

                {
                    value: item.category,
                    type: 'id_to_name',
                    idToName$: injector
                        .get(SalaryBonusOrReductionsService)
                        .listCategories(),
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.payroll.salaries.write,
                },
                deleteButtonConfig: {
                    permissionId: p.payroll.salaries.write,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },

                {
                    id: 'categories',
                    type: 'select',
                    label: 'translate_category',
                    bindLabel: 'name',
                    bindValue: 'id',
                    isMulti: true,
                    items$: injector
                        .get(SalaryBonusOrReductionsService)
                        .listCategories(),
                },
            ],
            fetcher: salaryBonusOrReductionTypesService.list.bind(
                salaryBonusOrReductionTypesService,
            ),
            deleter: salaryBonusOrReductionTypesService.delete.bind(
                salaryBonusOrReductionTypesService,
            ),
        },

        newButtonConfig: {
            permissionId: p.payroll.salaries.write,
        },
    };
};
