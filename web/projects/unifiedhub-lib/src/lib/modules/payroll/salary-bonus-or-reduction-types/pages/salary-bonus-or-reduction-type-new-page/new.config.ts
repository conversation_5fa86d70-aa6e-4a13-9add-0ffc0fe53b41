import { SalaryBonusOrReductionTypesService } from '../../salary-bonus-or-reduction-types.service';
import { SalaryBonusOrReductionType } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';
import { Injector } from '@angular/core';
import { SalaryBonusOrReductionsService } from '../../../salary-bonus-or-reductions';

export const configFn = (
    injector: Injector,
): NewPageConfigFn<SalaryBonusOrReductionType> => {
    const salaryBonusOrReductionTypesService = injector.get(
        SalaryBonusOrReductionTypesService,
    );

    return mode => ({
        title: 'translate_bonus_or_reduction_types',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_salary_bonus_or_reduction_type'
                : 'translate_update_salary_bonus_or_reduction_type_details',
        newFormConfig: {
            loader: salaryBonusOrReductionTypesService.get.bind(
                salaryBonusOrReductionTypesService,
            ),

            creator: salaryBonusOrReductionTypesService.create.bind(
                salaryBonusOrReductionTypesService,
            ),

            editor: salaryBonusOrReductionTypesService.update.bind(
                salaryBonusOrReductionTypesService,
            ),

            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'category',
                            type: 'select',
                            label: 'translate_category',
                            required: true,
                            disabled: mode === 'edit',

                            props: {
                                items$: injector
                                    .get(SalaryBonusOrReductionsService)
                                    .listCategories(),
                                bindLabel: 'name',
                                bindValue: 'id',
                                compareWith: (a, b) => a.id === b,
                            },
                        },

                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                            size: 2,
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
