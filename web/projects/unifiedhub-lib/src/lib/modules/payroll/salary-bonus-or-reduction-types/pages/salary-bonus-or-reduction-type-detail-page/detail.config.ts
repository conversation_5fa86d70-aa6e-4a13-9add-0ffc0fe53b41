import { SalaryBonusOrReductionTypesService } from '../../salary-bonus-or-reduction-types.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { SalaryBonusOrReductionType } from '../../../core';
import { Injector } from '@angular/core';
import { SalaryBonusOrReductionsService } from '../../../salary-bonus-or-reductions';

export const config = (
    injector: Injector,
): DetailPageConfig<SalaryBonusOrReductionType> => {
    const salaryBonusOrReductionTypesService = injector.get(
        SalaryBonusOrReductionTypesService,
    );

    return {
        loader: salaryBonusOrReductionTypesService.get.bind(
            salaryBonusOrReductionTypesService,
        ),
        config: item => ({
            title: 'translate_bonus_or_reduction_types',
            subtitle: 'translate_salary_bonus_or_reduction_type_details',
            editButtonConfig: {
                permissionId: p.payroll.salaries.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_salary_bonus_or_reduction_type_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_category',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.category,
                            type: 'id_to_name',
                            idToName$: injector
                                .get(SalaryBonusOrReductionsService)
                                .listCategories(),
                        },
                    ],
                },
            ],
        }),
    };
};
