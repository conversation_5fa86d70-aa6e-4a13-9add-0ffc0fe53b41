import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';
import { SalaryBonusOrReductionsService } from '../../../salary-bonus-or-reductions';

@Component({
    selector: 'lib-salary-bonus-or-reduction-type-detail-page',
    templateUrl: './salary-bonus-or-reduction-type-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
    providers: [SalaryBonusOrReductionsService],
})
export class SalaryBonusOrReductionTypeDetailComponent {
    protected readonly config = config(inject(Injector));
}
