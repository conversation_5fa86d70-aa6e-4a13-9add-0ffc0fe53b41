import { SalaryDeductionTypesService } from '../../salary-deduction-types.service';
import { SalaryDeductionType } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    salaryDeductionTypesService: SalaryDeductionTypesService,
): NewPageConfigFn<SalaryDeductionType> => {
    return mode => ({
        title: 'translate_salary_deduction_types',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_salary_deduction_type'
                : 'translate_update_salary_deduction_type_details',
        newFormConfig: {
            loader: salaryDeductionTypesService.get.bind(
                salaryDeductionTypesService,
            ),

            creator: salaryDeductionTypesService.create.bind(
                salaryDeductionTypesService,
            ),

            editor: salaryDeductionTypesService.update.bind(
                salaryDeductionTypesService,
            ),

            formConfig: [
                {
                    id: 'name',
                    type: 'multilingualTextInput',
                    label: 'translate_name',
                    required: true,
                },

                {
                    id: 'displayMode',
                    type: 'select',
                    label: 'translate_deduction_display_mode',
                    required: true,
                    props: {
                        items$: salaryDeductionTypesService.listDisplayModes(),
                        bindLabel: 'name',
                        bindValue: 'id',
                    },
                },
            ],
        },
    });
};
