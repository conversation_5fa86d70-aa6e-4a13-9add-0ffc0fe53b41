import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SalaryDeductionTypesService } from './salary-deduction-types.service';

@Component({
    selector: 'lib-salary-deduction-types',
    template: '<router-outlet/>',
    standalone: true,
    providers: [SalaryDeductionTypesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SalaryDeductionTypesComponent {}
