import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { SalaryDeductionTypesService } from '../../salary-deduction-types.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-salary-deduction-type-detail-page',
    templateUrl: './salary-deduction-type-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class SalaryDeductionTypeDetailComponent {
    protected readonly config = config(inject(SalaryDeductionTypesService));
}
