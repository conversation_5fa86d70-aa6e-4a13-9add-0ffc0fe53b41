import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { SalaryDeductionType, SalaryFilterAttrs } from '../core';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import { Item } from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';

@Injectable()
export class SalaryDeductionTypesService extends CrudService<
    SalaryDeductionType,
    SalaryFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public listDisplayModes(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.getBaseEndpoint()}/display-modes`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/payroll/salaries/deduction-types`;
    }
}
