import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { EmployeesService } from '../../../../employment/employees/employees.service';

@Component({
    selector: 'lib-bonus-or-reduction-list-page',
    templateUrl: './salary-bonus-or-reduction-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
    providers: [EmployeesService],
})
export class SalaryBonusOrReductionListPageComponent {
    protected config = config(inject(Injector));
}
