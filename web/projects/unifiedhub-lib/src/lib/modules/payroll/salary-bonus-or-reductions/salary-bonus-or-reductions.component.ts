import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SalaryBonusOrReductionsService } from './salary-bonus-or-reductions.service';

@Component({
    selector: 'lib-bonus-or-reductions',
    template: '<router-outlet/>',
    standalone: true,
    providers: [SalaryBonusOrReductionsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SalaryBonusOrReductionsComponent {}
