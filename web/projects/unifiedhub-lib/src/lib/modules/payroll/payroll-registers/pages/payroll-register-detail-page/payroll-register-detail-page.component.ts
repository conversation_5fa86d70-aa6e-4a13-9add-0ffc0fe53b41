import {
    ChangeDetectionStrategy,
    Component,
    inject,
    signal,
} from '@angular/core';
import { PayrollRegistersService } from '../../payroll-registers.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PayrollRegister } from '../../../core';
import { InvokeFlowActionButtonComponent } from '../../../../../features/flows';
import { finalize } from 'rxjs';
import { saveBlobToFileUtil } from '../../../../../common';
import { LoadingButtonComponent } from '../../../../../ui';

@Component({
    selector: 'lib-payroll-register-detail-page',
    templateUrl: './payroll-register-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicDetailPageComponent,
        FormsModule,
        RouterLink,
        TranslateModule,
        InvokeFlowActionButtonComponent,
        LoadingButtonComponent,
    ],
})
export class PayrollRegisterDetailPageComponent {
    protected readonly item = signal<PayrollRegister | undefined>(undefined);
    protected readonly isDownloading = signal<boolean>(false);

    protected readonly config = config(inject(PayrollRegistersService));

    public constructor(
        private readonly payrollRegistersService: PayrollRegistersService,
    ) {}

    public download(): void {
        if (!this.item() || this.isDownloading()) return;
        this.isDownloading.set(true);
        this.payrollRegistersService
            .download(this.item()!.id)
            .pipe(finalize(() => this.isDownloading.set(false)))
            .subscribe(blob => {
                saveBlobToFileUtil(
                    blob,
                    `payroll_register_${this.item()!.year}_${this.item()!.month}.pdf`,
                );
            });
    }
}
