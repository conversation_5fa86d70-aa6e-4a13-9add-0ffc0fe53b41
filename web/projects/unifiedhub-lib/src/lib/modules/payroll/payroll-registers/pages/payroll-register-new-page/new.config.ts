import { PayrollRegister } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';
import { PayrollRegistersService } from '../../payroll-registers.service';
import { map, of, Subject, switchMap } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { Alert } from '../../../../../ui/components/alert/types';

export const configFn = (
    payrollRegistersService: PayrollRegistersService,
): NewPageConfigFn<PayrollRegister> => {
    const subject = new Subject<{ year: number; month: number }>();

    return () => ({
        title: 'translate_payroll_registers',
        subtitle: 'translate_add_new_payroll_register',
        alerts$: subject.asObservable().pipe(
            debounceTime(200),
            switchMap(data => {
                const { year, month } = data;
                if (!year || !month || year < 0 || month < 1 || 12 < month) {
                    return of([]);
                }

                return payrollRegistersService
                    .getByYearAndMonth(year, month)
                    .pipe(
                        map(register => {
                            return register
                                ? register.actionAbility.canDelete
                                    ? [
                                          {
                                              type: 'warning',
                                              title: 'translate_a_payroll_register_exists',
                                              description:
                                                  'translate_there_is_currently_a_payroll_register_with_the_same_year_and_month_if_you_proceed_the_existing_register_will_be_overwritten',
                                          } satisfies Alert,
                                      ]
                                    : [
                                          {
                                              type: 'danger',
                                              title: 'translate_a_payroll_register_exists',
                                              description:
                                                  'translate_there_is_an_already_approved_register_with_the_same_year_and_month_you_cannot_proceed_with_the_following_year_and_month',
                                          } satisfies Alert,
                                      ]
                                : [];
                        }),
                    );
            }),
        ),
        newFormConfig: {
            loader: payrollRegistersService.get.bind(payrollRegistersService),
            creator: payrollRegistersService.create.bind(
                payrollRegistersService,
            ),
            onValuesChange: group => {
                const year = group.controls['year'].value;
                const month = group.controls['month'].value;
                subject.next({ year, month });
            },
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'year',
                            type: 'input',
                            label: 'translate_year',
                            required: true,
                            props: {
                                type: 'number',
                            },
                        },

                        {
                            id: 'month',
                            type: 'input',
                            label: 'translate_month',
                            required: true,
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },
            ],
        },
    });
};
