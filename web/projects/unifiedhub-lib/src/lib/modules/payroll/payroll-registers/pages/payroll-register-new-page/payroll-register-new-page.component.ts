import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { PayrollRegistersService } from '../../payroll-registers.service';

@Component({
    selector: 'lib-payroll-register-new-page',
    templateUrl: './payroll-register-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent, TranslateModule],
})
export class PayrollRegisterNewPageComponent {
    protected readonly configFn = configFn(inject(PayrollRegistersService));
}
