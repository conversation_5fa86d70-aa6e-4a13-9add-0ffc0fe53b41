import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    input,
} from '@angular/core';
import { config } from './list.config';
import { SalaryCertificate, SalaryCertificateFilterAttrs } from '../../../core';
import { Observable } from 'rxjs';
import { DynamicListFullComponent } from '../../../../../features';
import { Filter, PaginatedResult } from '../../../../../common';
import { CitiesService } from '../../../../foundation/cities/cities.service';

@Component({
    selector: 'lib-salary-certificate-list',
    templateUrl: './salary-certificate-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListFullComponent],
    providers: [CitiesService],
})
export class SalaryCertificateListComponent {
    public readonly filter = input<SalaryCertificateFilterAttrs>();
    public readonly hiddenColumns = input<{
        employee?: boolean;
    }>();
    public readonly hiddenFilters = input<{
        employee?: boolean;
    }>();
    public readonly fetcher =
        input<
            (
                filter: Filter<SalaryCertificateFilterAttrs>,
            ) => Observable<PaginatedResult<SalaryCertificate>>
        >();

    protected readonly config = computed(() => {
        return config(
            this.injector,
            this.filter(),
            this.fetcher(),
            this.hiddenColumns(),
            this.hiddenFilters(),
        );
    });

    public constructor(private readonly injector: Injector) {}
}
