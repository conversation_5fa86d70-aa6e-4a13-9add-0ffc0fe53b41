import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SalariesService } from './salaries.service';
import { getEmployeeListServices } from '../../employment/core';
import { SalaryAllowanceTypesService } from '../salary-allowance-types';
import { SalaryAllowancesService } from './salary-allowances.service';
import { SalaryDeductionsService } from './salary-deductions.service';
import { SalaryDeductionTypesService } from '../salary-deduction-types';
import { EmployeesService } from '../../employment/employees/employees.service';
import { RouterOutlet } from '@angular/router';
import { SalaryBonusOrReductionsService } from '../salary-bonus-or-reductions';

@Component({
    selector: 'lib-salaries',
    template: '<router-outlet/>',
    standalone: true,
    imports: [RouterOutlet],
    providers: [
        SalariesService,
        SalaryAllowancesService,
        SalaryDeductionsService,
        SalaryBonusOrReductionsService,
        SalaryAllowanceTypesService,
        SalaryDeductionTypesService,
        EmployeesService,
        ...getEmployeeListServices(),
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SalariesComponent {}
