import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import {
    Currency,
    Salary,
    SalaryChangeTransaction,
    SalaryChangeTransactionFilterAttrs,
    SalaryFilterAttrs,
} from '../core';
import { Observable } from 'rxjs';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import { CACHE_INTERCEPTOR } from '../../../core';
import {
    Filter,
    Item,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';

@Injectable()
export class SalariesService extends CrudService<Salary, SalaryFilterAttrs> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public listChangeTransaction(
        id: string,
        filter: Filter<SalaryChangeTransactionFilterAttrs>,
    ): Observable<PaginatedResult<SalaryChangeTransaction>> {
        return this.httpClient.get<PaginatedResult<SalaryChangeTransaction>>(
            `${this.getBaseEndpoint()}/${id}/change-transactions`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }

    public listCurrencies(): Observable<Currency[]> {
        return this.httpClient.get<Currency[]>(
            `${this.config.apiUrl}/payroll/misc/currencies`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    public listChangeTransactionTypes(): Observable<Item[]> {
        return this.httpClient.get<Currency[]>(
            `${this.config.apiUrl}/payroll/misc/change-transaction-types`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    public listChangeTransactionEntityTypes(): Observable<Item[]> {
        return this.httpClient.get<Currency[]>(
            `${this.config.apiUrl}/payroll/misc/change-transaction-entity-types`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/payroll/salaries`;
    }
}
