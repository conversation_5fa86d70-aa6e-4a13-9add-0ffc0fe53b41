import { Salary, SalaryAllowance } from '../../../../core';
import { map } from 'rxjs';
import { SectionConfig } from '../../../../../../features';
import { SalaryAllowancesService } from '../../../salary-allowances.service';
import { SalaryAllowanceTypesService } from '../../../../salary-allowance-types';
import { formatDateOnly } from '../../../../../../common';
import { p } from '../../../../../../permissions';

export const salaryAllowanceSection = (
    item: Salary | null,
    salaryAllowancesService: SalaryAllowancesService,
    salaryAllowanceTypesService: SalaryAllowanceTypesService,
): SectionConfig<Salary> => {
    return {
        type: 'list_with_create',
        title: 'translate_salary_allowances',
        reloadOnListChange: true,
        config: {
            list: {
                type: 'simple',
                title: 'translate_salary_allowances',
                newButtonConfig: {
                    permissionId: p.payroll.salaries.write,
                },
                fetcher: () => salaryAllowancesService.list(item!.id),
                deleter: id => salaryAllowancesService.delete(id),
                rowConfigFn: tmp => {
                    const allowance = tmp as SalaryAllowance;
                    return {
                        classes: allowance.isExpired
                            ? 'diagonal-stripes'
                            : undefined,
                        editButtonConfig: {
                            permissionId: p.payroll.salaries.write,
                        },
                        deleteButtonConfig: {
                            permissionId: p.payroll.salaries.write,
                        },
                    };
                },
                columnConfigFn: () => [
                    {
                        label: 'translate_allowance_type',
                    },

                    {
                        label: 'translate_amount',
                    },

                    {
                        label: 'translate_from',
                    },

                    {
                        label: 'translate_to',
                    },

                    {
                        label: 'translate_notes',
                    },
                ],
                valueConfigFn: listItem => {
                    const allowance = listItem as SalaryAllowance;
                    return [
                        {
                            value: allowance.type.name,
                        },

                        allowance.amount
                            ? {
                                  value: allowance.amount,
                                  type: 'currency',
                              }
                            : {
                                  value: 'translate_allowance_no_payment',
                              },

                        {
                            value: allowance.from,
                            type: 'date',
                        },

                        {
                            value: allowance.to,
                            type: 'date',
                        },

                        {
                            value: allowance.notes,
                        },
                    ];
                },
            },
            form: {
                newTitle: 'translate_create_new_salary_allowance',
                updateTitle: 'translate_update_salary_allowance_details',
                fetcher: id => salaryAllowancesService.get(id),
                creator: allowance =>
                    salaryAllowancesService.create(
                        item!.id,
                        allowance as SalaryAllowance,
                    ),
                updater: (id, allowance) =>
                    salaryAllowancesService.update(
                        id,
                        allowance as SalaryAllowance,
                    ),
                configFn: () => [
                    {
                        type: 'group',
                        config: [
                            {
                                id: 'type',
                                type: 'select',
                                label: 'translate_allowance_type',
                                required: true,
                                props: {
                                    items$: salaryAllowanceTypesService
                                        .list({ pageSize: -1 })
                                        .pipe(map(x => x.items)),
                                    bindLabel: 'name',
                                },
                            },

                            {
                                id: 'amount',
                                type: 'input',
                                label: 'translate_amount',
                                props: {
                                    type: 'number',
                                },
                            },
                        ],
                    },

                    {
                        type: 'group',
                        config: [
                            {
                                id: 'from',
                                type: 'datetime',
                                label: 'translate_from',
                                required: true,
                                defaultValue: formatDateOnly(new Date()),
                                props: {
                                    dateOnly: true,
                                },
                            },

                            {
                                id: 'to',
                                type: 'datetime',
                                label: 'translate_to',
                                props: {
                                    dateOnly: true,
                                },
                            },
                        ],
                    },

                    {
                        id: 'notes',
                        type: 'textarea',
                        label: 'translate_notes',
                    },
                ],
            },
        },
    };
};
