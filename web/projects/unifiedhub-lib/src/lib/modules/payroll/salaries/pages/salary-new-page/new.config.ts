import { Salary } from '../../../core';
import { SalariesService } from '../../salaries.service';
import { NewPageConfigFn } from '../../../../../features';
import { map } from 'rxjs';
import { EmployeesService } from '../../../../employment/employees/employees.service';

export const configFn: (
    salariesService: SalariesService,
    employeesService: EmployeesService,
) => NewPageConfigFn<Salary> = (
    salariesService: SalariesService,
    employeesService: EmployeesService,
) => {
    return mode => ({
        title: 'translate_salaries',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_salary'
                : 'translate_update_salary_details',
        newFormConfig: {
            loader: salariesService.get.bind(salariesService),
            creator: salariesService.create.bind(salariesService),
            editor: salariesService.update.bind(salariesService),

            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'employee',
                            type: 'select',
                            label: 'translate_employee_name',
                            required: true,
                            disabled: mode === 'edit',
                            props: {
                                bindLabel: 'name',
                                compareWith: (a, b) => a.id == b.id,
                                loaderFetcher: (keyword: string) =>
                                    employeesService
                                        .list({
                                            attrs: {
                                                keyword,
                                                states: ['active'],
                                            },
                                            pageNumber: 0,
                                            pageSize: -1,
                                        })
                                        .pipe(map(x => x.items)),
                            },
                        },

                        {
                            id: 'amount',
                            type: 'input',
                            label: 'translate_base_salary',
                            required: true,
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'pensionDeductionAmount',
                            type: 'input',
                            label: 'translate_pension_deduction_amount',
                            props: {
                                type: 'number',
                            },
                        },

                        {
                            id: 'currency',
                            type: 'select',
                            label: 'translate_currency',
                            required: true,
                            props: {
                                items$: salariesService.listCurrencies(),
                                bindLabel: 'name',
                            },
                        },

                        {
                            id: 'closureDate',
                            label: 'translate_financial_record_closure_date',
                            type: 'datetime',
                            props: {
                                enableTime: false,
                            },
                        },
                    ],
                },
            ],
        },
    });
};
