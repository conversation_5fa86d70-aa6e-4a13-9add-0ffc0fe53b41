import { Salary } from '../../../../core';
import { SalariesService } from '../../../salaries.service';
import { p } from '../../../../../../permissions';
import { SalaryAllowanceTypesService } from '../../../../salary-allowance-types';
import { DetailPageConfig } from '../../../../../../features';
import { Injector } from '@angular/core';
import { SalaryAllowancesService } from '../../../salary-allowances.service';
import { basicInfoSection } from './basic-info.config';
import { salaryAllowanceSection } from './salary-allowance.config';
import { salaryBonusOrReductionsSection } from './salary-bonus-or-reductions.config';
import { salaryDeductionSection } from './salary-deduction.config';
import { SalaryDeductionsService } from '../../../salary-deductions.service';
import { SalaryDeductionTypesService } from '../../../../salary-deduction-types';
import { salaryChangeTransactionsSection } from './salary-change-transactions.config';

export const config = (injector: Injector): DetailPageConfig<Salary> => {
    const salariesService = injector.get(SalariesService);

    return {
        loader: salariesService.get.bind(salariesService),
        config: (item, itemId) => ({
            title: 'translate_salaries',
            subtitle: 'translate_salary_details',
            editButtonConfig: {
                permissionId: p.payroll.salaries.write,
            },
            sectionConfigs: [
                basicInfoSection(item),

                salaryAllowanceSection(
                    item,
                    injector.get(SalaryAllowancesService),
                    injector.get(SalaryAllowanceTypesService),
                ),

                salaryDeductionSection(
                    item,
                    injector.get(SalaryDeductionsService),
                    injector.get(SalaryDeductionTypesService),
                ),

                salaryBonusOrReductionsSection(item, injector),

                salaryChangeTransactionsSection(itemId, injector),
            ],
        }),
    };
};
