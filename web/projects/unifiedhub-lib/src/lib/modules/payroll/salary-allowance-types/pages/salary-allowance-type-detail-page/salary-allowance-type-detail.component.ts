import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { SalaryAllowanceTypesService } from '../../salary-allowance-types.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-salary-allowance-type-detail-page',
    templateUrl: './salary-allowance-type-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class SalaryAllowanceTypeDetailComponent {
    protected readonly config = config(inject(SalaryAllowanceTypesService));
}
