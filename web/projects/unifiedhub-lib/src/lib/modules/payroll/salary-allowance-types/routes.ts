import { Routes } from '@angular/router';
import { SalaryAllowanceTypesComponent } from './salary-allowance-types.component';
import { SalaryAllowanceTypeListPageComponent } from './pages/salary-allowance-type-list-page/salary-allowance-type-list-page.component';
import { SalaryAllowanceTypeNewPageComponent } from './pages/salary-allowance-type-new-page/salary-allowance-type-new-page.component';
import { SalaryAllowanceTypeDetailComponent } from './pages/salary-allowance-type-detail-page/salary-allowance-type-detail.component';
import { p } from '../../../permissions';

export const routes = [
    {
        path: '',
        component: SalaryAllowanceTypesComponent,
        children: [
            {
                path: '',
                component: SalaryAllowanceTypeListPageComponent,
                data: {
                    title: 'translate_salary_allowance_types',
                },
            },

            {
                path: 'new',
                component: SalaryAllowanceTypeNewPageComponent,
                data: {
                    title: 'translate_add_new_salary_allowance_type',
                    permissionIds: [p.payroll.salaries.write],
                },
            },

            {
                path: 'edit/:id',
                component: SalaryAllowanceTypeNewPageComponent,
                data: {
                    title: 'translate_update_salary_allowance_type_details',
                    permissionIds: [p.payroll.salaries.write],
                },
            },

            {
                path: ':id',
                component: SalaryAllowanceTypeDetailComponent,
                data: {
                    title: 'translate_salary_allowance_type_details',
                },
            },
        ],
    },
] satisfies Routes;
