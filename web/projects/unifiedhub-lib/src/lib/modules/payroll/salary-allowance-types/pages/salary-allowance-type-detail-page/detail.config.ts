import { SalaryAllowanceTypesService } from '../../salary-allowance-types.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { SalaryAllowanceType } from '../../../core';

export const config = (
    salaryAllowanceTypesService: SalaryAllowanceTypesService,
): DetailPageConfig<SalaryAllowanceType> => {
    return {
        loader: salaryAllowanceTypesService.get.bind(
            salaryAllowanceTypesService,
        ),
        config: item => ({
            title: 'translate_salary_allowance_types',
            subtitle: 'translate_salary_allowance_type_details',
            editButtonConfig: {
                permissionId: p.payroll.salaries.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_salary_allowance_type_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_allowance_type',
                        },

                        {
                            label: 'translate_allowance_subtype',
                        },

                        {
                            label: 'translate_amount',
                        },

                        {
                            label: 'translate_linked_salaries',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.type,
                            type: 'id_to_name',
                            idToName$: salaryAllowanceTypesService.listTypes(),
                        },

                        {
                            value: item?.subtype,
                            type: 'id_to_name',
                            idToName$:
                                salaryAllowanceTypesService.listSubtypes(),
                        },

                        {
                            value: item?.amount,
                            type: 'number',
                        },

                        {
                            value: item?.allowanceCount,
                            type: 'number',
                        },
                    ],
                },
            ],
        }),
    };
};
