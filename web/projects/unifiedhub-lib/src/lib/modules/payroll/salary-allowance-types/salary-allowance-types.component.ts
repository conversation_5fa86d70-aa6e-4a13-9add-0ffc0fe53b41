import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SalaryAllowanceTypesService } from './salary-allowance-types.service';

@Component({
    selector: 'lib-salary-allowance-types',
    template: '<router-outlet/>',
    standalone: true,
    providers: [SalaryAllowanceTypesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SalaryAllowanceTypesComponent {}
