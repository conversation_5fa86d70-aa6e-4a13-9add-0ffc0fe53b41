import { SalaryAllowanceType } from './salary-allowance-type.type';
import { FlowTransaction } from '../../../../features/flows/types';

export type PayrollRegister = {
    id: string;
    month: number;
    year: number;
    flowState: string;
    previousRegister?: PayrollRegister;
    nextRegister?: PayrollRegister;
    amount: number;
    baseAmount: number;
    totalAmount: number;
    totalAllowanceAmount: number;
    totalDeductionAmount: number;
    totalBonusAmount: number;
    totalReductionAmount: number;
    pensionDeductionAmount: number;
    actionAbility: {
        canDelete: boolean;
    };
    separateDisplayModelAllowanceTypes: {
        type: SalaryAllowanceType;
        total: number;
    }[];
    flowTransitions: FlowTransaction[];
};
