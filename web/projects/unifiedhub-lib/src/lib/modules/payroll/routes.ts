import { Routes } from '@angular/router';
import { p } from '../../permissions';

export const routes = [
    {
        path: 'salaries/allowance-types',
        loadChildren: () =>
            import('./salary-allowance-types/routes').then(x => x.routes),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'salaries/deduction-types',
        loadChildren: () =>
            import('./salary-deduction-types/routes').then(x => x.routes),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'salaries/bonus-or-reductions',
        loadChildren: () =>
            import('./salary-bonus-or-reductions/routes').then(x => x.routes),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'salaries/bonus-or-reduction-types',
        loadChildren: () =>
            import('./salary-bonus-or-reduction-types/routes').then(
                x => x.routes,
            ),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'salaries/certificate-requesting-entities',
        loadChildren: () =>
            import('./salary-certificate-requesting-entities/routes').then(
                x => x.routes,
            ),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'salaries',
        loadChildren: () => import('./salaries/routes').then(x => x.routes),
        data: {
            permissionIds: [p.payroll.salaries.read],
        },
    },

    {
        path: 'registers',
        loadChildren: () =>
            import('./payroll-registers/routes').then(x => x.routes),
        data: {
            permissionIds: [p.payroll.registers.read],
        },
    },
] satisfies Routes;
