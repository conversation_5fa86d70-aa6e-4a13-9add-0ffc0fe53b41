import { SalaryCertificateRequestingEntitiesService } from '../../salary-certificate-requesting-entities.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { SalaryCertificateRequestingEntity } from '../../../core';
import { Injector } from '@angular/core';

export const config = (
    injector: Injector,
): DetailPageConfig<SalaryCertificateRequestingEntity> => {
    const salaryCertificateRequestingEntitiesService = injector.get(
        SalaryCertificateRequestingEntitiesService,
    );

    return {
        loader: salaryCertificateRequestingEntitiesService.get.bind(
            salaryCertificateRequestingEntitiesService,
        ),
        config: item => ({
            title: 'translate_certificate_requesting_entities',
            subtitle: 'translate_salary_certificate_requesting_entity_details',
            editButtonConfig: {
                permissionId: p.payroll.salaries.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_salary_certificate_requesting_entity_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },
                    ],
                },
            ],
        }),
    };
};
