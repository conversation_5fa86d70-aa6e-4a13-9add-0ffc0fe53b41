import { SalaryCertificateRequestingEntitiesService } from '../../salary-certificate-requesting-entities.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { SalaryCertificateRequestingEntity } from '../../../core';
import { Injector } from '@angular/core';

export const config = (
    injector: Injector,
): DynamicListPageConfig<
    SalaryCertificateRequestingEntity,
    { keyword?: string }
> => {
    const salaryCertificateRequestingEntitiesService = injector.get(
        SalaryCertificateRequestingEntitiesService,
    );

    return {
        title: 'translate_certificate_requesting_entities',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: [
                            '',
                            'payroll',
                            'salaries',
                            'certificate-requesting-entities',
                            item.id,
                        ],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.payroll.salaries.write,
                },
                deleteButtonConfig: {
                    permissionId: p.payroll.salaries.write,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: salaryCertificateRequestingEntitiesService.list.bind(
                salaryCertificateRequestingEntitiesService,
            ),
            deleter: salaryCertificateRequestingEntitiesService.delete.bind(
                salaryCertificateRequestingEntitiesService,
            ),
        },

        newButtonConfig: {
            permissionId: p.payroll.salaries.write,
        },
    };
};
