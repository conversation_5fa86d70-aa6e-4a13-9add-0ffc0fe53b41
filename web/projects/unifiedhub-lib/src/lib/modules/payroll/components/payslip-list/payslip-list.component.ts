import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    input,
} from '@angular/core';
import { Filter, PaginatedResult } from '../../../../common';
import { Observable } from 'rxjs';
import { PayrollRegister, Payslip, PayslipFilterAttrs } from '../../core';
import { PayslipsService } from './payslips.service';
import { config } from './list.config';
import { DynamicListFullComponent } from '../../../../features';
import { EmployeesService } from '../../../employment/employees/employees.service';
import { SalaryBonusOrReductionsService } from '../../salary-bonus-or-reductions';

@Component({
    selector: 'lib-payslip-list',
    templateUrl: 'payslip-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        PayslipsService,
        EmployeesService,
        SalaryBonusOrReductionsService,
    ],
    imports: [DynamicListFullComponent],
})
export class PayslipListComponent {
    public readonly filter = input<PayslipFilterAttrs>();
    public readonly hiddenColumns = input<{
        employee?: boolean;
        month?: boolean;
        year?: boolean;
    }>();
    public readonly hiddenFilters = input<{
        employee?: boolean;
        month?: boolean;
        year?: boolean;
    }>();
    public readonly fetcher =
        input<
            (
                filter: Filter<PayslipFilterAttrs>,
            ) => Observable<PaginatedResult<Payslip>>
        >();
    public readonly register = input<PayrollRegister>();

    protected readonly config = computed(() => {
        return config(
            this.injector,
            this.filter(),
            this.fetcher(),
            this.hiddenColumns(),
            this.hiddenFilters(),
            this.register(),
        );
    });

    public constructor(private readonly injector: Injector) {}
}
