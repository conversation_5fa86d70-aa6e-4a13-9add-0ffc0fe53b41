import { Injector } from '@angular/core';
import { map, Observable } from 'rxjs';
import { PayrollRegister, Payslip, PayslipFilterAttrs } from '../../core';
import { Filter, PaginatedResult } from '../../../../common';
import {
    CurrencyValueConfig,
    DynamicListFullConfig,
    ModalService,
    MultiLevelValueConfig,
    SelectFilterConfig,
} from '../../../../features';
import { PayslipsService } from './payslips.service';
import { formatEmployeeName } from '../../../employment/core';
import { EmployeesService } from '../../../employment/employees/employees.service';
import { PayslipDetailComponent } from './components/payslip-detail/payslip-detail.component';

export const config = (
    injector: Injector,
    filter?: PayslipFilterAttrs,
    fetcher?: (
        filter: Filter<PayslipFilterAttrs>,
    ) => Observable<PaginatedResult<Payslip>>,
    hiddenColumns?: {
        employee?: boolean;
        month?: boolean;
        year?: boolean;
    },
    hiddenFilters?: {
        employee?: boolean;
        month?: boolean;
        year?: boolean;
    },
    register?: PayrollRegister,
): DynamicListFullConfig<Payslip, PayslipFilterAttrs> => {
    const payslipsService = injector.get(PayslipsService);

    return {
        defaultFilter: filter,
        isEditLinkHidden: true,
        columnConfigFn: items => [
            ...(hiddenColumns?.employee
                ? []
                : [
                      {
                          label: 'translate_employee',
                      },
                  ]),

            ...(hiddenColumns?.year
                ? []
                : [
                      {
                          label: 'translate_year',
                      },
                  ]),

            ...(hiddenColumns?.month
                ? []
                : [
                      {
                          label: 'translate_month',
                      },
                  ]),

            {
                label: 'translate_base_amount',
            },

            ...(register
                ? register.separateDisplayModelAllowanceTypes.map(x => ({
                      label: x.type.name,
                  }))
                : items[0].allowances.map(x => ({
                      label: x.type.name,
                  }))),

            {
                label: 'translate_total_allowance_amount',
            },

            {
                label: 'translate_total_deduction_amount',
            },

            {
                label: 'translate_total_bonus_amount',
            },

            {
                label: 'translate_total_reduction_amount',
            },

            {
                label: 'translate_pension_deduction_amount',
            },

            {
                label: 'translate_total_amount',
            },
        ],

        valueConfigFn: item => [
            ...(hiddenColumns?.employee
                ? []
                : [
                      {
                          type: 'multi_line',
                          noTextWrap: true,
                          value: {
                              line1: [
                                  {
                                      value: formatEmployeeName(
                                          item.salary.employee,
                                      ),
                                  },
                              ],
                              line2: item.hasChanged
                                  ? [
                                        {
                                            value: 'translate_has_changed_since_last_payroll_register',
                                            classes: 'text-red-500',
                                        },
                                    ]
                                  : undefined,
                          },
                      } satisfies MultiLevelValueConfig,
                  ]),

            ...(hiddenColumns?.year
                ? []
                : [
                      {
                          value: item.register.year,
                      },
                  ]),

            ...(hiddenColumns?.month
                ? []
                : [
                      {
                          value: item.register.month,
                      },
                  ]),

            {
                value: item.baseAmount,
                type: 'currency',
            },

            ...((register
                ? register.separateDisplayModelAllowanceTypes.map(x => ({
                      value:
                          item.allowances.find(y => y.type.id === x.type.id)
                              ?.amount ?? 0,
                      type: 'currency',
                  }))
                : item.allowances.map(x => ({
                      value: x.amount ?? 0,
                      type: 'currency',
                  }))) as CurrencyValueConfig[]),

            {
                value: item.totalAllowanceAmount,
                type: 'currency',
            },

            {
                value: item.totalDeductionAmount,
                type: 'currency',
            },

            {
                value: item.totalBonusAmount,
                type: 'currency',
            },

            {
                value: item.totalReductionAmount,
                type: 'currency',
            },

            {
                value: item.pensionDeductionAmount,
                type: 'currency',
            },

            {
                value: item.totalAmount,
                type: 'currency',
            },
        ],

        actionsFn: item => {
            return [
                {
                    type: 'success',
                    iconClasses: 'fa fa-eye',
                    onClickFn: async () => {
                        await injector
                            .get(ModalService)
                            .show(PayslipDetailComponent, {
                                title: 'translate_details',
                                injector,
                                inputs: {
                                    payslipId: item.id,
                                },
                            });
                    },
                },

                {
                    type: 'info',
                    iconClasses: 'fa fa-money-bill',
                    linkConfig: {
                        value: ['', 'payroll', 'salaries', item.salary.id],
                        shouldOpenInNewTab: true,
                    },
                },
            ];
        },

        rowConfigFn: item => {
            return {
                classes: item.hasChanged
                    ? 'hover:bg-amber-200 bg-amber-100'
                    : undefined,
            };
        },

        filter: [
            ...(hiddenFilters?.employee
                ? []
                : [
                      {
                          id: 'employeeIds',
                          type: 'select',
                          label: 'translate_employees',
                          bindValue: 'id',
                          bindLabel: 'name',
                          isMulti: true,
                          loaderFetcher: keyword =>
                              injector
                                  .get(EmployeesService)
                                  .simpleList({ attrs: { keyword } })
                                  .pipe(
                                      map(data =>
                                          data.items.map(x => ({
                                              ...x,
                                              name: formatEmployeeName(x),
                                          })),
                                      ),
                                  ),
                      } satisfies SelectFilterConfig,
                  ]),

            ...(hiddenFilters?.year
                ? []
                : [
                      {
                          id: 'years',
                          type: 'select',
                          label: 'translate_years',
                          items: Array.from({
                              length: 50,
                          })
                              .map(
                                  (_, i) =>
                                      i + new Date().getFullYear() - 50 + 1,
                              )
                              .reverse(),
                          isMulti: true,
                      } satisfies SelectFilterConfig,
                  ]),

            ...(hiddenFilters?.month
                ? []
                : [
                      {
                          id: 'months',
                          type: 'select',
                          label: 'translate_months',
                          items: Array.from({ length: 12 }).map(
                              (_, i) => i + 1,
                          ),
                          isMulti: true,
                      } satisfies SelectFilterConfig,
                  ]),

            {
                id: 'hasChanged',
                type: 'checkbox',
                label: 'translate_only_show_payslips_that_have_been_changed_since_last_register',
            },
        ],

        fetcher: fetcher ?? payslipsService.list.bind(payslipsService),
    };
};
