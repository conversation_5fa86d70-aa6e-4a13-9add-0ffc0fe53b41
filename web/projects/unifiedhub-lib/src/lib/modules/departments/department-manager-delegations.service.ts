import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
    DepartmentManagerDelegation,
    DepartmentManagerDelegationFilterAttrs,
} from './core';
import { APP_CONFIG, AppConfig } from '../../config';
import { CrudService } from '../../features';
import { Filter, mapFilterToQueryParams, PaginatedResult } from '../../common';
import { Observable } from 'rxjs';
import { Employee } from '../employment/core';

@Injectable()
export class DepartmentManagerDelegationsService extends CrudService<
    DepartmentManagerDelegation,
    DepartmentManagerDelegationFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public listNonManagerEmployee(
        filter: Filter<DepartmentManagerDelegationFilterAttrs>,
    ): Observable<PaginatedResult<Employee>> {
        return this.httpClient.get<PaginatedResult<Employee>>(
            `${this.getBaseEndpoint()}/non-managers`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/departments/manager-delegations`;
    }
}
