import { ChangeDetectionStrategy, Component } from '@angular/core';
import { DepartmentsService } from './departments.service';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'lib-departments',
    template: '<router-outlet/>',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [DepartmentsService],
    imports: [RouterOutlet],
})
export class DepartmentsComponent {}
