import { Pipe, PipeTransform } from '@angular/core';
import { Department } from '../types';
import { MultilingualString } from '../../../../common';
import { formatDepartmentName } from '../utils';

@Pipe({
    name: 'formatDepartmentName',
    standalone: true,
})
export class FormatDepartmentNamePipe implements PipeTransform {
    public transform(
        department?: Department,
        depth?: number,
    ): MultilingualString | undefined {
        return formatDepartmentName(department, depth);
    }
}
