import { Routes } from '@angular/router';
import { DepartmentsComponent } from './departments.component';
import { DepartmentListPageComponent } from './pages/department-list-page/department-list-page.component';
import { DepartmentNewPageComponent } from './pages/department-new-page/department-new-page.component';
import { DepartmentDetailPageComponent } from './pages/department-detail-page/department-detail-page.component';
import { p } from '../../permissions';

export const routes = [
    {
        path: '',
        component: DepartmentsComponent,
        children: [
            {
                path: '',
                component: DepartmentListPageComponent,
                data: {
                    title: 'translate_departments',
                    permissionIds: [p.departments.read],
                },
            },

            {
                path: 'new',
                component: DepartmentNewPageComponent,
                data: {
                    title: 'translate_add_new_department',
                    permissionIds: [p.departments.write],
                },
            },

            {
                path: 'edit/:id',
                component: DepartmentNewPageComponent,
                data: {
                    title: 'translate_update_department_details',
                    permissionIds: [p.departments.write],
                },
            },

            {
                path: ':id',
                component: DepartmentDetailPageComponent,
                data: {
                    title: 'translate_department_details',
                    permissionIds: [p.departments.read],
                },
            },
        ],
    },
] satisfies Routes;
