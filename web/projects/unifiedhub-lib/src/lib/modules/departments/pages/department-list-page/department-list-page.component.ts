import { ChangeDetectionStrategy, Component } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../features';

@Component({
    selector: 'lib-department-list-page',
    templateUrl: './department-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class DepartmentListPageComponent {
    protected config = config();
}
