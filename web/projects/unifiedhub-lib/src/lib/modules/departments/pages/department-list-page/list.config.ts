import { DepartmentsService } from '../../departments.service';
import { Department, DepartmentFilterAttrs } from '../../core/types';
import { inject } from '@angular/core';
import { p } from '../../../../permissions';
import { DynamicListPageConfig } from '../../../../features';

export const config = (): DynamicListPageConfig<
    Department,
    DepartmentFilterAttrs
> => {
    const departmentsService = inject(DepartmentsService);

    return {
        title: 'translate_departments',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_parent_department',
                },

                {
                    label: 'translate_department_children_count',
                },

                {
                    label: 'translate_department_manager',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: { value: ['', 'departments', item.id] },
                },

                {
                    value: item.parent?.name,
                },

                {
                    value: item.childCount,
                },

                {
                    value: item.manager?.name,
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.departments.write,
                },
                deleteButtonConfig: {
                    permissionId: p.departments.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: departmentsService.list.bind(departmentsService),
            deleter: departmentsService.delete.bind(departmentsService),
        },

        newButtonConfig: {
            permissionId: p.departments.write,
        },
    };
};
