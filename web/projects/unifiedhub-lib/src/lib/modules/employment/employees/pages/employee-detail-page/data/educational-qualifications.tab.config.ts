import { Employee, EmployeeEducationalQualification } from '../../../../core';
import { TabListSectionConfigTab } from '../../../../../../features';
import { p } from '../../../../../../permissions';
import { of } from 'rxjs';
import { Injector } from '@angular/core';
import { EducationalQualificationsService } from '../../../../../foundation/educational-qualifications/educational-qualifications.service';

import { EducationalMajorsService } from '../../../../../foundation/educational-majors/educational-majors.service';
import { EducationalSpecializationsService } from '../../../../../foundation/educational-specializations/educational-specializations.service';
import { EmployeeEducationalQualificationsService } from '../../../employee-educational-qualifications.service';
import { FoundationService } from '../../../../../foundation/foundation.service';

export const educationalQualificationsTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const educationalQualificationsService = injector.get(
        EducationalQualificationsService,
    );
    const employeeEducationalQualificationsService = injector.get(
        EmployeeEducationalQualificationsService,
    );
    const educationalMajorsService = injector.get(EducationalMajorsService);
    const educationalSpecializationsService = injector.get(
        EducationalSpecializationsService,
    );
    const foundationService = injector.get(FoundationService);

    return {
        name: 'translate_employee_educational_qualifications',
        permissionId: p.employees.readSensitive,
        badgeCount: employee?.qualifications?.length,

        sectionConfigs: [
            {
                title: 'translate_employee_educational_qualifications',
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'simple',
                        fetcher: () =>
                            employee
                                ? employeeEducationalQualificationsService.listAll(
                                      {
                                          employeeIds: [employee?.id],
                                      },
                                  )
                                : of([]),
                        columnConfigFn: () => [
                            {
                                label: 'translate_qualification',
                            },
                            {
                                label: 'translate_major',
                            },
                            {
                                label: 'translate_specialization',
                            },
                            {
                                label: 'translate_commencement_date',
                            },
                            {
                                label: 'translate_graduation_date',
                            },
                            {
                                label: 'translate_grade',
                            },
                            {
                                label: 'translate_score',
                            },
                            {
                                label: 'translate_institution',
                            },
                            {
                                label: 'translate_place_of_study',
                            },
                            {
                                label: 'translate_attachment',
                            },
                        ],
                        valueConfigFn: item => {
                            const qualification =
                                item as EmployeeEducationalQualification;
                            return [
                                {
                                    value: qualification.qualification?.name,
                                },
                                {
                                    value: qualification.major?.name,
                                },
                                {
                                    value: qualification.specialization?.name,
                                },
                                {
                                    value: qualification.commencementDate,
                                    type: 'date',
                                },
                                {
                                    value: qualification.graduationDate,
                                    type: 'date',
                                },
                                {
                                    value: qualification.grade,
                                    type: 'id_to_name',
                                    idToName$:
                                        foundationService.educationalQualificationGrades(),
                                },
                                {
                                    value: qualification.score,
                                },
                                {
                                    value: qualification.institution,
                                },
                                {
                                    value: qualification.placeOfStudy,
                                },
                                {
                                    value: qualification.attachment!,
                                    type: 'file',
                                    downloader: () =>
                                        employeeEducationalQualificationsService.downloadFile(
                                            qualification.id,
                                        ),
                                },
                            ];
                        },
                        rowConfigFn: () => ({
                            editButtonConfig: {
                                permissionId: p.employees.write,
                            },
                            deleteButtonConfig: {
                                permissionId: p.employees.write,
                            },
                        }),
                        deleter: id =>
                            employeeEducationalQualificationsService.delete(id),
                    },
                    form: {
                        newTitle: 'translate_add_new_educational_qualification',
                        updateTitle:
                            'translate_update_educational_qualification_details',
                        configFn: () => [
                            {
                                id: 'employee',
                                type: 'hidden',
                                defaultValue: employee,
                                required: true,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'qualification',
                                        type: 'select',
                                        label: 'translate_qualification',
                                        required: true,
                                        props: {
                                            items$: educationalQualificationsService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'major',
                                        type: 'select',
                                        label: 'translate_major',
                                        props: {
                                            items$: educationalMajorsService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                    {
                                        id: 'specialization',
                                        type: 'select',
                                        label: 'translate_specialization',
                                        props: {
                                            items$: educationalSpecializationsService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'commencementDate',
                                        type: 'datetime',
                                        label: 'translate_commencement_date',
                                    },
                                    {
                                        id: 'graduationDate',
                                        type: 'datetime',
                                        label: 'translate_graduation_date',
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'grade',
                                        type: 'select',
                                        label: 'translate_grade',
                                        props: {
                                            items$: foundationService.educationalQualificationGrades(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                    {
                                        id: 'score',
                                        type: 'input',
                                        label: 'translate_score',
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'institution',
                                        type: 'input',
                                        label: 'translate_institution',
                                    },
                                    {
                                        id: 'placeOfStudy',
                                        type: 'input',
                                        label: 'translate_place_of_study',
                                    },
                                ],
                            },
                            {
                                id: 'attachment',
                                type: 'file',
                                label: 'translate_attachment',
                            },
                        ],
                        creator: item =>
                            employeeEducationalQualificationsService.create(
                                item as EmployeeEducationalQualification,
                            ),
                        updater: (id, item) =>
                            employeeEducationalQualificationsService.update(
                                id,
                                item as EmployeeEducationalQualification,
                            ),
                        fetcher: id =>
                            employeeEducationalQualificationsService.get(id),
                    },
                    detail: item => {
                        return {
                            title: 'translate_educational_qualification_details',
                            loader: () =>
                                employeeEducationalQualificationsService.get(
                                    item.id,
                                ),
                            sectionConfigs: item => {
                                const qualification =
                                    item as EmployeeEducationalQualification;

                                return [
                                    {
                                        type: 'detail',
                                        noContainer: true,
                                        fieldConfigFn: () => [
                                            {
                                                label: 'translate_qualification',
                                            },
                                            {
                                                label: 'translate_major',
                                            },
                                            {
                                                label: 'translate_specialization',
                                            },
                                            {
                                                label: 'translate_commencement_date',
                                            },
                                            {
                                                label: 'translate_graduation_date',
                                            },
                                            {
                                                label: 'translate_grade',
                                            },
                                            {
                                                label: 'translate_score',
                                            },
                                            {
                                                label: 'translate_institution',
                                            },
                                            {
                                                label: 'translate_place_of_study',
                                            },
                                            {
                                                label: 'translate_attachment',
                                            },
                                        ],

                                        valueConfigFn: () => [
                                            {
                                                value: qualification
                                                    .qualification?.name,
                                            },
                                            {
                                                value: qualification.major
                                                    ?.name,
                                            },
                                            {
                                                value: qualification
                                                    .specialization?.name,
                                            },
                                            {
                                                value: qualification.commencementDate,
                                                type: 'date',
                                            },
                                            {
                                                value: qualification.graduationDate,
                                                type: 'date',
                                            },
                                            {
                                                value: qualification.grade,
                                                type: 'id_to_name',
                                                idToName$:
                                                    foundationService.educationalQualificationGrades(),
                                            },
                                            { value: qualification.score },
                                            {
                                                value: qualification.institution,
                                            },
                                            {
                                                value: qualification.placeOfStudy,
                                            },
                                            {
                                                value: qualification.attachment!,
                                                type: 'file',
                                                downloader: () =>
                                                    employeeEducationalQualificationsService.downloadFile(
                                                        qualification.id,
                                                    ),
                                            },
                                        ],
                                    },
                                ];
                            },
                        };
                    },
                },
            },
        ],
    };
};
