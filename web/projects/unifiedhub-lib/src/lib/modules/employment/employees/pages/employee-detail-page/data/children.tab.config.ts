import { Employee, EmployeeChild } from '../../../../core';
import { TabListSectionConfigTab } from '../../../../../../features';
import { p } from '../../../../../../permissions';
import { of } from 'rxjs';
import { Injector } from '@angular/core';
import { EmployeeChildrenService } from '../../../employee-children.service';
import { CountriesService } from '../../../../../foundation/countries/countries.service';
import { FoundationService } from '../../../../../foundation/foundation.service';
import { MiscService } from '../../../misc.service';

export const childrenTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const foundationService = injector.get(FoundationService);
    const childrenService = injector.get(EmployeeChildrenService);
    const employeeChildrenService = injector.get(EmployeeChildrenService);
    const countriesService = injector.get(CountriesService);

    let nextOrder = employee?.children?.length
        ? Math.max(...employee.children.map(child => child.order!)) + 1
        : 1;

    return {
        name: 'translate_children_record',
        permissionId: p.employees.readSensitive,
        badgeCount: employee?.children?.length,

        sectionConfigs: [
            {
                title: 'translate_children_record',
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'simple',
                        newButtonConfig: {
                            permissionId: p.employees.children.write,
                        },
                        rowConfigFn: () => ({
                            editButtonConfig: {
                                permissionId: p.employees.children.write,
                            },
                            deleteButtonConfig: {
                                permissionId: p.employees.children.delete,
                            },
                        }),
                        columnConfigFn: () => [
                            {
                                label: 'translate_name',
                            },
                            {
                                label: 'translate_date_of_birth',
                            },
                            {
                                label: 'translate_order',
                            },
                            {
                                label: 'translate_gender',
                            },
                            {
                                label: 'translate_place_of_birth',
                            },
                            {
                                label: 'translate_id_number',
                            },
                            {
                                label: 'translate_nationality',
                            },
                            {
                                label: 'translate_mother_name',
                            },
                            {
                                label: 'translate_allowance_status',
                            },
                            {
                                label: 'translate_notes',
                            },
                        ],
                        valueConfigFn: item => {
                            const child = item as EmployeeChild;
                            return [
                                {
                                    value: child.name,
                                },
                                {
                                    value: child.dateOfBirth,
                                    type: 'date',
                                },
                                {
                                    value: child.order,
                                },
                                {
                                    value: child.gender,
                                    type: 'id_to_name',
                                    idToName$: foundationService.genders(),
                                },
                                {
                                    value: child.placeOfBirth,
                                },
                                {
                                    value: child.nationalIdNumber,
                                },
                                {
                                    value: child.nationality?.name,
                                },
                                {
                                    value: child.motherName,
                                },
                                {
                                    value: child.allowanceStatus,
                                    type: 'id_to_name',
                                    idToName$: injector
                                        .get(MiscService)
                                        .childAllowanceStatuses(),
                                },
                                {
                                    value: child.notes,
                                },
                            ];
                        },
                        fetcher: () =>
                            employee
                                ? childrenService.listAll({
                                      employeeIds: [employee?.id],
                                  })
                                : of([]),
                        deleter: id => childrenService.delete(id),
                    },
                    form: {
                        newTitle: 'translate_add_child',
                        updateTitle: 'translate_update_child',
                        configFn: () => [
                            {
                                id: 'employee',
                                type: 'hidden',
                                defaultValue: employee,
                                required: true,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'name',
                                        type: 'multilingualTextInput',
                                        label: 'translate_name',
                                        required: true,
                                    },
                                    {
                                        id: 'order',
                                        type: 'input',
                                        size: 30,
                                        defaultValue: nextOrder,
                                        required: true,
                                        label: 'translate_order',
                                        props: {
                                            type: 'number',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'dateOfBirth',
                                        type: 'datetime',
                                        required: true,
                                        label: 'translate_date_of_birth',
                                    },
                                    {
                                        id: 'placeOfBirth',
                                        type: 'input',
                                        label: 'translate_place_of_birth',
                                    },
                                    {
                                        id: 'gender',
                                        type: 'select',
                                        label: 'translate_gender',
                                        required: true,
                                        props: {
                                            items$: injector
                                                .get(FoundationService)
                                                .genders(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                    {
                                        id: 'nationality',
                                        type: 'select',
                                        label: 'translate_nationality',
                                        props: {
                                            items$: countriesService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                isGrid: true,
                                config: [
                                    {
                                        id: 'motherName',
                                        type: 'input',
                                        label: 'translate_mother_name',
                                        required: true,
                                    },

                                    {
                                        id: 'allowanceStatus',
                                        type: 'select',
                                        label: 'translate_allowance_status',
                                        props: {
                                            items$: injector
                                                .get(MiscService)
                                                .childAllowanceStatuses(),
                                            bindValue: 'id',
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'nationalIdNumber',
                                        type: 'input',
                                        label: 'translate_id_number',
                                        required: true,
                                    },
                                    {
                                        id: 'nationalIdExpirationDate',
                                        type: 'datetime',
                                        label: 'translate_national_id_expiration_date',
                                        required: true,
                                    },
                                    {
                                        id: 'nationalIdFile',
                                        type: 'file',
                                        label: 'translate_national_id_file',
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'passportFile',
                                        type: 'file',
                                        label: 'translate_passport_file',
                                    },
                                    {
                                        id: 'birthCertificateFile',
                                        type: 'file',
                                        label: 'translate_birth_certificate',
                                    },
                                ],
                            },
                            {
                                id: 'notes',
                                type: 'textarea',
                                label: 'translate_notes',
                            },
                        ],
                        creator: item =>
                            employeeChildrenService.create(
                                item as EmployeeChild,
                            ),
                        updater: (id, item) =>
                            childrenService.update(id, item as EmployeeChild),
                        fetcher: id => childrenService.get(id),
                    },
                },
            },
        ],
    };
};
