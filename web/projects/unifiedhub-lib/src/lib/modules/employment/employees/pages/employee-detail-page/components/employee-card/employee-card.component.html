<div
    *libWaitUntilLoaded="employee(); alignment: 'center'"
    class="flex flex-col items-center gap-2"
>
    <!-- Photo -->
    <lib-image
        class="mx-auto mb-4 h-52 w-52 rounded"
        [imageObservable]="employeePhotoObservable()!"
    />

    <!-- Employee number -->
    <div class="text-lg font-bold">{{ employee()!.number }}</div>

    <!-- Job classification / level -->
    <div class="text-lg font-bold">
        @if (employee()!.jobClassification?.type === 'civil') {
            {{
                employee()!.jobClassification!.name
                    | translateMultilingualString
            }}
        } @else if (
            employee()!.jobClassification?.type === 'sworn' &&
            employee()!.jobLevel
        ) {
            {{ employee()!.jobLevel!.name | translateMultilingualString }}
        }
    </div>

    <!-- Name -->
    <div class="text-center text-lg font-bold">
        {{ employee()!.name | translateMultilingualString }}
    </div>

    @if (employee()!.jobTitle) {
        <!-- Job title -->
        <div class="text-center text-gray-500">
            {{ employee()!.jobTitle!.name | translateMultilingualString }}
        </div>
    }

    <!-- State -->
    <div
        class="mt-4 flex flex-col items-center gap-2 rounded-2xl px-3 py-1 text-white"
        [ngClass]="{
            'bg-green-400': employee()!.state === 'active',
            'bg-orange-500': employee()!.state === 'inactive',
            'bg-yellow-400': employee()!.state === 'suspended'
        }"
    >
        {{ employee()!.state | idToName: employeesService.states() | async }}
    </div>
</div>
