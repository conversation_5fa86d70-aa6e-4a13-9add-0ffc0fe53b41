import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import {
    Employee,
    EmployeeExportFilterAttrs,
    EmployeeFilterAttrs,
    EmployeeStatistics,
} from '../core';
import { ServiceRequest, ServiceRequestFilter } from '../../servicing';
import { Observable } from 'rxjs';
import {
    CrudService,
    ONGOING_REQUEST_LOADER_INTERCEPTOR_STEALTH,
} from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import {
    Filter,
    Item,
    mapFilterToQueryParams,
    mapObjectToQueryParams,
    PaginatedResult,
} from '../../../common';
import { CACHE_INTERCEPTOR, ERROR_INTERCEPTOR_STEALTH } from '../../../core';
import { DepartmentManager } from '../../departments/core';

@Injectable()
export class EmployeesService extends CrudService<
    Employee,
    EmployeeFilterAttrs,
    Pick<EmployeeFilterAttrs, 'keyword'>
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public getServiceRequests(
        id: string,
        filter?: Filter<ServiceRequestFilter>,
    ): Observable<PaginatedResult<ServiceRequest>> {
        return this.httpClient.get<PaginatedResult<ServiceRequest>>(
            `${this.getBaseEndpoint()}/${id}/service-requests`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }

    public exportToExcel(
        filterAttrs: EmployeeExportFilterAttrs,
    ): Observable<Blob> {
        Object.keys(filterAttrs).forEach(key => {
            const date = filterAttrs[key as keyof EmployeeExportFilterAttrs];
            if (!(date instanceof Date)) return;
            (filterAttrs as Record<string, string>)[key] = date.toISOString();
        });
        return this.httpClient.get(`${this.getBaseEndpoint()}/export`, {
            params: mapObjectToQueryParams(filterAttrs),
            responseType: 'blob',
        });
    }

    public downloadPhoto(id: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.config.apiUrl}/employees/${id}/photo`,
            {
                responseType: 'blob',
                context: new HttpContext()
                    .set(ONGOING_REQUEST_LOADER_INTERCEPTOR_STEALTH, true)
                    .set(ERROR_INTERCEPTOR_STEALTH, true),
            },
        );
    }

    public statistics(id: string): Observable<EmployeeStatistics> {
        return this.httpClient.get<EmployeeStatistics>(
            `${this.getBaseEndpoint()}/${id}/statistics`,
        );
    }

    public states(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(`${this.getBaseEndpoint()}/states`, {
            context: new HttpContext().set(CACHE_INTERCEPTOR, true),
        });
    }

    public updateState(id: string, data: unknown): Observable<void> {
        return this.httpClient.put<void>(
            `${this.getBaseEndpoint()}/${id}/state`,
            data,
        );
    }

    public getManagers(
        id: string,
        level: number = 0,
    ): Observable<DepartmentManager[]> {
        return this.httpClient.get<DepartmentManager[]>(
            `${this.getBaseEndpoint()}/${id}/managers`,
            {
                params: new HttpParams().append('level', level ?? 0),
            },
        );
    }

    public getManagersForCurrentUser(
        level: number = 0,
    ): Observable<DepartmentManager[]> {
        return this.httpClient.get<DepartmentManager[]>(
            `${this.getBaseEndpoint()}/current/managers`,
            {
                params: new HttpParams().append('level', level ?? 0),
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees`;
    }
}
