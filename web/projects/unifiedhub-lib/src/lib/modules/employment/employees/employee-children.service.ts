import { Inject, Injectable } from '@angular/core';
import { CrudService } from '../../../features';
import { EmployeeChild, EmployeeChildFilterAttrs } from '../core';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../config';

@Injectable()
export class EmployeeChildrenService extends CrudService<
    EmployeeChild,
    EmployeeChildFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/children`;
    }
}
