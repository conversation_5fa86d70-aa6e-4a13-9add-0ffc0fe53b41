import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    output,
} from '@angular/core';
import { Employee } from '../../../../../core';
import { EmployeesService } from '../../../../employees.service';
import { config } from './new.config';
import { DynamicNewFormComponent } from '../../../../../../../features';

@Component({
    selector: 'lib-update-employee-state',
    templateUrl: 'update-employee-state.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewFormComponent],
})
export class UpdateEmployeeStateComponent {
    public readonly employee = input.required<Employee>();
    public readonly update = output<void>();

    protected config = computed(() => {
        return config(this.employee(), this.employeesService);
    });

    public constructor(private readonly employeesService: EmployeesService) {}
}
