import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import { EmployeeBankAccount, EmployeeBankAccountFilterAttrs } from '../core';
import { CrudService } from '../../../features';

@Injectable()
export class EmployeeBankAccountsService extends CrudService<
    EmployeeBankAccount,
    EmployeeBankAccountFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public setAsDefault(id: string): Observable<void> {
        return this.httpClient.put<void>(
            `${this.getBaseEndpoint()}/${id}/set-as-default`,
            undefined,
        );
    }

    public download(id: string): Observable<Blob> {
        return this.httpClient.get(`${this.getBaseEndpoint()}/${id}/download`, {
            responseType: 'blob',
        });
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/bank-accounts`;
    }
}
