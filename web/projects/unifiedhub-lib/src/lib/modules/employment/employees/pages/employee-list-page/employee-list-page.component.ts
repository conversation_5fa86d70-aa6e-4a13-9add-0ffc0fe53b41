import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { TranslateModule } from '@ngx-translate/core';
import { p } from '../../../../../permissions';
import { CitiesService } from '../../../../foundation/cities/cities.service';

@Component({
    selector: 'lib-employee-list-page',
    templateUrl: './employee-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent, TranslateModule],
    providers: [CitiesService],
})
export class EmployeeListPageComponent {
    protected config = config(inject(Injector));
    protected readonly p = p;
}
