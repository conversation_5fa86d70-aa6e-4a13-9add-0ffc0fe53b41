import { Employee, EmployeeDocument } from '../../../../core';
import { Injector } from '@angular/core';
import { TabListSectionConfigTab } from '../../../../../../features';
import { of } from 'rxjs';
import { EmployeeDocumentsService } from '../../../../employee-documents.service';
import { TranslateService } from '@ngx-translate/core';
import { p } from '../../../../../../permissions';
import { MultilingualStringTranslatorService } from '../../../../../../core';

export const documentsTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const employeeDocumentsService = injector.get(EmployeeDocumentsService);
    const translationService = injector.get(TranslateService);

    return {
        name: 'translate_employee_documents',
        permissionId: p.employees.readSensitive,
        sectionConfigs: [
            {
                type: 'list_with_create',
                noContainer: true,

                config: {
                    list: {
                        type: 'simple',
                        columnConfigFn: () => [
                            {
                                label: 'translate_name',
                            },

                            {
                                label: 'translate_document_number',
                            },

                            {
                                label: 'translate_issue_date',
                            },

                            {
                                label: 'translate_issue_place',
                            },

                            {
                                label: 'translate_expiration_date',
                            },

                            {
                                label: 'translate_other_details',
                            },

                            {
                                label: 'translate_file',
                            },
                        ],
                        valueConfigFn: tmp => {
                            const item = tmp as EmployeeDocument;
                            return [
                                {
                                    value:
                                        item.type +
                                        (item.type === 'other'
                                            ? ' - ' +
                                              injector
                                                  .get(
                                                      MultilingualStringTranslatorService,
                                                  )
                                                  .get(item.name)
                                            : ''),
                                    type: 'id_to_name',
                                    idToName$: employeeDocumentsService.types(),
                                },

                                {
                                    value: item.number,
                                },

                                {
                                    value: item.issueDate,
                                    type: 'date',
                                },

                                {
                                    value: item.issuePlace,
                                },

                                {
                                    value: item.expirationDate,
                                    type: 'date',
                                },

                                {
                                    value:
                                        item.unifiedNumber != null
                                            ? `${translationService.instant('translate_unified_number')} : ${item.unifiedNumber}`
                                            : item.familyBookNumber != null
                                              ? `${translationService.instant('translate_family_book_number')} : ${item.familyBookNumber}`
                                              : item.notes,
                                },

                                {
                                    value: item.file!,
                                    downloader: () =>
                                        employeeDocumentsService.download(
                                            item.id,
                                        ),
                                    type: 'file',
                                },
                            ];
                        },

                        fetcher: () =>
                            employee
                                ? employeeDocumentsService.listAll({
                                      employeeIds: [employee?.id],
                                      filterCountExclusions: ['employee_ids'],
                                  })
                                : of([]),

                        deleter: id => employeeDocumentsService.delete(id),
                    },

                    form: {
                        newTitle: 'translate_create_new_document',
                        updateTitle: 'translate_update_document',

                        configFn: () => [
                            {
                                id: 'employee',
                                type: 'hidden',
                                defaultValue: employee,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'number',
                                        label: 'translate_document_number',
                                        type: 'input',
                                        props: {
                                            type: 'text',
                                        },
                                    },
                                    {
                                        id: 'type',
                                        type: 'select',
                                        label: 'translate_type',
                                        required: true,
                                        props: {
                                            items$: employeeDocumentsService.types(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                        onValueChange: (value, _, fields) => {
                                            fields['name'].required = false;

                                            [
                                                'name',
                                                'unifiedNumber',
                                                'familyBookNumber',
                                            ].forEach(
                                                x => (fields[x].hide = true),
                                            );

                                            [
                                                'expirationDate',
                                                'issueDate',
                                                'issuePlace',
                                            ].forEach(
                                                x => (fields[x].hide = false),
                                            );

                                            switch (value) {
                                                case 'other':
                                                    fields['name'].required =
                                                        true;
                                                    fields['name'].hide = false;
                                                    break;

                                                case 'passport':
                                                    fields[
                                                        'unifiedNumber'
                                                    ].hide = false;
                                                    break;

                                                case 'family_book':
                                                    fields[
                                                        'familyBookNumber'
                                                    ].hide = false;
                                                    fields[
                                                        'expirationDate'
                                                    ].hide = true;
                                                    break;

                                                case 'signature':
                                                    [
                                                        'issueDate',
                                                        'issuePlace',
                                                        'expirationDate',
                                                    ].forEach(x => {
                                                        fields[x].hide = true;
                                                    });
                                                    break;
                                            }
                                        },
                                    },
                                ],
                            },
                            {
                                id: 'name',
                                label: 'translate_document_name',
                                type: 'multilingualTextInput',
                                hide: true,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'issueDate',
                                        type: 'datetime',
                                        label: 'translate_issue_date',
                                    },

                                    {
                                        id: 'issuePlace',
                                        type: 'input',
                                        label: 'translate_issue_place',
                                    },

                                    {
                                        id: 'unifiedNumber',
                                        hide: true,
                                        type: 'input',
                                        label: 'translate_unified_number',
                                    },
                                    {
                                        id: 'familyBookNumber',
                                        hide: true,
                                        type: 'input',
                                        label: 'translate_family_book',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'expirationDate',
                                        type: 'datetime',
                                        label: 'translate_expiration_date',
                                    },

                                    {
                                        id: 'file',
                                        type: 'file',
                                        label: 'translate_file',
                                    },
                                ],
                            },
                            {
                                id: 'notes',
                                type: 'textarea',
                                label: 'translate_notes',
                            },
                        ],

                        creator: item =>
                            employeeDocumentsService.create(
                                item as EmployeeDocument,
                            ),

                        fetcher: id => employeeDocumentsService.get(id),
                        updater: (id, item) =>
                            employeeDocumentsService.update(
                                id,
                                item as EmployeeDocument,
                            ),
                    },
                },
            },
        ],
    };
};
