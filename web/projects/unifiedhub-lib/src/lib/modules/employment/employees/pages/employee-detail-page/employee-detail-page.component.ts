import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    OutputRefSubscription,
    signal,
    TemplateRef,
    viewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { config } from './data/detail.config';
import { Employee } from '../../../core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LeavesService } from '../../../../leaves-management';
import {
    DynamicDetailPageComponent,
    ModalService,
} from '../../../../../features';
import { IdToNamePipe } from '../../../../../common';
import { AlertComponent } from '../../../../../ui';
import { AsyncPipe, DatePipe } from '@angular/common';
import { EmployeeTransfersService } from '../../employee-transfers.service';
import {
    RbacDirective,
    TranslateMultilingualStringPipe,
} from '../../../../../core';
import { CourseEmployeesService } from '../../../../courses-management/courses/course-employees.service';
import { UpdateEmployeeStateComponent } from './components/update-employee-state/update-employee-state.component';
import { firstValueFrom } from 'rxjs';
import { p } from '../../../../../permissions';
import { CoursesService } from '../../../../courses-management/courses/courses.service';
import { EmployeeMedicalExemptionsService } from '../../employee-medical-exemptions.service';
import { EmployeeMedicalExemptionTypesService } from '../../../employee-medical-exemption-types/employee-medical-exemption-types.service';
import { BanksService } from '../../../../foundation/banks/banks.service';
import { EmployeeBankAccountsService } from '../../employee-bank-accounts.service';

@Component({
    selector: 'lib-employee-detail-age',
    templateUrl: './employee-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        CoursesService,
        CourseEmployeesService,
        EmployeeMedicalExemptionsService,
        EmployeeMedicalExemptionTypesService,
        BanksService,
        EmployeeBankAccountsService,
    ],
    imports: [
        DynamicDetailPageComponent,
        FormsModule,
        TranslateModule,
        AlertComponent,
        IdToNamePipe,
        AsyncPipe,
        DatePipe,
        TranslateMultilingualStringPipe,
        RbacDirective,
    ],
})
export class EmployeeDetailPageComponent {
    protected readonly config = computed(() => {
        return config(this.injector, this.alertsTemplate()!);
    });

    protected readonly item = signal<Employee | null>(null);
    protected readonly itemId = signal<string | null>(null);

    protected readonly p = p;

    private readonly alertsTemplate =
        viewChild<TemplateRef<unknown>>('alertsTemplate');

    private readonly dynamicDetailPageComponent = viewChild<
        DynamicDetailPageComponent<Employee>
    >('dynamicDetailPageComponent');

    public constructor(
        protected readonly leavesService: LeavesService,
        protected readonly employeeTransfersService: EmployeeTransfersService,
        private readonly injector: Injector,
        private readonly modalService: ModalService,
        private readonly translateService: TranslateService,
    ) {}

    protected async updateState(): Promise<void> {
        let subscription: OutputRefSubscription;

        const component = await this.modalService.show(
            UpdateEmployeeStateComponent,
            {
                title: await firstValueFrom(
                    this.translateService.get('translate_update_state'),
                ),
                injector: this.injector,
                inputs: {
                    employee: this.item()!,
                },
                onDismiss: () => {
                    subscription?.unsubscribe();
                },
            },
        );

        subscription = component.update.subscribe(() => {
            this.dynamicDetailPageComponent()!.reload();
            this.modalService.dismiss(component);
        });
    }
}
