import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { Observable } from 'rxjs';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Item } from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';

@Injectable()
export class MiscService {
    public constructor(
        private readonly httpClient: HttpClient,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {}

    public operationTypes(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.config.apiUrl}/employees/misc/operation-types`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    public childAllowanceStatuses(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.config.apiUrl}/employees/misc/child-allowance-statuses`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }
}
