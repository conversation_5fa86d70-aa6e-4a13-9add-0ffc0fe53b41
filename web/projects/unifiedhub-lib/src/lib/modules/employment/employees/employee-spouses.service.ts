import { Inject, Injectable } from '@angular/core';
import { CrudService } from '../../../features';
import { EmployeeSpouse, EmployeeSpouseFilterAttrsType } from '../core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import { Item } from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';

@Injectable()
export class EmployeeSpousesService extends CrudService<
    EmployeeSpouse,
    EmployeeSpouseFilterAttrsType
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public marriageStatuses(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.getBaseEndpoint()}/statuses`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    public downloadCertificate(id: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.getBaseEndpoint()}/${id}/certificate/download`,
            {
                responseType: 'blob',
            },
        );
    }

    public downloadNoc(id: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.getBaseEndpoint()}/${id}/noc/download`,
            {
                responseType: 'blob',
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/spouses`;
    }
}
