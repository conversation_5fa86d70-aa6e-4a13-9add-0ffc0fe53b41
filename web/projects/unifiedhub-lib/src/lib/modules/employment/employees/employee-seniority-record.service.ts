import { Inject, Injectable } from '@angular/core';
import { CrudService } from '../../../features';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import { SeniorityRecord } from '../core/types/seniority-record.type';

@Injectable()
export class SeniorityRecordsService extends CrudService<SeniorityRecord, {}> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public downloadFile(id: string): Observable<Blob> {
        return this.httpClient.get(`${this.getBaseEndpoint()}/${id}/download`, {
            responseType: 'blob',
        });
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/seniority-records`;
    }
}
