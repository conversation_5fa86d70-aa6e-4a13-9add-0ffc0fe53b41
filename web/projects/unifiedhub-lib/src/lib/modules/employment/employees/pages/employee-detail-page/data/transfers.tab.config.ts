import { Employee, EmployeeTransfer } from '../../../../core';
import { Injector } from '@angular/core';
import { p } from '../../../../../../permissions';
import { TabListSectionConfigTab } from '../../../../../../features';
import { EmployeeTransfersService } from '../../../employee-transfers.service';
import { of } from 'rxjs';
import { EmployeeTransferReasonsService } from '../../../employee-transfer-reasons.service';
import { DepartmentsService } from '../../../../../departments/departments.service';

export const transfersTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const employeeTransfersService = injector.get(EmployeeTransfersService);
    const transferReasonsService = injector.get(EmployeeTransferReasonsService);
    const transfersData$ = employee
        ? employeeTransfersService.listAll({
              employeeIds: [employee.id],
              filterCountExclusions: ['employee_ids'],
          })
        : of([]);
    return {
        name: 'translate_employee_transfer_events',
        sectionConfigs: [
            {
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'simple',
                        columnConfigFn: () => [
                            {
                                label: 'translate_transfer_date',
                            },

                            {
                                label: 'translate_transfer_to_date',
                            },

                            {
                                label: 'translate_type',
                            },

                            {
                                label: 'translate_reason',
                            },

                            {
                                label: 'translate_from',
                            },

                            {
                                label: 'translate_to',
                            },

                            {
                                label: 'translate_reference_number',
                            },

                            {
                                label: 'translate_notes',
                            },

                            {
                                label: 'translate_service_request',
                                permissionId: p.servicing.requests.read,
                            },

                            {
                                label: 'translate_attachments',
                            },
                        ],
                        valueConfigFn: tmp => {
                            const item = tmp as EmployeeTransfer;
                            return [
                                {
                                    value: item.transferFromDate,
                                    type: 'date',
                                },
                                {
                                    value:
                                        item.transferToDate ??
                                        'translate_unknown',
                                    type:
                                        item.transferToDate != null
                                            ? 'date'
                                            : 'text',
                                },
                                {
                                    value: item.type,
                                    type: 'id_to_name',
                                    idToName$: employeeTransfersService.types(),
                                },

                                {
                                    value: item.transferReason?.name,
                                },

                                {
                                    value: item.fromDepartment?.name,
                                },

                                {
                                    value: item.toDepartment.name,
                                },

                                {
                                    value: item.referenceNumber,
                                },

                                {
                                    value: {
                                        line1: [
                                            {
                                                value: item.reason ?? '',
                                            },
                                        ],
                                        line2: [
                                            {
                                                value: item.notes ?? '',
                                            },
                                        ],
                                    },
                                    type: 'multi_line',
                                },

                                {
                                    value: item.serviceRequest
                                        ? 'translate_go_to_request_details'
                                        : 'translate_no_request',
                                    linkConfig: item.serviceRequest && {
                                        value: [
                                            '',
                                            'servicing',
                                            'requests',
                                            item.serviceRequest?.id,
                                        ],
                                    },
                                },

                                {
                                    value: item.file!,
                                    downloader: () =>
                                        employeeTransfersService.download(
                                            item.id,
                                        ),
                                    type: 'file',
                                },
                            ];
                        },

                        fetcher: () => transfersData$,

                        deleter: id => employeeTransfersService.delete(id),
                    },

                    form: {
                        newTitle: 'translate_create_new_department_transfer',
                        updateTitle: 'translate_edit_details',
                        configFn: () => [
                            {
                                id: 'employeeId',
                                type: 'hidden',
                                defaultValue: employee?.id,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'type',
                                        type: 'select',
                                        label: 'translate_type',
                                        required: true,
                                        onValueChange: (value, _, fields) => {
                                            if (value !== 'transfer') {
                                                fields['transferToDate'].hide =
                                                    false;
                                                fields[
                                                    'transferToDate'
                                                ].required = true;
                                            } else {
                                                fields['transferToDate'].hide =
                                                    true;
                                                fields[
                                                    'transferToDate'
                                                ].required = false;
                                            }
                                        },
                                        props: {
                                            items$: employeeTransfersService.types(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                    {
                                        id: 'transferReason',
                                        type: 'select',
                                        label: 'translate_transfer_reasons',
                                        required: true,
                                        props: {
                                            items$: transferReasonsService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'fromDepartment',
                                        type: 'select',
                                        label: 'translate_from_department',
                                        defaultValue: employee?.department,
                                        props: {
                                            items$: injector
                                                .get(DepartmentsService)
                                                .listAll(),
                                            bindLabel: 'name',
                                            bindLabelSecond: 'parent.name',
                                            bindLabelThird: 'parentSecond.name',
                                            compareWith: (a, b) =>
                                                a.id === b.id,
                                        },
                                    },

                                    {
                                        id: 'toDepartment',
                                        type: 'select',
                                        label: 'translate_to_department',
                                        required: true,
                                        props: {
                                            items$: injector
                                                .get(DepartmentsService)
                                                .listAll(),
                                            bindLabel: 'name',
                                            bindLabelSecond: 'parent.name',
                                            bindLabelThird: 'parentSecond.name',
                                        },
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'transferFromDate',
                                        type: 'datetime',
                                        label: 'translate_transfer_date',
                                        required: true,
                                    },
                                    {
                                        id: 'transferToDate',
                                        type: 'datetime',
                                        label: 'translate_transfer_to_date',
                                        hide: true,
                                    },
                                    {
                                        id: 'referenceNumber',
                                        type: 'input',
                                        label: 'translate_reference_number',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'reason',
                                        type: 'textarea',
                                        label: 'translate_transfer_reason_details',
                                    },

                                    {
                                        id: 'notes',
                                        type: 'textarea',
                                        label: 'translate_notes',
                                    },
                                ],
                            },
                            {
                                id: 'file',
                                type: 'file',
                                label: 'translate_file',
                            },
                            {
                                id: 'shouldUpdateEmployee',
                                label: 'translate_apply_changes_to_employee_profile',
                                type: 'input',
                                props: {
                                    type: 'checkbox',
                                },
                            },
                        ],
                        creator: item =>
                            employeeTransfersService.create(
                                item as EmployeeTransfer,
                            ),
                        fetcher: id => employeeTransfersService.get(id),
                        updater: (id, item) =>
                            employeeTransfersService.update(
                                id,
                                item as EmployeeTransfer,
                            ),
                    },
                },
            },
        ],
    };
};
