import { TabListSectionConfigTab } from '../../../../../../features';
import { Employee, EmployeeSpouse } from '../../../../core';
import { p } from '../../../../../../permissions';
import { CountriesService } from '../../../../../foundation/countries/countries.service';
import { Injector } from '@angular/core';
import { EmployeeSpousesService } from '../../../employee-spouses.service';
import { of } from 'rxjs';
import { FoundationService } from '../../../../../foundation/foundation.service';

export const spousesTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const countriesService = injector.get(CountriesService);
    const employeeSpousesService = injector.get(EmployeeSpousesService);
    return {
        name: 'translate_spouses',
        permissionId: p.employees.readSensitive,

        sectionConfigs: [
            {
                title:
                    employee?.gender === 'male'
                        ? 'translate_wife_details'
                        : 'translate_husband_details',
                type: 'list_with_create',
                config: {
                    list: {
                        type: 'simple',
                        fetcher: employee
                            ? () =>
                                  employeeSpousesService.listAll({
                                      employeeIds: [employee?.id],
                                  })
                            : () => of([]),
                        deleter: (id: string) =>
                            employeeSpousesService.delete(id),

                        columnConfigFn: () => [
                            {
                                label:
                                    employee?.gender === 'male'
                                        ? 'translate_wife_name'
                                        : 'translate_husband_name',
                            },
                            {
                                label: 'translate_nationality',
                            },
                            {
                                label: 'translate_date_of_birth',
                            },
                            {
                                label: 'translate_marriage_status',
                            },
                            {
                                label: 'translate_date_of_marriage',
                            },
                            {
                                label: 'translate_marriage_certificate',
                            },
                            {
                                label: 'translate_has_house_allowance',
                            },
                            {
                                label: 'translate_is_working',
                            },
                            {
                                label: 'translate_service_request',
                                permissionId: p.servicing.requests.read,
                            },
                        ],
                        valueConfigFn: item => {
                            const spouse = item as EmployeeSpouse;
                            return [
                                {
                                    value: spouse.name,
                                },
                                {
                                    value: spouse.nationality?.name,
                                },
                                {
                                    value: spouse.dateOfBirth,
                                    type: 'date',
                                },
                                {
                                    value: spouse.marriageStatus,
                                    type: 'id_to_name',
                                    idToName$:
                                        employeeSpousesService.marriageStatuses(),
                                },
                                {
                                    value: spouse.dateOfMarriage,
                                    type: 'date',
                                },
                                spouse.marriageCertificateFile
                                    ? {
                                          value: spouse.marriageCertificateFile,
                                          type: 'file',
                                          downloader: () =>
                                              employeeSpousesService.downloadCertificate(
                                                  spouse.id,
                                              ),
                                      }
                                    : { value: '-' },
                                {
                                    value: spouse.hasHouseAllowance,
                                    type: 'boolean',
                                },
                                {
                                    value: spouse.isWorking,
                                    type: 'boolean',
                                },
                                {
                                    value: spouse.serviceRequest
                                        ? 'translate_go_to_request_details'
                                        : 'translate_no_request',
                                    linkConfig: spouse.serviceRequest && {
                                        value: [
                                            '',
                                            'servicing',
                                            'requests',
                                            spouse.serviceRequest?.id,
                                        ],
                                    },
                                },
                            ];
                        },

                        rowConfigFn: () => ({
                            editButtonConfig: {
                                permissionId: p.employees.write,
                            },
                            deleteButtonConfig: {
                                permissionId: p.employees.write,
                            },
                        }),
                    },
                    form: {
                        newTitle:
                            employee?.gender === 'female'
                                ? 'translate_add_new_husband'
                                : 'translate_add_new_wife',
                        updateTitle:
                            employee?.gender === 'female'
                                ? 'translate_update_husband'
                                : 'translate_update_wife',
                        configFn: () => [
                            {
                                id: 'employee',
                                type: 'hidden',
                                required: true,
                                defaultValue: employee,
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'name',
                                        type: 'multilingualTextInput',
                                        label: 'translate_spouse_name',
                                        required: true,
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'dateOfBirth',
                                        type: 'datetime',
                                        label: 'translate_date_of_birth',
                                    },
                                    {
                                        id: 'placeOfBirth',
                                        type: 'input',
                                        label: 'translate_place_of_birth',
                                    },
                                    {
                                        id: 'idNumber',
                                        type: 'input',
                                        label: 'translate_id_number',
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'gender',
                                        type: 'select',
                                        label: 'translate_gender',
                                        required: true,
                                        defaultValue:
                                            employee?.gender === 'female'
                                                ? 'male'
                                                : 'female',
                                        props: {
                                            items$: injector
                                                .get(FoundationService)
                                                .genders(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                    {
                                        id: 'nationality',
                                        type: 'select',
                                        label: 'translate_nationality',
                                        props: {
                                            items$: countriesService.listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'dateOfMarriage',
                                        type: 'datetime',
                                        label: 'translate_date_of_marriage',
                                    },
                                    {
                                        id: 'placeOfMarriage',
                                        type: 'input',
                                        label: 'translate_place_of_marriage',
                                    },
                                    {
                                        id: 'marriageStatus',
                                        type: 'select',
                                        label: 'translate_marriage_status',
                                        required: true,
                                        props: {
                                            items$: injector
                                                .get(EmployeeSpousesService)
                                                .marriageStatuses(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                ],
                            },
                            {
                                id: 'isWorking',
                                type: 'input',
                                label: 'translate_is_working',
                                props: {
                                    type: 'checkbox',
                                },
                                onValueChange: (value, group, fields) => {
                                    [
                                        'hireDate',
                                        'workNocFile',
                                        'jobTitle',
                                        'workAddress',
                                        'placeOfWork',
                                    ].forEach(fieldId => {
                                        if (fields[fieldId]) {
                                            fields[fieldId].hide = !value;
                                        }
                                    });
                                },
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'hireDate',
                                        type: 'datetime',
                                        label: 'translate_hire_date',
                                        hide: true,
                                    },
                                    {
                                        id: 'jobTitle',
                                        type: 'input',
                                        label: 'translate_job_title',
                                        hide: true,
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'workAddress',
                                        type: 'input',
                                        label: 'translate_work_address',
                                        hide: true,
                                    },
                                    {
                                        id: 'placeOfWork',
                                        type: 'input',
                                        label: 'translate_place_of_work',
                                        hide: true,
                                    },
                                ],
                            },
                            {
                                id: 'hasHouseAllowance',
                                type: 'input',
                                label: 'translate_has_house_allowance',
                                props: {
                                    type: 'checkbox',
                                },
                                onValueChange: (value, group, fields) => {
                                    [
                                        'houseAllowanceAmount',
                                        'allowanceStartDate',
                                    ].forEach(fieldId => {
                                        if (fields[fieldId]) {
                                            fields[fieldId].hide = !value;
                                        }
                                    });
                                },
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'houseAllowanceAmount',
                                        type: 'input',
                                        label: 'translate_allowance_amount',
                                        hide: true,
                                        props: {
                                            type: 'number',
                                        },
                                    },
                                    {
                                        id: 'allowanceStartDate',
                                        type: 'datetime',
                                        label: 'translate_allowance_start_date',
                                        hide: true,
                                    },
                                ],
                            },
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'marriageCertificateFile',
                                        type: 'file',
                                        label: 'translate_marriage_certificate',
                                    },
                                    {
                                        id: 'workNocFile',
                                        type: 'file',
                                        label: 'translate_work_noc_file',
                                        hide: true,
                                    },
                                ],
                            },
                            {
                                id: 'notes',
                                type: 'textarea',
                                label: 'translate_notes',
                            },
                        ],
                        fetcher: id => employeeSpousesService.get(id),
                        creator: item =>
                            employeeSpousesService.create(
                                item as EmployeeSpouse,
                            ),
                        updater: (id, item) =>
                            employeeSpousesService.update(
                                id,
                                item as EmployeeSpouse,
                            ),
                    },

                    detail: item => {
                        return {
                            title: 'translate_spouse_details',
                            loader: () => employeeSpousesService.get(item.id),
                            sectionConfigs: item => {
                                const spouse = item as EmployeeSpouse;

                                return [
                                    {
                                        type: 'detail',
                                        noContainer: true,
                                        fieldConfigFn: () => [
                                            {
                                                label:
                                                    employee?.gender === 'male'
                                                        ? 'translate_wife_name'
                                                        : 'translate_husband_name',
                                            },
                                            {
                                                label: 'translate_nationality',
                                            },
                                            {
                                                label: 'translate_date_of_birth',
                                            },
                                            {
                                                label: 'translate_marriage_status',
                                            },
                                            {
                                                label: 'translate_date_of_marriage',
                                            },
                                            {
                                                label: 'translate_marriage_certificate',
                                            },
                                            {
                                                label: 'translate_has_house_allowance',
                                            },
                                            {
                                                label: 'translate_is_working',
                                            },
                                            {
                                                label: 'translate_service_request',
                                            },
                                        ],

                                        valueConfigFn: () => [
                                            {
                                                value: spouse.name,
                                            },
                                            {
                                                value: spouse.nationality?.name,
                                            },
                                            {
                                                value: spouse.dateOfBirth,
                                                type: 'date',
                                            },
                                            {
                                                value: spouse.marriageStatus,
                                                type: 'id_to_name',
                                                idToName$:
                                                    employeeSpousesService.marriageStatuses(),
                                            },
                                            {
                                                value: spouse.dateOfMarriage,
                                                type: 'date',
                                            },
                                            spouse.marriageCertificateFile
                                                ? {
                                                      value: spouse.marriageCertificateFile,
                                                      type: 'file',
                                                      downloader: () =>
                                                          employeeSpousesService.downloadCertificate(
                                                              spouse.id,
                                                          ),
                                                  }
                                                : { value: '-' },
                                            {
                                                value: spouse.hasHouseAllowance,
                                                type: 'boolean',
                                            },
                                            {
                                                value: spouse.isWorking,
                                                type: 'boolean',
                                            },
                                            {
                                                value: spouse.serviceRequest
                                                    ? 'translate_go_to_request_details'
                                                    : 'translate_no_request',
                                                linkConfig:
                                                    spouse.serviceRequest && {
                                                        permissionId:
                                                            p.servicing.requests
                                                                .read,
                                                        value: [
                                                            '',
                                                            'servicing',
                                                            'requests',
                                                            spouse
                                                                .serviceRequest
                                                                ?.id,
                                                        ],
                                                    },
                                            },
                                        ],
                                    },
                                ];
                            },
                        };
                    },
                },
            },
        ],
    };
};
