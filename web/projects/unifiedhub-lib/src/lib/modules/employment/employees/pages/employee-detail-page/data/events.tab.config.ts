import {
    Employee,
    EmployeeEvent,
    EmployeeEventFilterAttrs,
} from '../../../../core';
import { EmployeeEventsService } from '../../../../employee-events.service';
import {
    DynamicFormConfig,
    TabListSectionConfigTab,
} from '../../../../../../features';
import { p } from '../../../../../../permissions';
import { Injector } from '@angular/core';

export const eventsTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const employeeEventsService = injector.get(EmployeeEventsService);

    return {
        name: 'translate_employee_events',

        sectionConfigs: [
            {
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'full',
                        columnConfigFn: () => [
                            {
                                label: 'translate_time',
                            },

                            {
                                label: 'translate_type',
                            },

                            {
                                label: 'translate_details',
                            },

                            {
                                label: 'translate_reference_number',
                            },

                            {
                                label: 'translate_attachment',
                            },
                        ],
                        valueConfigFn: tmp => {
                            const item = tmp as EmployeeEvent;
                            return [
                                {
                                    value: item.time,
                                    type: 'date',
                                },

                                {
                                    value: item.type,
                                    type: 'id_to_name',
                                    idToName$: employeeEventsService.types(),
                                },

                                {
                                    value: item.details,
                                },

                                {
                                    value: item.referenceNumber,
                                },

                                {
                                    value: '',
                                },
                            ];
                        },
                        rowConfigFn: () => {
                            return {
                                editButtonConfig: {
                                    permissionId: p.employees.write,
                                },
                                deleteButtonConfig: {
                                    permissionId: p.employees.write,
                                },
                            };
                        },

                        filter: [
                            {
                                id: 'from',
                                type: 'datetime',
                                label: 'translate_from',
                            },

                            {
                                id: 'to',
                                type: 'datetime',
                                label: 'translate_to',
                            },

                            {
                                id: 'types',
                                type: 'select',
                                items$: employeeEventsService.types(),
                                bindLabel: 'name',
                                bindValue: 'id',
                                label: 'translate_type',
                                isMulti: true,
                            },
                        ],

                        defaultFilter: {
                            employeeIds: employee ? [employee.id] : [],
                            filterCountExclusions: ['employee_ids'],
                        } satisfies EmployeeEventFilterAttrs,

                        fetcher: employeeEventsService.list.bind(
                            employeeEventsService,
                        ),

                        deleter: employeeEventsService.delete.bind(
                            employeeEventsService,
                        ),
                    },

                    form: {
                        newTitle: 'translate_add_new_employee_event',
                        updateTitle: 'translate_update_employee_event_details',
                        fetcher: id => employeeEventsService.get(id),
                        updater: (id, item) =>
                            employeeEventsService.update(
                                id,
                                item as EmployeeEvent,
                            ),
                        creator: item =>
                            employeeEventsService.create(item as EmployeeEvent),
                        configFn: mode => [
                            ...(mode === 'new'
                                ? [
                                      {
                                          id: 'employee',
                                          type: 'hidden',
                                          defaultValue: employee,
                                          required: true,
                                      } satisfies DynamicFormConfig,
                                  ]
                                : []),

                            {
                                id: 'type',
                                label: 'translate_type',
                                type: 'select',
                                required: true,
                                props: {
                                    items$: employeeEventsService.types(),
                                    bindValue: 'id',
                                    bindLabel: 'name',
                                },
                            },

                            {
                                id: 'time',
                                label: 'translate_event_time',
                                type: 'datetime',
                                required: true,
                            },

                            {
                                id: 'details',
                                type: 'multilingualTextInput',
                                label: 'translate_details',
                                props: {
                                    type: 'long',
                                },
                            },

                            {
                                id: 'referenceNumber',
                                type: 'input',
                                label: 'translate_reference_number',
                            },
                        ],
                    },
                },
            },
        ],
    };
};
