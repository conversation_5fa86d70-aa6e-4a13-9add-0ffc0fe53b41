import {
    CourseEmployee,
    CourseEmployeeFilterAttrs,
} from '../../../../../courses-management/core';
import { Injector } from '@angular/core';
import { TabListSectionConfigTab } from '../../../../../../features';
import { p } from '../../../../../../permissions';
import { map } from 'rxjs';
import { CoursesService } from '../../../../../courses-management/courses/courses.service';
import { Employee } from '../../../../core';
import { CourseEmployeesService } from '../../../../../courses-management/courses/course-employees.service';

export const courseList = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const courseEmployeesService = injector.get(CourseEmployeesService);

    return {
        name: 'translate_courses',
        permissionId: p.courses.read,
        sectionConfigs: [
            {
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'full',
                        columnConfigFn: () => [
                            { label: 'translate_course' },
                            { label: 'translate_type' },
                            { label: 'translate_course_major' },
                            { label: 'translate_from' },
                            { label: 'translate_to' },
                            { label: 'translate_course_result' },
                        ],
                        valueConfigFn: tmp => {
                            const item = tmp as CourseEmployee;

                            return [
                                {
                                    value: item.course.name,
                                    linkConfig: {
                                        value: ['', 'courses', item.course.id],
                                        permissionId: p.courses.read,
                                        shouldOpenInNewTab: true,
                                    },
                                },

                                {
                                    value: item.course?.type?.name,
                                },
                                {
                                    value: item.course?.major?.name,
                                },
                                {
                                    value: item.course?.startDate,
                                    type: 'date',
                                },
                                {
                                    value: item.course?.endDate,
                                    type: 'date',
                                },
                                {
                                    value: item.result?.name,
                                },
                            ];
                        },
                        filter: [
                            {
                                id: 'keyword',
                                type: 'text',
                                label: 'translate_search_by_name',
                            },

                            {
                                id: 'courseIds',
                                type: 'select',
                                loaderFetcher: keyword =>
                                    injector
                                        .get(CoursesService)
                                        .list({
                                            attrs: { keyword },
                                            pageSize: 20,
                                        })
                                        .pipe(map(x => x.items)),
                                bindLabel: 'name',
                                bindValue: 'id',
                                label: 'translate_course',
                                isMulti: true,
                            },
                        ],

                        defaultFilter: {
                            employeeIds: employee ? [employee.id] : [],
                            filterCountExclusions: ['employee_ids'],
                        } satisfies CourseEmployeeFilterAttrs,

                        fetcher: courseEmployeesService.list.bind(
                            courseEmployeesService,
                        ),
                    },

                    detail: item => {
                        return {
                            title: 'translate_course_details',
                            loader: () => courseEmployeesService.get(item.id),
                            sectionConfigs: item => {
                                const courseEmployee = item as CourseEmployee;
                                return [
                                    {
                                        type: 'detail',
                                        noContainer: true,
                                        fieldConfigFn: () => [
                                            { label: 'translate_course' },
                                            { label: 'translate_type' },
                                            { label: 'translate_course_major' },
                                            { label: 'translate_from' },
                                            { label: 'translate_to' },
                                            {
                                                label: 'translate_course_result',
                                            },
                                        ],

                                        valueConfigFn: () => [
                                            {
                                                value: courseEmployee.course
                                                    .name,
                                                linkConfig: {
                                                    value: [
                                                        '',
                                                        'courses',
                                                        courseEmployee.course
                                                            .id,
                                                    ],
                                                    permissionId:
                                                        p.courses.read,
                                                    shouldOpenInNewTab: true,
                                                },
                                            },

                                            {
                                                value: courseEmployee.course
                                                    ?.type?.name,
                                            },
                                            {
                                                value: courseEmployee.course
                                                    ?.major?.name,
                                            },
                                            {
                                                value: courseEmployee.course
                                                    ?.startDate,
                                                type: 'date',
                                            },
                                            {
                                                value: courseEmployee.course
                                                    ?.endDate,
                                                type: 'date',
                                            },
                                            {
                                                value: courseEmployee.result
                                                    ?.name,
                                            },
                                        ],
                                    },
                                ];
                            },
                        };
                    },
                },
            },
        ],
    };
};
