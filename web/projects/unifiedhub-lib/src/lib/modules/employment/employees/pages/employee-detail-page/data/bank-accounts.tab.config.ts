import { Injector } from '@angular/core';
import {
    DynamicFormConfig,
    TabListSectionConfigTab,
} from '../../../../../../features';
import { p } from '../../../../../../permissions';
import {
    Employee,
    EmployeeBankAccount,
    EmployeeBankAccountFilterAttrs,
} from '../../../../core';
import { EmployeeBankAccountsService } from '../../../employee-bank-accounts.service';
import { BanksService } from '../../../../../foundation/banks/banks.service';
import { finalize, map } from 'rxjs';

export const bankAccountsTab = (
    employee: Employee | null,
    injector: Injector,
): TabListSectionConfigTab<Employee> => {
    const employeeBankAccountsService = injector.get(
        EmployeeBankAccountsService,
    );

    return {
        name: 'translate_bank_accounts',
        permissionId: p.employees.read,
        sectionConfigs: [
            {
                type: 'list_with_create',
                noContainer: true,
                config: {
                    list: {
                        type: 'full',
                        columnConfigFn: () => [
                            { label: 'translate_bank' },
                            { label: 'translate_iban' },
                            { label: 'translate_change_date' },
                            { label: 'translate_file' },
                            { label: 'translate_is_default' },
                        ],
                        valueConfigFn: tmp => {
                            const item = tmp as EmployeeBankAccount;

                            return [
                                {
                                    value: item.bank.name,
                                },
                                {
                                    value: item.iban,
                                },
                                {
                                    value: item.changeDate,
                                    type: 'date',
                                },
                                {
                                    value: item.bankAttachmentFile,
                                    downloader: () =>
                                        employeeBankAccountsService.download(
                                            item.id,
                                        ),
                                    type: 'file',
                                },
                                {
                                    value: item.isDefault,
                                    type: 'boolean',
                                },
                            ];
                        },
                        rowConfigFn: () => {
                            return {
                                editButtonConfig: {
                                    permissionId: p.employees.write,
                                },
                                deleteButtonConfig: {
                                    permissionId: p.employees.write,
                                },
                            };
                        },
                        actionsFn: (tmp, currentlyProcessing, __, reload) => {
                            const item = tmp as EmployeeBankAccount;
                            return item.isDefault
                                ? []
                                : [
                                      {
                                          type: 'success',
                                          iconClasses: 'fa fa-star',
                                          isDisabledFn: () =>
                                              currentlyProcessing.value.has(
                                                  item,
                                              ),
                                          onClickFn: () => {
                                              currentlyProcessing.signal.update(
                                                  x => new Set(x.add(item)),
                                              );
                                              employeeBankAccountsService
                                                  .setAsDefault(item.id)
                                                  .pipe(
                                                      finalize(() =>
                                                          currentlyProcessing.signal.update(
                                                              x => {
                                                                  x.delete(
                                                                      item,
                                                                  );
                                                                  return new Set(
                                                                      x,
                                                                  );
                                                              },
                                                          ),
                                                      ),
                                                  )
                                                  .subscribe(() => {
                                                      reload();
                                                  });
                                          },
                                      },
                                  ];
                        },

                        defaultFilter: {
                            employeeIds: employee ? [employee.id] : [],
                            filterCountExclusions: ['employee_ids'],
                        } satisfies EmployeeBankAccountFilterAttrs,

                        fetcher: employeeBankAccountsService.list.bind(
                            employeeBankAccountsService,
                        ),
                        deleter: employeeBankAccountsService.delete.bind(
                            employeeBankAccountsService,
                        ),
                    },

                    form: {
                        newTitle: 'translate_add_new_bank_account',
                        updateTitle: 'translate_update_bank_account_details',
                        fetcher: id => employeeBankAccountsService.get(id),
                        updater: (id, item) =>
                            employeeBankAccountsService.update(
                                id,
                                item as EmployeeBankAccount,
                            ),
                        creator: item =>
                            employeeBankAccountsService.create(
                                item as EmployeeBankAccount,
                            ),
                        configFn: mode => [
                            ...(mode === 'new'
                                ? [
                                      {
                                          id: 'employee',
                                          type: 'hidden',
                                          defaultValue: employee,
                                          required: true,
                                      } satisfies DynamicFormConfig,
                                  ]
                                : []),

                            {
                                id: 'bank',
                                label: 'translate_bank',
                                type: 'select',
                                required: true,
                                disabled: mode === 'edit',
                                props: {
                                    loaderFetcher: keyword =>
                                        injector
                                            .get(BanksService)
                                            .list({ attrs: { keyword } })
                                            .pipe(map(x => x.items)),
                                    bindLabel: 'name',
                                },
                            },

                            {
                                id: 'iban',
                                type: 'input',
                                label: 'translate_iban',
                                required: true,
                            },

                            {
                                id: 'changeDate',
                                type: 'datetime',
                                label: 'translate_change_date',
                            },

                            {
                                id: 'bankAttachmentFile',
                                type: 'file',
                                label: 'translate_bank_attachment_file',
                            },
                        ],
                    },
                },
            },
        ],
    };
};
