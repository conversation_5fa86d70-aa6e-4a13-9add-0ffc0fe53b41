import { Employee } from '../../../../core';
import { RowSectionConfigSection } from '../../../../../../features';

export const additionalInfoDetail = (
    item: Employee | null,
): RowSectionConfigSection<Employee> => ({
    title: 'translate_additional_info',
    type: 'detail',
    fieldConfigFn: () => [
        {
            label: 'translate_city',
        },
        {
            label: 'translate_address',
        },
        {
            label: 'translate_phone_number',
        },
        {
            label: 'translate_national_id_number',
        },
        {
            label: 'translate_national_id_expiration_date',
        },
        {
            label: 'translate_passport_number',
        },
        {
            label: 'translate_passport_expiration_date',
        },
        {
            label: 'translate_passport_unified_number',
        },
        {
            label: 'translate_passport_issue_authority',
        },
        {
            label: 'translate_family_book_number',
        },
        {
            label: 'translate_family_book_issuing_city',
        },
        {
            label: 'translate_family_book_issuing_suburb',
        },
        {
            label: 'translate_residency_number',
            isHidden:
                item?.nationality?.category === 'citizen' ||
                item?.nationality?.category === 'arabian_gulf',
        },
        {
            label: 'translate_residency_expiration_date',
            isHidden:
                item?.nationality?.category === 'citizen' ||
                item?.nationality?.category === 'arabian_gulf',
        },
    ],
    valueConfigFn: () => [
        {
            value: item?.city?.name,
        },
        {
            value: item?.address,
        },
        {
            value: item?.phoneNumber,
        },
        {
            value: item?.nationalIdNumber,
        },
        {
            value: item?.nationalIdExpirationDate,
            type: 'date',
        },
        {
            value: item?.passportNumber,
        },
        {
            value: item?.passportExpirationDate,
            type: 'date',
        },
        {
            value: item?.passportUnifiedNumber,
        },
        {
            value: item?.passportIssueAuthority,
        },
        {
            value: item?.familyBookNumber,
        },
        {
            value: item?.familyBookIssuingCity?.name,
        },
        {
            value: item?.familyBookIssuingSuburb?.name,
        },
        {
            value: item?.residencyNumber,
        },
        {
            value: item?.residencyExpirationDate,
            type: 'date',
        },
    ],
});
