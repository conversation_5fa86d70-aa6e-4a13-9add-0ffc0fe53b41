import { JobLevelsService } from '../../job-levels.service';
import { JobLevel } from '../../../core';
import { JobClassificationsService } from '../../../job-classifications/job-classifications.service';
import { map } from 'rxjs';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    jobLevelsService: JobLevelsService,
    jobClassificationsService: JobClassificationsService,
): NewPageConfigFn<JobLevel> => {
    return mode => ({
        title: 'translate_job_levels',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_job_level'
                : 'translate_update_job_level_details',
        newFormConfig: {
            loader: jobLevelsService.get.bind(jobLevelsService),
            creator: jobLevelsService.create.bind(jobLevelsService),
            editor: jobLevelsService.update.bind(jobLevelsService),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                            size: 2,
                        },
                        {
                            id: 'order',
                            label: 'translate_order',
                            type: 'input',
                            size: 1,
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'description',
                            type: 'multilingualTextInput',
                            label: 'translate_description',
                            props: {
                                type: 'long',
                            },
                            size: 2,
                        },

                        {
                            id: 'classification',
                            type: 'select',
                            label: 'translate_job_classification',
                            required: true,
                            props: {
                                bindLabel: 'name',
                                compareWith: (a, b) => a.id === b.id,
                                items$: jobClassificationsService
                                    .list({
                                        pageNumber: 0,
                                        pageSize: -1,
                                    })
                                    .pipe(map(x => x.items)),
                            },
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'leaveConfig.annualLeaveConfig.maxPerRequestDurationInDays',
                            type: 'input',
                            label: 'translate_annual_leave_max_per_request_duration_in_days',
                            props: {
                                type: 'number',
                            },
                        },

                        {
                            id: 'leaveConfig.annualLeaveConfig.maxPerYearDurationInDays',
                            type: 'input',
                            label: 'translate_annual_leave_max_per_year_duration_in_days',
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'leaveConfig.annualLeaveConfig.isBalanceCreditMonthlyIncremented',
                            type: 'input',
                            label: 'translate_is_balance_credit_monthly_incremented',
                            props: {
                                type: 'checkbox',
                            },
                        },

                        {
                            id: 'leaveConfig.annualLeaveConfig.balanceCreditPerYear',
                            type: 'input',
                            label: 'translate_balance_credit_per_year',
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },
            ],
        },
    });
};
