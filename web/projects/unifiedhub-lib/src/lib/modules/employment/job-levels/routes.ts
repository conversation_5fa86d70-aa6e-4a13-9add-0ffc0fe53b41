import { Routes } from '@angular/router';
import { JobLevelsComponent } from './job-levels.component';
import { JobLevelListPageComponent } from './pages/job-level-list-page/job-level-list-page.component';
import { JobLevelNewPageComponent } from './pages/job-level-new-page/job-level-new-page.component';
import { JobLevelDetailComponent } from './pages/job-level-detail-page/job-level-detail.component';

export const routes = [
    {
        path: '',
        component: JobLevelsComponent,
        children: [
            {
                path: '',
                component: JobLevelListPageComponent,
                data: {
                    title: 'translate_job_levels',
                },
            },

            {
                path: 'new',
                component: JobLevelNewPageComponent,
                data: {
                    title: 'translate_add_new_job_level',
                },
            },

            {
                path: 'edit/:id',
                component: JobLevelNewPageComponent,
                data: {
                    title: 'translate_update_job_level_details',
                },
            },

            {
                path: ':id',
                component: JobLevelDetailComponent,
                data: {
                    title: 'translate_job_level_details',
                },
            },
        ],
    },
] satisfies Routes;
