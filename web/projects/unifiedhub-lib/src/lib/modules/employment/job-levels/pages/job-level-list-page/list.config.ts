import { JobLevelsService } from '../../job-levels.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { JobLevel } from '../../../core';

export const config = (
    jobLevelsService: JobLevelsService,
): DynamicListPageConfig<JobLevel, { keyword?: string }> => {
    return {
        title: 'translate_job_levels',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'employees', 'job-levels', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: jobLevelsService.list.bind(jobLevelsService),
            deleter: jobLevelsService.delete.bind(jobLevelsService),
        },

        newButtonConfig: {
            permissionId: p.employees.manageLists,
        },
    };
};
