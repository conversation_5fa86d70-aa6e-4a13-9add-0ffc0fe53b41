import { JobLevelsService } from '../../job-levels.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { JobLevel } from '../../../core';

export const config = (
    jobLevelsService: JobLevelsService,
): DetailPageConfig<JobLevel> => {
    return {
        loader: jobLevelsService.get.bind(jobLevelsService),
        config: item => ({
            title: 'translate_job_levels',
            subtitle: 'translate_job_level_details',
            editButtonConfig: {
                permissionId: p.employees.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_job_level_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_order',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.order,
                        },
                    ],
                },
            ],
        }),
    };
};
