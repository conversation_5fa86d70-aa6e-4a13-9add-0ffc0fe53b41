import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { JobLevelsService } from './job-levels.service';
import { JobClassificationsService } from '../job-classifications/job-classifications.service';

@Component({
    selector: 'lib-job-levels',
    template: '<router-outlet/>',
    standalone: true,
    providers: [JobLevelsService, JobClassificationsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class JobLevelsComponent {}
