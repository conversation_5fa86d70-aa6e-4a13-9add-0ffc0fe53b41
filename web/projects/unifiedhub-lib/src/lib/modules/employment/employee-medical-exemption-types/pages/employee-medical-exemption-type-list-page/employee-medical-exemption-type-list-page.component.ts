import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { EmployeeMedicalExemptionTypesService } from '../../employee-medical-exemption-types.service';

@Component({
    selector: 'lib-employee-medical-exemption-type-list-page',
    templateUrl: './employee-medical-exemption-type-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class EmployeeMedicalExemptionTypeListPageComponent {
    protected config = config(inject(EmployeeMedicalExemptionTypesService));
}
