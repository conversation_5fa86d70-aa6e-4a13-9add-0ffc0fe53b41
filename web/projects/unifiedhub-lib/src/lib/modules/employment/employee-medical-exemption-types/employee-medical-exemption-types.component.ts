import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { EmployeeMedicalExemptionTypesService } from './employee-medical-exemption-types.service';

@Component({
    selector: 'lib-employee-medical-exemption-types',
    template: '<router-outlet/>',
    standalone: true,
    providers: [EmployeeMedicalExemptionTypesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmployeeMedicalExemptionTypesComponent {}
