import { Routes } from '@angular/router';
import { EmployeeMedicalExemptionTypesComponent } from './employee-medical-exemption-types.component';
import { EmployeeMedicalExemptionTypeListPageComponent } from './pages/employee-medical-exemption-type-list-page/employee-medical-exemption-type-list-page.component';
import { EmployeeMedicalExemptionTypeNewPageComponent } from './pages/employee-medical-exemption-type-new-page/employee-medical-exemption-type-new-page.component';
import { EmployeeMedicalExemptionTypeDetailComponent } from './pages/employee-medical-exemption-type-detail-page/employee-medical-exemption-type-detail.component';

export const routes = [
    {
        path: '',
        component: EmployeeMedicalExemptionTypesComponent,
        children: [
            {
                path: '',
                component: EmployeeMedicalExemptionTypeListPageComponent,
                data: {
                    title: 'translate_medical_exemption_types',
                },
            },

            {
                path: 'new',
                component: EmployeeMedicalExemptionTypeNewPageComponent,
                data: {
                    title: 'translate_add_new_medical_exemption_type',
                },
            },

            {
                path: 'edit/:id',
                component: EmployeeMedicalExemptionTypeNewPageComponent,
                data: {
                    title: 'translate_update_medical_exemption_type_details',
                },
            },

            {
                path: ':id',
                component: EmployeeMedicalExemptionTypeDetailComponent,
                data: {
                    title: 'translate_medical_exemption_type_details',
                },
            },
        ],
    },
] satisfies Routes;
