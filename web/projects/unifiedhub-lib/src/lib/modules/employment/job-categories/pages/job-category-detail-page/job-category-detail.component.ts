import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { JobCategoriesService } from '../../job-categories.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-job-category-detail-page',
    templateUrl: './job-category-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class JobCategoryDetailComponent {
    protected readonly config = config(inject(JobCategoriesService));
}
