import { JobCategoriesService } from '../../job-categories.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { JobCategory } from '../../../core';

export const config = (
    jobCategoriesService: JobCategoriesService,
): DetailPageConfig<JobCategory> => {
    return {
        loader: jobCategoriesService.get.bind(jobCategoriesService),
        config: item => ({
            title: 'translate_job_categories',
            subtitle: 'translate_job_category_details',
            editButtonConfig: {
                permissionId: p.employees.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_job_category_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_order',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.order,
                        },
                    ],
                },
            ],
        }),
    };
};
