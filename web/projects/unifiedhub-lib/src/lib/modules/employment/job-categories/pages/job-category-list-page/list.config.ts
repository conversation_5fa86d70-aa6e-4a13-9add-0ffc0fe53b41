import { JobCategoriesService } from '../../job-categories.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { JobCategory } from '../../../core';

export const config = (
    jobCategoriesService: JobCategoriesService,
): DynamicListPageConfig<JobCategory, { keyword?: string }> => {
    return {
        title: 'translate_job_categories',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'employees', 'job-categories', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: jobCategoriesService.list.bind(jobCategoriesService),
            deleter: jobCategoriesService.delete.bind(jobCategoriesService),
        },

        newButtonConfig: {
            permissionId: p.employees.manageLists,
        },
    };
};
