import { File, MultilingualString } from '../../../../common';
import { Employee } from './employee.type';
import { ServiceRequest } from '../../../servicing';
import { Country } from '../../../foundation/core';

export type EmployeeSpouse = {
    id: string;
    name: MultilingualString;
    employee: Employee;
    gender?: string;
    idNumber?: string;
    nationality?: Country;
    dateOfBirth?: Date;
    placeOfBirth?: string;
    dateOfMarriage?: Date;
    placeOfMarriage?: string;
    marriageStatus: string;
    marriageCertificateFile?: File;
    notes?: string;

    isWorking?: boolean;
    jobTitle?: string;
    workAddress?: string;
    placeOfWork?: string;
    hireDate?: Date;
    workNocFile?: File;

    hasHouseAllowance: Boolean;
    houseAllowanceAmount?: string;
    allowanceStartDate?: Date;

    serviceRequest?: ServiceRequest;
};
