import { Employee } from './employee.type';
import { File, MultilingualString } from '../../../../common';

export type EmployeeDocument = {
    id: string;
    name: MultilingualString;
    number?: string;
    unifiedNumber?: string;
    familyBookNumber?: string;
    employee: Employee;
    type: string;
    issueDate?: Date;
    issuePlace?: string;
    expirationDate?: Date;
    notes?: string;
    file?: File;
};
