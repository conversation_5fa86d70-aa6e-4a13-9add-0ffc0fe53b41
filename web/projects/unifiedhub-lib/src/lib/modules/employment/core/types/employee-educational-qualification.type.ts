import { File } from '../../../../common';
import { Employee } from './employee.type';
import {
    EducationalMajor,
    EducationalQualification,
    EducationalSpecialization,
} from '../../../foundation/core';

export type EmployeeEducationalQualification = {
    id: string;
    employee: Employee;
    qualification: EducationalQualification;
    major?: EducationalMajor;
    specialization?: EducationalSpecialization;
    commencementDate?: Date;
    graduationDate?: Date;
    grade?: string;
    score?: string;
    institution?: string;
    placeOfStudy?: string;
    attachment?: File;
};
