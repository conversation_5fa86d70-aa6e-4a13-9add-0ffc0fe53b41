import { Employee } from './employee.type';
import { EmployeePromotionType } from './employee-promotion-type.type';
import { JobLevel } from './job-level.type';
import { File } from '../../../../common';

export type EmployeeJobLevelChange = {
    id: string;
    employee: Employee;
    type: 'promotion' | 'demotion' | 'stripping';
    promotionType?: EmployeePromotionType;
    reason?: string;
    referenceNumber?: string;
    referenceDate?: Date;
    referenceFile: File;
    changeDate: Date;
    currentJobLevel: JobLevel;
    newJobLevel: JobLevel;
    details?: string;
    attachment?: File;
};
