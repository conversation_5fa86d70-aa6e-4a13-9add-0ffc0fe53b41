import { File } from '../../../../common';
import { Employee } from './employee.type';
import { EmployeeViolationType } from './employee-violation-type.type';
import { EmployeePenalty } from './employee-penalty.type';

export type EmployeeViolationEvent = {
    id: string;
    employee: Employee;
    violationTime: Date;
    decisionTime?: Date;
    violationType: EmployeeViolationType;
    decisionMaker?: string;
    notes?: string;
    penalties: EmployeePenalty[];
    files: File[];
};
