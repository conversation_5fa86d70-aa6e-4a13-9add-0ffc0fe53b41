import { JobTitlesService } from '../../job-titles/job-titles.service';
import { JobCategoriesService } from '../../job-categories/job-categories.service';
import { MilitaryStatusesService } from '../../military-statuses/military-statuses.service';
import { EducationalQualificationsService } from '../../../foundation/educational-qualifications/educational-qualifications.service';
import { CountriesService } from '../../../foundation/countries/countries.service';
import { JobClassificationsService } from '../../job-classifications/job-classifications.service';
import { Provider } from '@angular/core';
import { MiscService } from '../../employees/misc.service';
import { DepartmentsService } from '../../../departments/departments.service';
import { JobLevelsService } from '../../job-levels/job-levels.service';

export const getEmployeeListServices = (): Provider[] => {
    return [
        JobTitlesService,
        JobCategoriesService,
        MilitaryStatusesService,
        EducationalQualificationsService,
        CountriesService,
        JobClassificationsService,
        JobLevelsService,
        DepartmentsService,
        MiscService,
    ];
};
