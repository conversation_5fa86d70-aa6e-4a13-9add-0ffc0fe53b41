import { Inject, Injectable } from '@angular/core';
import { EmployeePenalty } from './core';
import { HttpClient } from '@angular/common/http';
import { CrudService } from '../../features';
import { APP_CONFIG, AppConfig } from '../../config';

@Injectable()
export class EmployeePenaltiesService extends CrudService<
    EmployeePenalty,
    { keyword?: string }
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    protected getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/penalties`;
    }
}
