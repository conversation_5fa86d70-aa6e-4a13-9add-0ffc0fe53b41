import { Routes } from '@angular/router';
import { JobClassificationsComponent } from './job-classifications.component';
import { JobClassificationListPageComponent } from './pages/job-classification-list-page/job-classification-list-page.component';
import { JobClassificationNewPageComponent } from './pages/job-classification-new-page/job-classification-new-page.component';
import { JobClassificationDetailComponent } from './pages/job-classification-detail-page/job-classification-detail.component';

export const routes = [
    {
        path: '',
        component: JobClassificationsComponent,
        children: [
            {
                path: '',
                component: JobClassificationListPageComponent,
                data: {
                    title: 'translate_job_classifications',
                },
            },

            {
                path: 'new',
                component: JobClassificationNewPageComponent,
                data: {
                    title: 'translate_add_new_job_classification',
                },
            },

            {
                path: 'edit/:id',
                component: JobClassificationNewPageComponent,
                data: {
                    title: 'translate_update_job_classification_details',
                },
            },

            {
                path: ':id',
                component: JobClassificationDetailComponent,
                data: {
                    title: 'translate_job_classification_details',
                },
            },
        ],
    },
] satisfies Routes;
