import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { JobClassificationsService } from '../../job-classifications.service';

@Component({
    selector: 'lib-job-classification-list-page',
    templateUrl: './job-classification-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class JobClassificationListPageComponent {
    protected config = config(inject(JobClassificationsService));
}
