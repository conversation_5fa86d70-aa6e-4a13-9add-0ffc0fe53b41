import { JobClassificationsService } from '../../job-classifications.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { JobClassification } from '../../../core';

export const config = (
    jobClassificationsService: JobClassificationsService,
): DynamicListPageConfig<JobClassification, { keyword?: string }> => {
    return {
        title: 'translate_job_classifications',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: [
                            '',
                            'employees',
                            'job-classifications',
                            item.id,
                        ],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.employees.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: jobClassificationsService.list.bind(
                jobClassificationsService,
            ),
            deleter: jobClassificationsService.delete.bind(
                jobClassificationsService,
            ),
        },

        newButtonConfig: {
            permissionId: p.employees.manageLists,
        },
    };
};
