import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { EmploymentTypesService } from '../../employment-types.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-employment-type-detail-page',
    templateUrl: './employment-type-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class EmploymentTypeDetailComponent {
    protected readonly config = config(inject(EmploymentTypesService));
}
