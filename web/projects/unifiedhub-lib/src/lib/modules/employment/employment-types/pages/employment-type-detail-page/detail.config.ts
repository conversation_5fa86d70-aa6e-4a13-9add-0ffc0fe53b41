import { EmploymentTypesService } from '../../employment-types.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { EmploymentType } from '../../../core';

export const config = (
    employmentTypesService: EmploymentTypesService,
): DetailPageConfig<EmploymentType> => {
    return {
        loader: employmentTypesService.get.bind(employmentTypesService),
        config: item => ({
            title: 'translate_employment_types',
            subtitle: 'translate_employment_type_details',
            editButtonConfig: {
                permissionId: p.employees.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_employment_type_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_order',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.order,
                        },
                    ],
                },
            ],
        }),
    };
};
