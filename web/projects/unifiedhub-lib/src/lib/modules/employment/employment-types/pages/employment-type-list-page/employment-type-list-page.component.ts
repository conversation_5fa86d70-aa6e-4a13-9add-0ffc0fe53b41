import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { EmploymentTypesService } from '../../employment-types.service';

@Component({
    selector: 'lib-employment-type-list-page',
    templateUrl: './employment-type-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class EmploymentTypeListPageComponent {
    protected config = config(inject(EmploymentTypesService));
}
