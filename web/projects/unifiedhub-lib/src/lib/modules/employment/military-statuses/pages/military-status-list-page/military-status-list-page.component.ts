import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { MilitaryStatusesService } from '../../military-statuses.service';

@Component({
    selector: 'lib-military-status-list-page',
    templateUrl: './military-status-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class MilitaryStatusListPageComponent {
    protected config = config(inject(MilitaryStatusesService));
}
