import { Routes } from '@angular/router';
import { MilitaryStatusesComponent } from './military-statuses.component';
import { MilitaryStatusListPageComponent } from './pages/military-status-list-page/military-status-list-page.component';
import { MilitaryStatusNewPageComponent } from './pages/military-status-new-page/military-status-new-page.component';
import { MilitaryStatusDetailComponent } from './pages/military-status-detail-page/military-status-detail.component';

export const routes = [
    {
        path: '',
        component: MilitaryStatusesComponent,
        children: [
            {
                path: '',
                component: MilitaryStatusListPageComponent,
                data: {
                    title: 'translate_military_statuses',
                },
            },

            {
                path: 'new',
                component: MilitaryStatusNewPageComponent,
                data: {
                    title: 'translate_add_new_military_status',
                },
            },

            {
                path: 'edit/:id',
                component: MilitaryStatusNewPageComponent,
                data: {
                    title: 'translate_update_military_status_details',
                },
            },

            {
                path: ':id',
                component: MilitaryStatusDetailComponent,
                data: {
                    title: 'translate_military_status_details',
                },
            },
        ],
    },
] satisfies Routes;
