import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { MilitaryStatusesService } from '../../military-statuses.service';

@Component({
    selector: 'lib-military-status-new-page',
    templateUrl: './military-status-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        RouterLink,
        DynamicNewPageComponent,
    ],
})
export class MilitaryStatusNewPageComponent {
    protected configFn = configFn(inject(MilitaryStatusesService));
}
