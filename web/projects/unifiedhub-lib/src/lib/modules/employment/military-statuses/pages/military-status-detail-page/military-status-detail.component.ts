import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { MilitaryStatusesService } from '../../military-statuses.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-military-status-detail-page',
    templateUrl: './military-status-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class MilitaryStatusDetailComponent {
    protected readonly config = config(inject(MilitaryStatusesService));
}
