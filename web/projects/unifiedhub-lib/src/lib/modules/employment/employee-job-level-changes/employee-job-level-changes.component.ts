import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { EmployeeJobLevelChangesService } from './employee-job-level-changes.service';
import { EmployeePromotionTypesService } from '../employee-promotion-types.service';
import { EmployeesService } from '../employees/employees.service';
import { DepartmentsService } from '../../departments/departments.service';
import { JobLevelsService } from '../job-levels/job-levels.service';

@Component({
    selector: 'lib-employee-job-level-changes',
    template: '<router-outlet/>',
    standalone: true,
    providers: [
        EmployeeJobLevelChangesService,
        EmployeesService,
        JobLevelsService,
        EmployeePromotionTypesService,
        DepartmentsService,
    ],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmployeeJobLevelChangesComponent {}
