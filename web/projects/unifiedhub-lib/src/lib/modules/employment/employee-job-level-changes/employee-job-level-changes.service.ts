import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { CrudService } from '../../../features';
import {
    EmployeeJobLevelChange,
    EmployeeJobLevelChangeFilterAttrs,
} from '../core';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import { Item } from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';

@Injectable()
export class EmployeeJobLevelChangesService extends CrudService<
    EmployeeJobLevelChange,
    EmployeeJobLevelChangeFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public downloadReference(id: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.getBaseEndpoint()}/${id}/download-reference`,
            {
                responseType: 'blob',
            },
        );
    }

    public types(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(`${this.getBaseEndpoint()}/types`, {
            context: new HttpContext().set(CACHE_INTERCEPTOR, true),
        });
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/employees/job-level-changes`;
    }
}
