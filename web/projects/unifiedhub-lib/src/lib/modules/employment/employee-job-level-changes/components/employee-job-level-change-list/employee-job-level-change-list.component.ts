import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    input,
} from '@angular/core';
import { config } from './list.config';
import { Observable } from 'rxjs';
import { DynamicListFullComponent } from '../../../../../features';
import { Filter, PaginatedResult } from '../../../../../common';
import {
    EmployeeJobLevelChange,
    EmployeeJobLevelChangeFilterAttrs,
} from '../../../core';

@Component({
    selector: 'lib-employee-job-level-change-list',
    templateUrl: './employee-job-level-change-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListFullComponent],
})
export class EmployeeJobLevelChangeListComponent {
    public readonly mode = input<'view' | 'default'>('default');
    public readonly filter = input<EmployeeJobLevelChangeFilterAttrs>();
    public readonly hiddenColumns = input<{
        employee?: boolean;
    }>();
    public readonly hiddenFilters = input<{
        employee?: boolean;
    }>();
    public readonly fetcher =
        input<
            (
                filter: Filter<EmployeeJobLevelChangeFilterAttrs>,
            ) => Observable<PaginatedResult<EmployeeJobLevelChange>>
        >();

    protected readonly config = computed(() => {
        return config(
            this.injector,
            this.mode(),
            this.filter(),
            this.fetcher(),
            this.hiddenColumns(),
            this.hiddenFilters(),
        );
    });

    public constructor(private readonly injector: Injector) {}
}
