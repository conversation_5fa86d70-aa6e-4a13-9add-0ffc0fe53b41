import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-employee-termination-new-page',
    templateUrl: './employee-termination-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        RouterLink,
        DynamicNewPageComponent,
    ],
})
export class EmployeeTerminationNewPageComponent {
    protected configFn = configFn(inject(Injector));
}
