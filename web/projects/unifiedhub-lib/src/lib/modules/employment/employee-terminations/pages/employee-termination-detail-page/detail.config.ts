import { EmployeeTerminationsService } from '../../employee-terminations.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { EmployeeTermination } from '../../../core';

export const config = (
    employeeTerminationsService: EmployeeTerminationsService,
): DetailPageConfig<EmployeeTermination> => {
    return {
        loader: employeeTerminationsService.get.bind(
            employeeTerminationsService,
        ),
        config: item => ({
            title: 'translate_employee_terminations',
            subtitle: 'translate_employee_termination_details',
            editButtonConfig: {
                permissionId: p.employees.terminations.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_employee_termination_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_employee_name',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.employee.name,
                        },
                    ],
                },
            ],
        }),
    };
};
