import { EmployeeTerminationsService } from '../../employee-terminations.service';
import { EmployeeTermination } from '../../../core';
import { Injector } from '@angular/core';
import { map } from 'rxjs';
import { NewPageConfigFn } from '../../../../../features';
import { EmployeesService } from '../../../employees/employees.service';

export const configFn = (
    injector: Injector,
): NewPageConfigFn<EmployeeTermination> => {
    const employeeTerminationsService = injector.get(
        EmployeeTerminationsService,
    );
    const employeeService = injector.get(EmployeesService);

    return mode => ({
        title: 'translate_employee_terminations',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_employee_termination'
                : 'translate_update_employee_termination_details',
        newFormConfig: {
            loader: employeeTerminationsService.get.bind(
                employeeTerminationsService,
            ),
            creator: employeeTerminationsService.create.bind(
                employeeTerminationsService,
            ),
            editor: employeeTerminationsService.update.bind(
                employeeTerminationsService,
            ),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'employee',
                            type: 'select',
                            label: 'translate_employee',
                            disabled: mode !== 'new',
                            required: true,
                            props: {
                                bindLabel: 'name',
                                loaderFetcher: keyword =>
                                    employeeService
                                        .list({
                                            attrs: {
                                                keyword,
                                                states: ['active'],
                                            },
                                            pageSize: 20,
                                        })
                                        .pipe(map(x => x.items)),
                            },
                        },
                        {
                            id: 'lastWorkDate',
                            type: 'datetime',
                            label: 'translate_last_work_date',
                            required: true,
                        },

                        {
                            id: 'type',
                            type: 'select',
                            label: 'translate_type',
                            required: true,
                            props: {
                                bindLabel: 'name',
                                items$: employeeTerminationsService.types(),
                            },
                        },

                        {
                            id: 'terminationReason',
                            type: 'select',
                            label: 'translate_termination_reason',
                            required: true,
                            props: {
                                bindLabel: 'name',
                                items$: employeeTerminationsService.reasons(),
                            },
                        },
                    ],
                },
                {
                    type: 'group',
                    config: [
                        {
                            id: 'details',
                            type: 'textarea',
                            label: 'translate_details',
                        },
                        {
                            id: 'signedRequestFile',
                            type: 'file',
                            label: 'translate_signed_request_file',
                        },
                    ],
                },
                {
                    type: 'group',
                    config: [
                        {
                            id: 'financeNotes',
                            type: 'textarea',
                            label: 'translate_finance_notes',
                        },
                        {
                            id: 'warehouseNotes',
                            type: 'textarea',
                            label: 'translate_warehouse_notes',
                        },
                    ],
                },
                {
                    type: 'group',
                    config: [
                        {
                            id: 'armoryNotes',
                            type: 'textarea',
                            label: 'translate_armory_notes',
                            size: 2,
                        },
                        {
                            id: 'isResidencyCanceled',
                            type: 'input',
                            label: 'translate_is_residency_canceled',
                            size: 1,
                            props: {
                                type: 'checkbox',
                            },
                        },
                        {
                            id: 'isHealthInsuranceCanceled',
                            type: 'input',
                            label: 'translate_is_health_insurance_canceled',
                            size: 1,
                            props: {
                                type: 'checkbox',
                            },
                        },
                    ],
                },
                {
                    type: 'group',
                    config: [
                        {
                            id: 'residencyAndHealthInsuranceCancellationNotes',
                            type: 'textarea',
                            label: 'translate_residency_and_health_insurance_cancellation_notes',
                        },
                    ],
                },
                {
                    type: 'group',
                    config: [
                        {
                            id: 'lengthOfServiceInDays',
                            type: 'input',
                            label: 'translate_length_of_service_in_days',
                            props: {
                                type: 'number',
                            },
                        },
                        {
                            id: 'endOfServiceBenefits',
                            type: 'input',
                            label: 'translate_end_of_service_benefits',
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },
            ],
        },
    });
};
