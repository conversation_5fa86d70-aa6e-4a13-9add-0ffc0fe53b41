import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { JobTitlesService } from '../../job-titles.service';
import { JobClassificationsService } from '../../../job-classifications/job-classifications.service';

@Component({
    selector: 'lib-job-title-new-page',
    templateUrl: './job-title-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        DynamicNewPageComponent,
    ],
})
export class JobTitleNewPageComponent {
    protected config = configFn(
        inject(JobTitlesService),
        inject(JobClassificationsService),
    );
}
