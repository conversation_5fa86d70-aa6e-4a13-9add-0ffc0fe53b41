import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { JobTitlesService } from '../../job-titles.service';

@Component({
    selector: 'lib-job-title-list-page',
    templateUrl: './job-title-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class JobTitleListPageComponent {
    protected config = config(inject(JobTitlesService));
}
