import { Routes } from '@angular/router';
import { RolesComponent } from './roles.component';
import { RoleListPageComponent } from './pages/role-list-page/role-list-page.component';
import { RoleNewPageComponent } from './pages/role-new-page/role-new-page.component';
import { RoleDetailPageComponent } from './pages/role-detail-page/role-detail-page.component';
import { p } from '../../permissions';

export const routes = [
    {
        path: '',
        component: RolesComponent,
        children: [
            {
                path: '',
                component: RoleListPageComponent,
                data: {
                    title: 'translate_roles',
                    permissionIds: [p.roles.read],
                },
            },

            {
                path: 'new',
                component: RoleNewPageComponent,
                data: {
                    title: 'translate_add_new_role',
                    permissionIds: [p.roles.write],
                },
            },

            {
                path: 'edit/:id',
                component: RoleNewPageComponent,
                data: {
                    title: 'translate_update_role_details',
                    permissionIds: [p.roles.write],
                },
            },

            {
                path: ':id',
                component: RoleDetailPageComponent,
                data: {
                    title: 'translate_role_details',
                    permissionIds: [p.roles.read],
                },
            },
        ],
    },
] satisfies Routes;
