import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RolesService } from './roles.service';
import { RouterOutlet } from '@angular/router';
import { PermissionsService } from './permissions.service';

@Component({
    selector: 'lib-roles',
    template: '<router-outlet/>',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [RolesService, PermissionsService],
    imports: [RouterOutlet],
})
export class RolesComponent {}
