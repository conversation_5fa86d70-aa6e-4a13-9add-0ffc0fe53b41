import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Role, RoleFilterAttrs } from './types';
import { Observable } from 'rxjs';
import { User } from '../users/core';
import { CrudService } from '../../features';
import { APP_CONFIG, AppConfig } from '../../config';
import { Filter, mapFilterToQueryParams, PaginatedResult } from '../../common';

@Injectable()
export class RolesService extends CrudService<Role, RoleFilterAttrs> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public getUsersInRole(
        id: string,
        filter: Filter<{ keyword?: string }>,
    ): Observable<PaginatedResult<User>> {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<User>>(
            `${this.getBaseEndpoint()}/${id}/users`,
            { params },
        );
    }

    public getUsersNotInRole(
        id: string,
        filter: Filter<{ keyword?: string }>,
    ): Observable<PaginatedResult<User>> {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<User>>(
            `${this.getBaseEndpoint()}/${id}/users/others`,
            { params },
        );
    }

    public addUsersToRoles(
        roleIds: string[],
        userIds: string[],
    ): Observable<any> {
        return this.httpClient.post(`${this.getBaseEndpoint()}/users/add`, {
            roleIds,
            userIds,
        });
    }

    public removeUsersFromRoles(
        roleIds: string[],
        userIds: string[],
    ): Observable<any> {
        return this.httpClient.post(`${this.getBaseEndpoint()}/users/remove`, {
            roleIds,
            userIds,
        });
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/roles`;
    }
}
