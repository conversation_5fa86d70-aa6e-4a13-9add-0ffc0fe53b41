import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { UaepassComponent } from './uaepass/uaepass.component';
import { SendResetPasswordLinkComponent } from './send-reset-password-link/send-reset-password-link.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { AuthComponent } from './auth.component';

export const routes = [
    {
        path: '',
        component: AuthComponent,
        children: [
            {
                path: '',
                redirectTo: 'login',
                pathMatch: 'full',
            },

            {
                path: 'login',
                component: LoginComponent,
                data: {
                    title: 'translate_sign_in',
                },
            },

            {
                path: 'uaepass',
                component: UaepassComponent,
                data: {
                    title: 'translate_signing_in_with_uae_pass',
                },
            },

            {
                path: 'reset-password/send',
                component: SendResetPasswordLinkComponent,
                data: {
                    title: 'translate_send_reset_password_link',
                },
            },

            {
                path: 'reset-password',
                component: ResetPasswordComponent,
                data: {
                    title: 'translate_reset_password',
                },
            },
        ],
    },
] satisfies Routes;
