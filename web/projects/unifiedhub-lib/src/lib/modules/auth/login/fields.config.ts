import { DynamicFormConfig } from '../../../features';

export const config = (): DynamicFormConfig[] => [
    {
        id: 'email',
        type: 'input',
        label: 'translate_email_address',
        required: true,

        props: {
            type: 'email',
        },
    },

    {
        id: 'password',
        type: 'input',
        label: 'translate_password',
        required: true,

        props: {
            type: 'password',
        },
    },
];
