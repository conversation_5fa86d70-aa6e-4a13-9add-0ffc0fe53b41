import {
    ChangeDetectionStrategy,
    Component,
    Inject,
    signal,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormlyModule } from '@ngx-formly/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AUTH_CONFIG, AuthConfig, UaepassButtonComponent } from '../core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { config } from './fields.config';
import { finalize } from 'rxjs';
import { LoadingButtonComponent } from '../../../ui';
import { DynamicFormComponent } from '../../../features';
import {
    AuthService,
    MultilingualStringTranslatorService,
    ToastService,
} from '../../../core';

@Component({
    selector: 'lib-login',
    templateUrl: './login.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ReactiveFormsModule,
        FormlyModule,
        UaepassButtonComponent,
        TranslateModule,
        LoadingButtonComponent,
        DynamicFormComponent,
        RouterLink,
        LoadingButtonComponent,
        DynamicFormComponent,
    ],
})
export class LoginComponent {
    protected isSubmitting = signal<boolean>(false);
    protected readonly form = signal<FormGroup>(new FormGroup({}));

    protected readonly config = config();

    public constructor(
        @Inject(AUTH_CONFIG) protected readonly authConfig: AuthConfig,
        private readonly authService: AuthService,
        private readonly toastService: ToastService,
        private readonly router: Router,
        private readonly translateService: TranslateService,
        private readonly multilingualStringTranslator: MultilingualStringTranslatorService,
        private readonly activatedRoute: ActivatedRoute,
    ) {}

    protected setForm(form: FormGroup): void {
        this.form.set(form);
        const email = this.activatedRoute.snapshot.queryParamMap.get(
            'email',
        ) as string | undefined;
        if (email) {
            form.controls['email'].setValue(email);
        }
    }

    protected submit(): void {
        if (!this.form().valid) return;

        if (this.isSubmitting()) return;

        this.isSubmitting.set(true);

        const model = this.form().getRawValue();

        this.authService
            .login(model.email, model.password)
            .pipe(finalize(() => this.isSubmitting.set(false)))
            .subscribe(identity => {
                this.translateService
                    .get('translate_successfully_logged_in_as_name', {
                        name: this.multilingualStringTranslator.get(
                            identity.user.name,
                        ),
                    })
                    .subscribe(async str => {
                        this.toastService.success(str);

                        const returnUrl =
                            this.activatedRoute.snapshot.queryParamMap.get(
                                'returnUrl',
                            );
                        if (returnUrl) {
                            await this.router.navigateByUrl(returnUrl);
                        } else {
                            await this.router.navigate(['']);
                        }
                    });
            });
    }
}
