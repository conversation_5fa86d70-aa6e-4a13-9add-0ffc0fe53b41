@if (!error()) {
    <div class="flex flex-row items-center gap-2">
        <lib-loading-ring />
        <span>
            {{ 'translate_signing_in_with_uae_pass_please_wait' | translate }}
        </span>
    </div>
} @else {
    <div class="flex flex-col gap-2">
        <lib-alert
            alertTitle="translate_an_error_has_occurred"
            alertType="danger"
            [alertDescription]="error()!"
        />
        <a class="self-end text-sm" [routerLink]="['', 'auth', 'login']">
            {{ 'translate_go_back_to_login' | translate }}
        </a>
    </div>
}
