import {
    ChangeDetectionStrategy,
    Component,
    Inject,
    OnInit,
    signal,
} from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AlertComponent, LoadingRingComponent } from '../../../../../ui';
import { APP_CONFIG, AppConfig } from '../../../../../config';

@Component({
    selector: 'lib-uaepass-redirect-handler',
    templateUrl: './uaepass-redirect-handler.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        RouterLink,
        LoadingRingComponent,
        AlertComponent,
        TranslateModule,
    ],
})
export class UaepassRedirectHandlerComponent implements OnInit {
    public static codeKey = 'UAEPASS_AUTH_CODE';
    public static errorKey = 'UAEPASS_AUTH_ERROR';

    protected error = signal<string | null>(null);

    public constructor(
        private readonly activatedRoute: ActivatedRoute,
        private readonly translateService: TranslateService,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {}

    public ngOnInit(): void {
        this.processAuthorizationCode();
    }

    private processAuthorizationCode(): void {
        const code = this.activatedRoute.snapshot.queryParamMap.get('code');
        const state = this.activatedRoute.snapshot.queryParamMap.get('state');
        const origin = new URL(this.config.uaepass.redirectUri).origin;

        if (code) {
            window.opener.postMessage(
                {
                    type: UaepassRedirectHandlerComponent.codeKey,
                    code,
                    state,
                },
                origin,
            );

            window.close();
            return;
        }

        this.translateService
            .get('translate_an_unknown_error_has_occurred')
            .subscribe(str => {
                window.opener.postMessage(
                    {
                        type: UaepassRedirectHandlerComponent.errorKey,
                        error:
                            this.activatedRoute.snapshot.queryParamMap.get(
                                'error',
                            ) ?? str,
                    },
                    origin,
                );
                window.close();
            });
    }
}
