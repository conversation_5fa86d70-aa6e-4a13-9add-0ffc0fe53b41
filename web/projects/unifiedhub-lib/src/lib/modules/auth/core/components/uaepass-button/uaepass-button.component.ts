import {
    ChangeDetectionStrategy,
    Component,
    Inject,
    OnDestroy,
    signal,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { constructUrl } from '../../../../../common';
import { APP_CONFIG, AppConfig } from '../../../../../config';
import { LoadingRingComponent } from '../../../../../ui';
import { UaepassRedirectHandlerComponent } from '../uaepass-redirect-handler/uaepass-redirect-handler.component';
import {
    AuthService,
    MultilingualStringTranslatorService,
    ToastService,
} from '../../../../../core';
import { finalize } from 'rxjs';
import { Router } from '@angular/router';
import { AUTH_CONFIG, AuthConfig } from '../../auth-config.type';

@Component({
    selector: 'lib-uaepass-button',
    templateUrl: './uaepass-button.component.html',
    styleUrl: './uaepass-button.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [LoadingRingComponent],
})
export class UaepassButtonComponent implements OnDestroy {
    protected isWaitingForWindow = signal<boolean>(false);
    protected isAuthenticating = signal<boolean>(false);

    private state: string | undefined = undefined;
    private abortController?: AbortController;

    public constructor(
        protected readonly translateService: TranslateService,
        private readonly toastService: ToastService,
        private readonly authService: AuthService,
        private readonly multilingualStringTranslator: MultilingualStringTranslatorService,
        private readonly router: Router,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        @Inject(AUTH_CONFIG) private readonly authConfig: AuthConfig,
    ) {}

    public ngOnDestroy(): void {
        this.abortController?.abort();
    }

    protected loginWithUaepass(): void {
        if (this.isWaitingForWindow() || this.isAuthenticating()) return;
        this.isWaitingForWindow.set(true);

        this.state = this.generateRandomState();

        const authUrl = constructUrl(this.config.uaepass.endpoints.authorize, {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'redirect_uri': this.config.uaepass.redirectUri,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'client_id': this.config.uaepass.clientId,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'response_type': 'code',
            'scope':
                'urn:uae:digitalid:profile:general urn:uae:digitalid:profile:general:profileType urn:uae:digitalid:profile:general:unifiedId', // cspell:ignore digitalid
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'acr_values': 'urn:safelayer:tws:policies:authentication:level:low', // cspell:ignore safelayer
            'state': this.state,
        });

        // Open UAEPass auth in a new window
        const authWindow = window.open(
            authUrl,
            'UAEPassAuth',
            'width=500,height=600',
        );

        const checkWindowClosed = setInterval(() => {
            if (authWindow && authWindow.closed) {
                clearInterval(checkWindowClosed);
                this.abortController?.abort();
                this.abortController = undefined;
                this.isWaitingForWindow.set(false);
            }
        }, 500);

        this.abortController = new AbortController();
        window.addEventListener('message', this.handleAuthMessage.bind(this), {
            signal: this.abortController!.signal,
        });
    }

    private handleAuthMessage(event: MessageEvent): void {
        if (new URL(this.config.uaepass.redirectUri).origin !== event.origin)
            return;

        if (
            event.data['type'] === UaepassRedirectHandlerComponent.codeKey &&
            event.data['state'] === this.state
        ) {
            this.authenticateWithUaepassCode(event.data['code']);
        } else if (event.data['error']) {
            this.toastService.danger(event.data['error']);
        }
    }

    private authenticateWithUaepassCode(code: string): void {
        if (this.isAuthenticating()) return;
        this.isAuthenticating.set(true);

        this.authService
            .loginByUaepass(
                code,
                this.authConfig?.uaepass?.registerIfDoesNotExist ?? false,
            )
            .pipe(finalize(() => this.isAuthenticating.set(false)))
            .subscribe(async identity => {
                this.translateService
                    .get('translate_successfully_logged_in_as_name', {
                        name: this.multilingualStringTranslator.get(
                            identity.user.name,
                        ),
                    })
                    .subscribe(async str => {
                        this.toastService.success(str);
                        await this.router.navigate(['']);
                    });
            });
    }

    private generateRandomState(): string {
        return (
            Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15)
        );
    }
}
