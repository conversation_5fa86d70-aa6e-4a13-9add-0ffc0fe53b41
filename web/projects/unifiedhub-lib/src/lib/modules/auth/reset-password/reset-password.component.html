<div class="flex flex-col gap-8">
    @if (errors().length) {
        <lib-alert
            alertType="danger"
            alertTitle="{{ 'translate_an_error_has_occurred' | translate }}"
            [alertList]="errors()"
        />
    }

    <lib-dynamic-form [config]="config()" (formGroupChange)="form.set($event)">
        <lib-loading-button
            submitButton
            (click)="submit()"
            [isLoading]="isSubmitting()"
            label="translate_reset_password"
        />
    </lib-dynamic-form>

    <a class="self-center text-sm" [routerLink]="['', 'auth', 'login']">
        {{ 'translate_go_back_to_login' | translate }}
    </a>
</div>
