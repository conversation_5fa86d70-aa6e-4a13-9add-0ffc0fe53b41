import { Routes } from '@angular/router';
import { SuggestionCategoriesComponent } from './suggestion-categories.component';
import { SuggestionCategoryListPageComponent } from './pages/suggestion-category-list-page/suggestion-category-list-page.component';
import { SuggestionCategoryNewPageComponent } from './pages/suggestion-category-new-page/suggestion-category-new-page.component';
import { SuggestionCategoryDetailComponent } from './pages/suggestion-category-detail-page/suggestion-category-detail.component';

export const routes = [
    {
        path: '',
        component: SuggestionCategoriesComponent,
        children: [
            {
                path: '',
                component: SuggestionCategoryListPageComponent,
                data: {
                    title: 'translate_suggestion_categories',
                },
            },

            {
                path: 'new',
                component: SuggestionCategoryNewPageComponent,
                data: {
                    title: 'translate_add_new_suggestion_category',
                },
            },

            {
                path: 'edit/:id',
                component: SuggestionCategoryNewPageComponent,
                data: {
                    title: 'translate_update_suggestion_category_details',
                },
            },

            {
                path: ':id',
                component: SuggestionCategoryDetailComponent,
                data: {
                    title: 'translate_suggestion_category_details',
                },
            },
        ],
    },
] satisfies Routes;
