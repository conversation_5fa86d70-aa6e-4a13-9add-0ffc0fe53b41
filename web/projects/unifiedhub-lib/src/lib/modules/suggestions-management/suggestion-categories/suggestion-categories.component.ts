import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SuggestionCategoriesService } from './suggestion-categories.service';

@Component({
    selector: 'lib-suggestion-categories',
    template: '<router-outlet/>',
    standalone: true,
    providers: [SuggestionCategoriesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SuggestionCategoriesComponent {}
