import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { SuggestionCategoriesService } from '../../suggestion-categories.service';

@Component({
    selector: 'lib-suggestion-category-list-page',
    templateUrl: './suggestion-category-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class SuggestionCategoryListPageComponent {
    protected config = config(inject(SuggestionCategoriesService));
}
