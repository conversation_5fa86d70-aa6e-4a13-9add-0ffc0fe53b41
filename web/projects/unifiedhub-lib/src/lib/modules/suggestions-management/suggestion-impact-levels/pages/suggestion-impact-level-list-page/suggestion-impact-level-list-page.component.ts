import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { SuggestionImpactLevelsService } from '../../suggestion-impact-levels.service';

@Component({
    selector: 'lib-suggestion-impact-level-list-page',
    templateUrl: './suggestion-impact-level-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class SuggestionImpactLevelListPageComponent {
    protected config = config(inject(SuggestionImpactLevelsService));
}
