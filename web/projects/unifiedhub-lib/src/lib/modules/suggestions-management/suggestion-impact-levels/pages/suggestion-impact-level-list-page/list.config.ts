import { SuggestionImpactLevelsService } from '../../suggestion-impact-levels.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { SuggestionImpactLevel } from '../../../core';

export const config = (
    suggestionImpactLevelsService: SuggestionImpactLevelsService,
): DynamicListPageConfig<SuggestionImpactLevel, { keyword?: string }> => {
    return {
        title: 'translate_suggestion_impact_levels',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'suggestions', 'impact-levels', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: suggestionImpactLevelsService.list.bind(
                suggestionImpactLevelsService,
            ),
            deleter: suggestionImpactLevelsService.delete.bind(
                suggestionImpactLevelsService,
            ),
        },

        newButtonConfig: {
            permissionId: p.courses.manageLists,
        },
    };
};
