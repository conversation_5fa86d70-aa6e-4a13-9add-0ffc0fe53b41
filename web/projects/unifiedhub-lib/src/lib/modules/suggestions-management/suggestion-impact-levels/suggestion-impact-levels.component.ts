import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SuggestionImpactLevelsService } from './suggestion-impact-levels.service';

@Component({
    selector: 'lib-suggestion-impact-levels',
    template: '<router-outlet/>',
    standalone: true,
    providers: [SuggestionImpactLevelsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SuggestionImpactLevelsComponent {}
