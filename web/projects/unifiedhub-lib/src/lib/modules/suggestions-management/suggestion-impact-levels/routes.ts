import { Routes } from '@angular/router';
import { SuggestionImpactLevelsComponent } from './suggestion-impact-levels.component';
import { SuggestionImpactLevelListPageComponent } from './pages/suggestion-impact-level-list-page/suggestion-impact-level-list-page.component';
import { SuggestionImpactLevelNewPageComponent } from './pages/suggestion-impact-level-new-page/suggestion-impact-level-new-page.component';
import { SuggestionImpactLevelDetailComponent } from './pages/suggestion-impact-level-detail-page/suggestion-impact-level-detail.component';

export const routes = [
    {
        path: '',
        component: SuggestionImpactLevelsComponent,
        children: [
            {
                path: '',
                component: SuggestionImpactLevelListPageComponent,
                data: {
                    title: 'translate_suggestion_impact_levels',
                },
            },

            {
                path: 'new',
                component: SuggestionImpactLevelNewPageComponent,
                data: {
                    title: 'translate_add_new_suggestion_impact_level',
                },
            },

            {
                path: 'edit/:id',
                component: SuggestionImpactLevelNewPageComponent,
                data: {
                    title: 'translate_update_suggestion_impact_level_details',
                },
            },

            {
                path: ':id',
                component: SuggestionImpactLevelDetailComponent,
                data: {
                    title: 'translate_suggestion_impact_level_details',
                },
            },
        ],
    },
] satisfies Routes;
