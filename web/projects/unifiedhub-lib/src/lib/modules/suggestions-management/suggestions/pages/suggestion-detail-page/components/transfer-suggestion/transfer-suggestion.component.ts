import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    output,
    signal,
} from '@angular/core';
import { Suggestion } from '../../../../../core';
import { DynamicFormComponent } from '../../../../../../../features';
import { configFn } from './new.config';
import { LoadingButtonComponent } from '../../../../../../../ui';
import { FormGroup } from '@angular/forms';
import { DepartmentsService } from '../../../../../../departments/departments.service';
import { Department } from '../../../../../../departments/core';

@Component({
    selector: 'lib-transfer-suggestion',
    templateUrl: 'transfer-suggestion.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicFormComponent, LoadingButtonComponent],
})
export class TransferSuggestionComponent {
    public readonly suggestion = input.required<Suggestion>();
    public readonly transfer = output<{
        department: Department;
        notes?: string;
    }>();

    protected readonly config = computed(() => {
        return configFn(this.suggestion(), this.departmentsService);
    });
    protected readonly form = signal<FormGroup | undefined>(undefined);

    public constructor(
        private readonly departmentsService: DepartmentsService,
    ) {}

    protected submit(): void {
        if (this.form()?.invalid ?? true) return;
        this.transfer.emit(this.form()!.getRawValue());
    }
}
