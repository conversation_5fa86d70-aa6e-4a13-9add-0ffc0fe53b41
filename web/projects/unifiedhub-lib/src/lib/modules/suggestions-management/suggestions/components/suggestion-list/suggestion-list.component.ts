import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    input,
} from '@angular/core';
import { config } from './list.config';
import { DynamicListFullComponent } from '../../../../../features';
import { Suggestion, SuggestionFilterAttrs } from '../../../core';
import { Filter, PaginatedResult } from '../../../../../common';
import { Observable } from 'rxjs';

@Component({
    selector: 'lib-suggestion-list',
    templateUrl: './suggestion-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListFullComponent],
})
export class SuggestionListComponent {
    public readonly filter = input<SuggestionFilterAttrs>();
    public readonly fetcher =
        input<
            (
                filter: Filter<SuggestionFilterAttrs>,
            ) => Observable<PaginatedResult<Suggestion>>
        >();

    protected readonly config = computed(() => {
        return config(this.injector, this.filter(), this.fetcher());
    });

    public constructor(private readonly injector: Injector) {}
}
