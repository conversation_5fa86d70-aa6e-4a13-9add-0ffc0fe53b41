<lib-dynamic-detail-page
    #detailPage
    [config]="config"
    (itemLoad)="item.set($event)"
>
    <ng-container tools>
        @if (item() && item()!.stateAvailability.submitted) {
            <!-- Submit button -->
            <button
                (click)="submit()"
                [disabled]="isProcessing()"
                class="btn btn-lg btn-success flex flex-row items-center gap-2"
            >
                <i class="fa fa-paper-plane"></i>
                <span>
                    {{ 'translate_submit' | translate }}
                </span>
            </button>
        }
        @if (
            item() &&
            (item()!.stateAvailability
                | isAnyPropTrue: ['submitted', 'returned'])
        ) {
            @if (
                (p.suggestions.assess | rbac | async) &&
                item()!.state !== 'returned' &&
                item()!.state !== 'draft'
            ) {
                @if (!item()!.assessingDepartment) {
                    <!-- Transfer to department button -->
                    <button
                        (click)="showTransferDialog()"
                        [disabled]="isProcessing()"
                        class="btn btn-lg btn-info flex flex-row items-center gap-2"
                    >
                        <i class="fa fa-upload"></i>
                        <span>
                            {{ 'translate_transfer' | translate }}
                        </span>
                    </button>
                } @else {
                    <!-- Withdraw department transfer -->
                    <button
                        (click)="withdrawTransfer()"
                        [disabled]="isProcessing()"
                        class="btn btn-lg btn-danger flex flex-row items-center gap-2"
                    >
                        <i class="fa fa-download"></i>
                        <span>
                            {{ 'translate_withdraw_transfer' | translate }}
                        </span>
                    </button>
                }
            }

            <!-- Update state button -->
            <button
                (click)="showUpdateStateDialog()"
                [disabled]="isProcessing()"
                class="btn btn-lg btn-primary flex flex-row items-center gap-2"
            >
                <i class="fa fa-paper-plane"></i>
                <span>
                    {{ 'translate_update_state' | translate }}
                </span>
            </button>
        }

        <!-- My suggestions -->
        <a
            [routerLink]="['', 'suggestions', 'mine']"
            class="btn btn-lg btn-primary flex flex-row items-center gap-2"
        >
            <i class="fa fa-thought-bubble"></i>
            <span>
                {{ 'translate_my_suggestions' | translate }}
            </span>
        </a>
    </ng-container>
</lib-dynamic-detail-page>
