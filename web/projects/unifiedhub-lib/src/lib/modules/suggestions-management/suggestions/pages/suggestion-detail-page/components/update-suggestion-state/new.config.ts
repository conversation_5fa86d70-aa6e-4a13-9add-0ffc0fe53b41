import { DynamicFormConfig } from '../../../../../../../features';
import { SuggestionsService } from '../../../../suggestions.service';
import { Item } from '../../../../../../../common';
import { Suggestion, SuggestionStateAvailability } from '../../../../../core';
import { map, Observable } from 'rxjs';

const getApplicableStates = (
    states$: Observable<Item[]>,
    suggestion: Suggestion,
): Observable<Item[]> => {
    const stateToAbility = (
        state: Suggestion['state'],
    ): keyof SuggestionStateAvailability => {
        return state.replace(/_([a-z])/g, (_, letter) =>
            letter.toUpperCase(),
        ) as keyof SuggestionStateAvailability;
    };

    return states$.pipe(
        map(states => {
            return states.filter(
                x =>
                    suggestion.stateAvailability[
                        stateToAbility(x.id as Suggestion['state'])
                    ],
            );
        }),
    );
};

export const configFn = (
    suggestion: Suggestion,
    suggestionsService: SuggestionsService,
): DynamicFormConfig[] => {
    return [
        {
            id: 'state',
            type: 'select',
            label: 'translate_state',
            required: true,
            props: {
                items$: getApplicableStates(
                    suggestionsService.states(),
                    suggestion,
                ),
                bindLabel: 'name',
                bindValue: 'id',
            },
            onValueChange: (value, _, fields) => {
                fields['notes'].required = value === 'returned';
            },
        },

        {
            id: 'notes',
            type: 'textarea',
            label: 'translate_notes',
        },
    ];
};
