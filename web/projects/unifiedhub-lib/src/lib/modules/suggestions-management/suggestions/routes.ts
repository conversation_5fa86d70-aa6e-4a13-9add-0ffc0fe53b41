import { Routes } from '@angular/router';
import { SuggestionsComponent } from './suggestions.component';
import { SuggestionListPageComponent } from './pages/suggestion-list-page/suggestion-list-page.component';
import { SuggestionNewPageComponent } from './pages/suggestion-new-page/suggestion-new-page.component';
import { SuggestionDetailPageComponent } from './pages/suggestion-detail-page/suggestion-detail-page.component';
import { MySuggestionListPageComponent } from './pages/my-suggestion-list-page/my-suggestion-list-page.component';
import { SuggestionDashboardPageComponent } from './pages/suggestion-dashboard-page/suggestion-dashboard-page.component';
import { p } from '../../../permissions';

export const routes = [
    {
        path: '',
        component: SuggestionsComponent,
        children: [
            {
                path: '',
                component: SuggestionListPageComponent,
                data: {
                    title: 'translate_suggestions',
                    permissionIds: [
                        p.suggestions.assess,
                        p.suggestions.departmentAssess,
                    ],
                },
            },

            {
                path: 'new',
                component: SuggestionNewPageComponent,
                data: {
                    title: 'translate_add_new_suggestion',
                },
            },

            {
                path: 'mine',
                component: MySuggestionListPageComponent,
                data: {
                    title: 'translate_my_suggestions',
                },
            },

            {
                path: 'statistics',
                component: SuggestionDashboardPageComponent,
                data: {
                    title: 'translate_suggestions_statistics',
                },
            },

            {
                path: 'edit/:id',
                component: SuggestionNewPageComponent,
                data: {
                    title: 'translate_update_suggestion_details',
                },
            },

            {
                path: ':id',
                component: SuggestionDetailPageComponent,
                data: {
                    title: 'translate_suggestion_details',
                },
            },
        ],
    },
] satisfies Routes;
