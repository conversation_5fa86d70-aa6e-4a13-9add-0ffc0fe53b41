import { SuggestionsService } from '../../suggestions.service';
import { Suggestion } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';
import { Injector } from '@angular/core';
import { SuggestionCategoriesService } from '../../../suggestion-categories/suggestion-categories.service';
import { SuggestionImpactLevelsService } from '../../../suggestion-impact-levels/suggestion-impact-levels.service';
import { DepartmentsService } from '../../../../departments/departments.service';
import { EmployeesService } from '../../../../employment/employees/employees.service';
import { map } from 'rxjs';

export const configFn = (injector: Injector): NewPageConfigFn<Suggestion> => {
    const suggestionsService = injector.get(SuggestionsService);

    return mode => ({
        title: 'translate_suggestions',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_suggestion'
                : 'translate_update_suggestion_details',
        listPageConfig: {
            route: ['', 'suggestions', 'mine'],
            title: 'translate_my_suggestions',
        },
        newFormConfig: {
            loader: suggestionsService.get.bind(suggestionsService),
            creator: suggestionsService.create.bind(suggestionsService),
            editor: suggestionsService.update.bind(suggestionsService),
            formConfig: [
                {
                    id: 'name',
                    type: 'multilingualTextInput',
                    label: 'translate_name',
                    required: true,
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'expectedGoals',
                            type: 'textarea',
                            label: 'translate_expected_goals',
                        },

                        {
                            id: 'beneficiaries',
                            type: 'textarea',
                            label: 'translate_beneficiaries',
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'category',
                            type: 'select',
                            label: 'translate_category',
                            required: true,
                            props: {
                                items$: injector
                                    .get(SuggestionCategoriesService)
                                    .listAll(),
                                bindLabel: 'name',
                                compareWith: (a, b) => a?.id === b?.id,
                            },
                        },

                        {
                            id: 'impactLevel',
                            type: 'select',
                            label: 'translate_impact_level',
                            required: true,
                            props: {
                                items$: injector
                                    .get(SuggestionImpactLevelsService)
                                    .listAll(),
                                bindLabel: 'name',
                                compareWith: (a, b) => a?.id === b?.id,
                            },
                        },

                        {
                            id: 'department',
                            type: 'select',
                            label: 'translate_department',
                            props: {
                                items$: injector
                                    .get(DepartmentsService)
                                    .listAll(),
                                bindLabel: 'name',
                                compareWith: (a, b) => a?.id === b?.id,
                            },
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'expectedImplementationTime',
                            type: 'textarea',
                            label: 'translate_expected_implementation_time',
                        },

                        {
                            id: 'employees',
                            type: 'select',
                            label: 'translate_employees',
                            props: {
                                loaderFetcher: keyword =>
                                    injector
                                        .get(EmployeesService)
                                        .simpleList({ attrs: { keyword } })
                                        .pipe(map(x => x.items)),
                                bindLabel: 'name',
                                compareWith: (a, b) => a?.id === b?.id,
                                isMulti: true,
                            },
                        },
                    ],
                },

                {
                    id: 'files',
                    type: 'repeat',
                    label: 'translate_files',
                    props: {
                        repeatedConfig: {
                            type: 'file',
                            id: 'file',
                            label: 'translate_file',
                            props: {
                                fileDownloader: file =>
                                    suggestionsService.file(file.id!),
                            },
                        },
                    },
                },
            ],
        },
    });
};
