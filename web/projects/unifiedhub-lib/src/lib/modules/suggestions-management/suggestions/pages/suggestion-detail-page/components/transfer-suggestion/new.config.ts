import { DynamicFormConfig } from '../../../../../../../features';
import { DepartmentsService } from '../../../../../../departments/departments.service';
import { map } from 'rxjs';
import { Suggestion } from '../../../../../core';

export const configFn = (
    suggestion: Suggestion,
    departmentsService: DepartmentsService,
): DynamicFormConfig[] => {
    return [
        {
            id: 'department',
            type: 'select',
            label: 'translate_department',
            required: true,
            defaultValue: suggestion.department,
            props: {
                loaderFetcher: keyword =>
                    departmentsService
                        .simpleList({ attrs: { keyword } })
                        .pipe(map(data => data.items)),
                bindLabel: 'name',
            },
        },

        {
            id: 'notes',
            type: 'textarea',
            label: 'translate_notes',
        },
    ];
};
