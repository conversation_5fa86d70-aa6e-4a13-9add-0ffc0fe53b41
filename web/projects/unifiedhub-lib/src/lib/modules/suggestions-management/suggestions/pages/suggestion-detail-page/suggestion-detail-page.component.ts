import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
    OutputRefSubscription,
    signal,
    viewChild,
} from '@angular/core';
import { config } from './detail.config';
import {
    DynamicDetailPageComponent,
    ModalService,
    SmartAlertService,
} from '../../../../../features';
import { EmployeesService } from '../../../../employment/employees/employees.service';
import { DepartmentsService } from '../../../../departments/departments.service';
import { JobLevelsService } from '../../../../employment/job-levels/job-levels.service';
import { JobTitlesService } from '../../../../employment/job-titles/job-titles.service';
import { Suggestion } from '../../../core';
import { TranslateModule } from '@ngx-translate/core';
import { SuggestionsService } from '../../suggestions.service';
import { finalize } from 'rxjs';
import { RbacPipe, ToastService } from '../../../../../core';
import { IsAnyPropTruePipe } from './pipes/is-any-prop-true.pipe';
import { UpdateSuggestionStateComponent } from './components/update-suggestion-state/update-suggestion-state.component';
import { TransferSuggestionComponent } from './components/transfer-suggestion/transfer-suggestion.component';
import { p } from '../../../../../permissions';
import { AsyncPipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
    selector: 'lib-suggestion-detail-page',
    templateUrl: './suggestion-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        EmployeesService,
        DepartmentsService,
        JobLevelsService,
        JobTitlesService,
    ],
    imports: [
        DynamicDetailPageComponent,
        TranslateModule,
        IsAnyPropTruePipe,
        RbacPipe,
        AsyncPipe,
        RouterLink,
    ],
})
export class SuggestionDetailPageComponent {
    protected readonly item = signal<Suggestion | undefined>(undefined);
    protected readonly isProcessing = signal<boolean>(false);

    protected readonly config = config(inject(Injector));
    protected readonly p = p;

    private readonly detailPage =
        viewChild<DynamicDetailPageComponent<Suggestion>>('detailPage');

    public constructor(
        private readonly smartAlertService: SmartAlertService,
        private readonly suggestionsService: SuggestionsService,
        private readonly toastService: ToastService,
        private readonly modalService: ModalService,
        private readonly injector: Injector,
    ) {}

    protected submit(): void {
        this.smartAlertService
            .confirm('translate_are_you_sure_you_want_to_submit')
            .subscribe(isConfirmed => {
                if (!isConfirmed) return;
                this.updateState('submitted');
            });
    }

    protected async showUpdateStateDialog(): Promise<void> {
        if (!this.item()) return;
        let subscription: OutputRefSubscription;

        const component = await this.modalService.show(
            UpdateSuggestionStateComponent,
            {
                title: 'translate_update_suggestion_state',
                injector: this.injector,
                inputs: {
                    suggestion: this.item()!,
                },
                onDismiss: () => {
                    subscription.unsubscribe();
                },
            },
        );

        subscription = component.update.subscribe(result => {
            this.updateState(result.state, result.notes);
            this.modalService.dismiss(component);
        });
    }

    protected async showTransferDialog(): Promise<void> {
        if (!this.item()) return;
        let subscription: OutputRefSubscription;

        const component = await this.modalService.show(
            TransferSuggestionComponent,
            {
                title: 'translate_transfer_suggestion_to_department',
                injector: this.injector,
                inputs: {
                    suggestion: this.item()!,
                },
                onDismiss: () => {
                    subscription.unsubscribe();
                },
            },
        );

        subscription = component.transfer.subscribe(result => {
            this.transfer(result.department.id, result.notes);
            this.modalService.dismiss(component);
        });
    }

    protected withdrawTransfer(): void {
        this.smartAlertService
            .confirm(
                'translate_are_you_sure_you_want_to_withdraw_assessment_privileges_from_the_current_assessing_department',
            )
            .subscribe(isConfirmed => {
                if (!isConfirmed) return;
                this.transfer();
            });
    }

    private updateState(state: Suggestion['state'], notes?: string): void {
        if (this.isProcessing() || !this.item()) return;
        this.isProcessing.set(true);
        this.suggestionsService
            .updateState(this.item()!.id, state, notes)
            .pipe(finalize(() => this.isProcessing.set(false)))
            .subscribe(() => {
                this.toastService.success('translate_operation_successful');
                this.detailPage()!.reload();
            });
    }

    private transfer(departmentId?: string, notes?: string): void {
        if (this.isProcessing() || !this.item()) return;
        this.isProcessing.set(true);
        this.suggestionsService
            .transfer(this.item()!.id, departmentId, notes)
            .pipe(finalize(() => this.isProcessing.set(false)))
            .subscribe(() => {
                this.toastService.success('translate_operation_successful');
                this.detailPage()!.reload();
            });
    }
}
