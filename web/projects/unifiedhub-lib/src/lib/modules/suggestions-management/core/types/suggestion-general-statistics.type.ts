import { Department } from '../../../departments/core';
import { Employee } from '../../../employment/core';

export type SuggestionGeneralStatistics = {
    totalCount: number;
    applicableCount: number;
    inapplicableCount: number;
    suggesterCount: number;
    submittedCount: number;
    completedCount: number;

    totalPerPeriodCounts: {
        period: number;
        totalCount: number;
    }[];

    topTotalPerDepartmentCounts: {
        department: Department;
        totalCount: number;
    }[];

    topTotalPerEmployeeCounts: {
        employee: Employee;
        totalCount: number;
        applicableCount: number;
    }[];

    topApplicablePerEmployeeCounts: {
        employee: Employee;
        totalCount: number;
        applicableCount: number;
    }[];
};
