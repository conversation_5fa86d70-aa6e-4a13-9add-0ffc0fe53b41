import {
    trigger,
    transition,
    query,
    stagger,
    animateChild,
    style,
    animate,
} from '@angular/animations';

export const translatingCardAnimation = [
    trigger('translatingCardGrid', [
        transition('* => *', [
            query('@translatingCard', stagger('50ms', animateChild()), {
                optional: true,
            }),
        ]),
    ]),

    trigger('translatingCard', [
        transition(':enter', [
            style({
                // transform: 'perspective(30cm) rotateX(-75deg)',
                transform: 'translate(-10px, -10px) rotateZ(5deg)',
                opacity: 0.0,
            }),
            animate(
                '500ms cubic-bezier(.17,.67,.15,1.57)',
                style({
                    // transform: 'perspective(30cm) rotateX(0deg)',
                    transform: 'translate(0, 0) rotateZ(0deg)',
                    opacity: 1.0,
                }),
            ),
        ]),
    ]),
];
