import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { UsersService } from '../users/users.service';
import { ServicingFieldReplaceService } from './servicing-field-replace.service';

@Component({
    selector: 'lib-servicing',
    template: '<router-outlet/>',
    standalone: true,
    providers: [UsersService, ServicingFieldReplaceService],
    imports: [RouterOutlet],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServicingComponent {}
