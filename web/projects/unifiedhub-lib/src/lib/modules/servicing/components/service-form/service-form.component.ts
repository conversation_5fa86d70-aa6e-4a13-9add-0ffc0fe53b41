import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    model,
    output,
} from '@angular/core';
import { ServiceFormItem } from '../../types';
import { FormBuilder } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import {
    DynamicFormComponent,
    DynamicFormConfig,
    DynamicFormFieldConfig,
    DynamicFormSelectConfig,
} from '../../../../features';
import { MultilingualStringTranslatorService } from '../../../../core';

type FileDownloader = (fieldPath: string) => Observable<Blob>;

@Component({
    selector: 'lib-service-form',
    templateUrl: 'service-form.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicFormComponent],
})
export class ServiceFormComponent {
    public data = model<unknown>();
    public form = model(this.fb.group({}));
    public fields = model<Record<string, DynamicFormFieldConfig>>();

    public readonly formItems = input.required<ServiceFormItem[]>();
    public readonly mode = input<'default' | 'view'>('default');
    public readonly fileDownloader = input<FileDownloader>();
    public readonly formRefresher =
        input<(values: unknown) => Observable<ServiceFormItem[]>>();

    public readonly stateListChange = output<unknown>();

    protected readonly config = computed<DynamicFormConfig[]>(() => {
        return this.mapFormItemsToFormConfig(
            this.formItems(),
            this.mode(),
            this.fileDownloader(),
        );
    });

    private ongoingFormRefresh?: Subscription;

    public constructor(
        private readonly fb: FormBuilder,
        private readonly multilingualStringTranslatorService: MultilingualStringTranslatorService,
    ) {}

    private mapFormItemsToFormConfig(
        formItems: ServiceFormItem[],
        mode: 'view' | 'default',
        fileDownloader?: FileDownloader,
    ): DynamicFormConfig[] {
        return formItems.map(x => {
            const config = {
                id: x.id,
                label: x.label,
                required: x.isRequired,
                defaultValue: x.defaultValue,
                note: this.multilingualStringTranslatorService.get(x.note),
                disabled: mode === 'view' || x.isDisabled,
                hide: x.isHidden,

                onValueChange: () => {
                    if (!x.refreshForm && !x.refreshStateList) return;

                    const fields = this.fields();
                    const formRefresher = this.formRefresher();

                    if (!fields || !formRefresher) return;

                    const fieldsArray = Object.values(fields);

                    const values = structuredClone(
                        this.form().getRawValue(),
                    ) as any;

                    // We need to remove the files before refreshing
                    // the configurations
                    fieldsArray
                        .filter(x => x.type === 'file')
                        .forEach(({ id }) => {
                            if (!id || !values?.[id]?.['bytes']) return;
                            values[id]['bytes'] = undefined;
                        });

                    if (x.refreshStateList) {
                        this.stateListChange.emit(values);
                    }

                    // The code below conditionally refreshes the form configuration
                    // when a value has changed. It communicates with the server
                    // to update the field properties based on changes on the form
                    // data.
                    if (x.refreshForm) {
                        this.ongoingFormRefresh?.unsubscribe();

                        this.ongoingFormRefresh = formRefresher(
                            values,
                        ).subscribe(refreshedFormItems => {
                            this.refreshFields(refreshedFormItems, fields);
                        });
                    }
                },
            } satisfies Partial<DynamicFormConfig>;

            switch (x.type) {
                case 'text':
                case 'number':
                case 'boolean':
                    return {
                        ...config,
                        type: 'input',
                        props: {
                            type: x.type === 'boolean' ? 'checkbox' : x.type,
                        },
                    };

                case 'large_text':
                    return {
                        ...config,
                        type: 'textarea',
                    };

                case 'date':
                case 'datetime':
                    return {
                        ...config,
                        type: 'datetime',
                        props: {
                            enableTime: x.type === 'datetime',
                        },
                    };

                case 'file':
                    return {
                        ...config,
                        type: 'file',
                        props: {
                            fileDownloader: fileDownloader
                                ? (_, fieldPath) => {
                                      return fileDownloader(fieldPath);
                                  }
                                : undefined,
                        },
                    };

                case 'option':
                    return {
                        ...config,
                        type: 'select',
                        props: {
                            items: x.options,
                            bindLabel: 'name',
                        },
                    };

                case 'repeat':
                    return {
                        ...config,
                        type: 'repeat',
                        props: {
                            isAdditionDisabled:
                                x.repeatConfig?.isAdditionDisabled,
                            repeatedConfig: {
                                type: 'group',
                                isGrid: true,
                                config: this.mapFormItemsToFormConfig(
                                    x.repeatConfig!.items,
                                    mode,
                                    fileDownloader,
                                ),
                            },
                        },
                    };
            }
        });
    }

    private refreshFields(
        refreshedFormItems: ServiceFormItem[],
        fields: Record<string, DynamicFormFieldConfig>,
    ): void {
        refreshedFormItems.forEach(item => {
            const field = fields[item.id];
            if (!field) return;

            if (field.hide !== item.isHidden) field.hide = item.isHidden;

            if (field.required !== item.isRequired)
                field.required = item.isRequired;

            const note = this.multilingualStringTranslatorService.get(
                item.note,
            );
            if (field.note !== note) field.note = note;

            if (item.type === 'option') {
                (field as DynamicFormSelectConfig)!.props!.items = item.options;
            }
        });
    }
}
