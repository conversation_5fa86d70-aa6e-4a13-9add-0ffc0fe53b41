import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
} from '@angular/core';
import { ServiceState } from '../../types';
import { NgClass } from '@angular/common';
import { TranslateMultilingualStringPipe } from '../../../../core';

@Component({
    selector: 'lib-service-state-list',
    templateUrl: './service-state-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateMultilingualStringPipe, NgClass],
})
export class ServiceStateListComponent {
    public readonly states = input.required<ServiceState[]>();
    public readonly currentState = input<ServiceState>();
    public readonly itemsGap = input<'large' | 'small'>('small');

    protected readonly currentStateKeyIndex = computed<number | undefined>(
        () => {
            const state = this.currentState();
            if (!state) return undefined;

            return this.states().findIndex(x => x.value === state.value);
        },
    );
}
