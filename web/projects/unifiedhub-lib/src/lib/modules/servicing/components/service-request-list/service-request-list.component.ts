import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    input,
    signal,
    viewChild,
} from '@angular/core';
import { ServicingService } from '../../servicing.service';
import { ServiceRequest, ServiceRequestFilter } from '../../types';
import { config } from './list.config';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import {
    deleteItemFromDynamicList,
    DynamicListFullComponent,
    SmartAlertService,
} from '../../../../features';
import { Filter, PaginatedResult } from '../../../../common';
import { ToastService } from '../../../../core';

@Component({
    selector: 'lib-service-request-list',
    templateUrl: './service-request-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListFullComponent],
})
export class ServiceRequestListComponent {
    public readonly scope = input<'all' | 'own' | 'act'>('all');
    public readonly userIds = input<string[]>();
    public readonly serviceIds = input<string[]>();
    public readonly hiddenColumns = input<{
        user?: boolean;
        service?: boolean;
    }>();
    public readonly hiddenFilters = input<{
        user?: boolean;
        service?: boolean;
    }>();
    public readonly fetcher =
        input<
            (
                filter: Filter<ServiceRequestFilter>,
            ) => Observable<PaginatedResult<ServiceRequest>>
        >();
    public readonly isQueryParamTrackingEnabled = input<boolean>(false);

    protected readonly currentlyProcessing = signal<Set<ServiceRequest>>(
        new Set(),
    );

    protected readonly config = computed(() => {
        return config(
            this.injector,
            this.delete.bind(this),
            {
                scope: this.scope(),
                userIds: this.userIds(),
                serviceIds: this.serviceIds(),
            },
            this.fetcher(),
            this.hiddenColumns(),
            this.hiddenFilters(),
            this.isQueryParamTrackingEnabled(),
        );
    });

    private readonly list =
        viewChild<
            DynamicListFullComponent<ServiceRequest, ServiceRequestFilter>
        >('list');

    public constructor(
        private readonly injector: Injector,
        private readonly servicingService: ServicingService,
        private readonly smartAlertService: SmartAlertService,
        private readonly translateService: TranslateService,
        private readonly toastService: ToastService,
    ) {}

    public delete(item: ServiceRequest): void {
        deleteItemFromDynamicList(
            item,
            this.smartAlertService,
            this.currentlyProcessing,
            this.servicingService.deleteServiceRequest.bind(
                this.servicingService,
            ),
            this.list()!.reload.bind(this.list()),
            this.translateService,
            this.toastService,
        );
    }
}
