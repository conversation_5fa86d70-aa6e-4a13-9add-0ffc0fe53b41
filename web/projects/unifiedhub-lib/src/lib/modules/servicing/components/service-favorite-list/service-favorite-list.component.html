@if (!services() || services()!.length > 0) {
    <div class="flex flex-col gap-2">
        <h2
            class="mb-4 text-center text-2xl font-bold md:ltr:text-left md:rtl:text-right"
        >
            {{ 'translate_favorite_services' | translate }}
        </h2>

        <lib-service-grid
            content
            *libWaitUntilListLoaded="services()"
            [services]="services()!"
            [cardConfig]="{
                cardBackgroundColor: 'gray',
                showPendingRequestCount: true
            }"
        />
    </div>
}
