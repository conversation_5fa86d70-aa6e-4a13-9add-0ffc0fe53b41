import { ServiceRequest, ServiceRequestFilter } from '../../types';
import { map, Observable } from 'rxjs';
import { Filter, PaginatedResult } from '../../../../common';
import {
    DynamicListFullConfig,
    ListFilterConfig,
    SelectFilterConfig,
} from '../../../../features';
import { Injector } from '@angular/core';
import { ServicingService } from '../../servicing.service';
import { ServicingFieldReplaceService } from '../../servicing-field-replace.service';
import { SERVICING_CONFIG, ServicingConfig } from '../../core';
import { getLastActionLabel } from '../../utils';

export const config = (
    injector: Injector,
    deleteCb: (item: ServiceRequest) => void,
    filter?: {
        userIds?: string[];
        serviceIds?: string[];
        scope: 'all' | 'own' | 'act';
    },
    fetcher?: (
        filter: Filter<ServiceRequestFilter>,
    ) => Observable<PaginatedResult<ServiceRequest>>,
    hiddenColumns?: {
        user?: boolean;
        service?: boolean;
    },
    hiddenFilters?: {
        user?: boolean;
        service?: boolean;
    },
    isQueryParamTrackingEnabled?: boolean,
): DynamicListFullConfig<ServiceRequest, ServiceRequestFilter> => {
    const servicingService = injector.get(ServicingService);
    const replace = injector.get(ServicingFieldReplaceService);
    const servicingConfig = injector.get(SERVICING_CONFIG) as ServicingConfig;

    return {
        fetcher:
            fetcher ??
            servicingService.listServiceRequests.bind(servicingService),
        persistentFilter: filter,
        isEditLinkHidden: true,
        isQueryParamTrackingEnabled,
        columnConfigFn: () => [
            ...[
                replace.column('createdTime', {
                    label: 'translate_created_time',
                    sortingKey: 'createdTime',
                }),
            ],

            ...(!hiddenColumns?.service
                ? [
                      replace.column('service', {
                          label: 'translate_service',
                      }),
                  ]
                : []),

            ...(!hiddenColumns?.user
                ? [
                      replace.column('user', {
                          label: 'translate_user_linked_with_request',
                      }),
                  ]
                : []),

            ...[
                replace.column('applyingUser', {
                    label: 'translate_service_applicant',
                }),

                replace.column('state', {
                    label: 'translate_state',
                }),

                replace.column('lastAction', {
                    label: 'translate_last_action',
                }),

                replace.column('transactionCount', {
                    label: 'translate_transaction_count',
                }),

                replace.column('latestTransactionCount', {
                    label: 'translate_latest_transaction_time',
                }),
            ],
        ],

        valueConfigFn: item => [
            ...[
                replace.value('createdTime', item, {
                    value: item.createdTime,
                    type: 'datetime',
                }),
            ],

            ...(!hiddenColumns?.service
                ? [
                      replace.value('service', item, {
                          value: item.service.name,
                      }),
                  ]
                : []),

            ...(!hiddenColumns?.user
                ? [
                      replace.value(
                          'user',
                          item,
                          servicingConfig.formatUserFn?.(item.user) ?? {
                              value: item.user.name,
                          },
                      ),
                  ]
                : []),

            ...[
                replace.value(
                    'applyingUser',
                    item,
                    servicingConfig.formatUserFn?.(item.applyingUser) ?? {
                        value: item.applyingUser.name,
                    },
                ),

                replace.value('state', item, {
                    value: item.state?.name,
                }),

                replace.value('lastAction', item, {
                    value: getLastActionLabel(item),
                }),

                replace.value('transactionCount', item, {
                    value: item.transactionCount,
                }),

                replace.value('latestTransactionCount', item, {
                    value: item.latestTransactionCreatedTime,
                    type: 'datetime',
                }),
            ],
        ],

        actionsFn: item => [
            {
                type: 'success',
                iconClasses: 'fa fa-eye',
                linkConfig: {
                    value: ['', 'servicing', 'requests', item.id],
                },
            },

            {
                type: 'info',
                iconClasses: 'fa fa-edit',
                linkConfig: {
                    value: ['', 'servicing', 'requests', item.id, 'edit'],
                },
                isHiddenFn: () =>
                    !item.ability.canWrite || item.service.isArchived,
            },

            {
                type: 'danger',
                iconClasses: 'fa fa-trash',
                onClickFn: () => deleteCb(item),
                isHiddenFn: () => !item.ability.canDelete,
            },
        ],

        filter: [
            ...(!hiddenFilters?.user
                ? [
                      {
                          id: 'userIds',
                          type: 'select',
                          label: 'translate_user_linked_with_request',
                          isMulti: true,
                          bindValue: 'id',
                          bindLabel: 'name',
                          loaderFetcher: keyword =>
                              servicingService.listUsers(keyword, 20),
                      } satisfies ListFilterConfig<ServiceRequestFilter>,
                  ]
                : []),

            ...[
                {
                    id: 'applyingUserIds',
                    type: 'select',
                    label: 'translate_service_applicant',
                    isMulti: true,
                    bindValue: 'id',
                    bindLabel: 'name',
                    loaderFetcher: keyword =>
                        servicingService.listUsers(keyword, 20),
                } satisfies ListFilterConfig<ServiceRequestFilter>,

                {
                    id: 'from',
                    type: 'datetime',
                    label: 'translate_from',
                } satisfies ListFilterConfig<ServiceRequestFilter>,

                {
                    id: 'to',
                    type: 'datetime',
                    label: 'translate_to',
                } satisfies ListFilterConfig<ServiceRequestFilter>,
            ],

            ...(!hiddenFilters?.service
                ? [
                      {
                          id: 'serviceIds',
                          type: 'select',
                          label: 'translate_service',
                          isMulti: true,
                          bindValue: 'id',
                          bindLabel: 'name',
                          items$: servicingService
                              .listServices({
                                  pageNumber: 0,
                                  pageSize: -1,
                              })
                              .pipe(map(x => x.items)),
                          onChange: (value, configs, updateAttrs) => {
                              const serviceIds = value as string[];
                              const statesConfig = configs.find(
                                  x => x.id === 'states',
                              )! as SelectFilterConfig;
                              updateAttrs('states', undefined);

                              if (serviceIds?.length === 1) {
                                  statesConfig.isHidden = false;
                                  statesConfig.isDisabled = false;
                                  statesConfig.items$ =
                                      servicingService.listServiceStates(
                                          serviceIds[0],
                                      );
                              } else {
                                  statesConfig.isHidden = true;
                                  statesConfig.isDisabled = true;
                              }
                          },
                      } satisfies ListFilterConfig<ServiceRequestFilter>,
                  ]
                : []),
            ...[
                {
                    id: 'states',
                    type: 'select',
                    label: 'translate_state',
                    bindValue: 'value',
                    bindLabel: 'name',
                    isMulti: true,
                    isHidden: true,
                } satisfies ListFilterConfig<ServiceRequestFilter>,
            ],
        ],
    };
};
