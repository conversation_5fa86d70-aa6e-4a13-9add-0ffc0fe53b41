import {
    ChangeDetectionStrategy,
    Component,
    input,
    model,
    signal,
} from '@angular/core';
import { Service } from '../../types';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
    ToastService,
    TranslateMultilingualStringPipe,
} from '../../../../core';
import { RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';
import { ServicingService } from '../../servicing.service';
import { finalize } from 'rxjs';
import { ServiceCardConfig } from './types/service-card-config.type';
import { StringifyPipe } from '../../../../common';

@Component({
    selector: 'lib-service-card',
    templateUrl: 'service-card.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        TranslateModule,
        TranslateMultilingualStringPipe,
        RouterLink,
        NgClass,
        StringifyPipe,
    ],
})
export class ServiceCardComponent {
    public service = model.required<Service>();
    public readonly config = input<ServiceCardConfig>();

    public readonly isLoading = signal(false);

    public constructor(
        private readonly servicingService: ServicingService,
        private readonly translateService: TranslateService,
        private readonly toastService: ToastService,
    ) {}

    protected toggleFavorite(): void {
        if (this.isLoading()) return;

        this.isLoading.set(true);
        const obs = this.service().isFavorite
            ? this.servicingService.removeFromFavorite(this.service().id)
            : this.servicingService.addServiceToFavorite(this.service().id);

        obs.pipe(finalize(() => this.isLoading.set(false))).subscribe(() => {
            this.service.update(x => ({ ...x, isFavorite: !x.isFavorite }));
            this.translateService
                .get('translate_operation_successful')
                .subscribe(str => this.toastService.success(str));
        });
    }
}
