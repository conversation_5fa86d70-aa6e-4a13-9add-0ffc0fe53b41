import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ServiceCategory } from '../../types';
import { ServicingService } from '../../servicing.service';
import { translatingCardAnimation } from '../../animations';
import { RouterLink } from '@angular/router';
import { LoadingRingComponent, PageContainerComponent } from '../../../../ui';
import { EnumeratePipe } from '../../../../common';
import { TranslateMultilingualStringPipe } from '../../../../core';

@Component({
    selector: 'lib-service-category-list-page',
    templateUrl: './service-category-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PageContainerComponent,
        TranslateModule,
        EnumeratePipe,
        TranslateMultilingualStringPipe,
        RouterLink,
        LoadingRingComponent,
    ],
    animations: [translatingCardAnimation],
})
export class ServiceCategoryListPageComponent {
    protected readonly categories = signal<ServiceCategory[] | undefined>(
        undefined,
    );

    public constructor(servicingService: ServicingService) {
        servicingService
            .listCategories()
            .subscribe(items => this.categories.set(items));
    }
}
