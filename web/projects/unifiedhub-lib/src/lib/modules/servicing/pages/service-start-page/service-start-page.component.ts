import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    signal,
} from '@angular/core';
import { Service, ServiceRequest, ServiceState } from '../../types';
import { ServicingService } from '../../servicing.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import {
    ServiceRequestFormComponent,
    ServiceStateListComponent,
} from '../../components';
import {
    LoadingButtonComponent,
    PageContainerComponent,
    WaitUntilListLoadedDirective,
    WaitUntilLoadedDirective,
} from '../../../../ui';
import { TranslateMultilingualStringPipe } from '../../../../core';
import { DynamicFormComponent } from '../../../../features';
import { BehaviorSubject, combineLatest, filter, switchMap } from 'rxjs';
import { User } from '../../../users/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime } from 'rxjs/operators';

@Component({
    selector: 'lib-service-start-page',
    templateUrl: './service-start-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PageContainerComponent,
        TranslateModule,
        TranslateMultilingualStringPipe,
        DynamicFormComponent,
        LoadingButtonComponent,
        WaitUntilListLoadedDirective,
        NgClass,
        ServiceStateListComponent,
        WaitUntilLoadedDirective,
        RouterLink,
        ServiceRequestFormComponent,
    ],
})
export class ServiceStartPageComponent {
    protected readonly service = signal<Service | undefined>(undefined);
    protected readonly mode = signal<'start' | 'edit'>('start');
    protected readonly request = signal<ServiceRequest | undefined>(undefined);
    protected readonly states = signal<ServiceState[] | undefined>(undefined);

    protected readonly user$ = new BehaviorSubject<User | undefined>(undefined);
    protected readonly data$ = new BehaviorSubject<unknown>(undefined);

    public constructor(
        private readonly servicingService: ServicingService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly router: Router,
        private readonly destroyRef: DestroyRef,
    ) {
        const isStart = activatedRoute.snapshot.url[0].path === 'services';
        this.mode.set(isStart ? 'start' : 'edit');

        switch (this.mode()) {
            case 'start':
                this.initStartMode();
                break;
            case 'edit':
                this.initEditMode();
                break;
        }
    }

    private initStartMode(): void {
        const serviceId =
            this.activatedRoute.snapshot.paramMap.get('serviceId')!;
        this.initStatesRefresher(serviceId);
        this.loadService(serviceId);
    }

    private initEditMode(): void {
        const requestId =
            this.activatedRoute.snapshot.paramMap.get('requestId')!;
        this.servicingService.getRequest(requestId).subscribe(async request => {
            // Ensure that the user has permission to edit the
            // request before proceeding.
            if (!request.ability.canWrite) {
                await this.router.navigate(['']);
                return;
            }

            this.initStatesRefresher(request.service.id);
            this.request.set(request);
            this.service.set(request.service);
        });
    }

    private loadService(serviceId: string): void {
        this.servicingService
            .getService(serviceId)
            .subscribe(item => this.service.set(item));
    }

    private initStatesRefresher(serviceId: string): void {
        combineLatest([this.user$, this.data$])
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                switchMap(([user, data]) => {
                    return this.servicingService.listServiceStates(
                        serviceId,
                        undefined,
                        user?.id,
                        data,
                    );
                }),
                filter(x => x !== undefined),
            )
            .subscribe(items => this.states.set(items));
    }
}
