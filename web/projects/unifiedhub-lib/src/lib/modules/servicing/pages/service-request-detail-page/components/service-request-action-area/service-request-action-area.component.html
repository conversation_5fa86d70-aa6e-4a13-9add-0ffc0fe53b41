<!--
  If there is only one action and it belongs to the first
  status, then display a `Submit request` button. Otherwise,
  display a select that lets the user choose the action.
-->
@if (currentStateIndex() === 0 && request().validActions.length === 1) {
    <div class="flex flex-col gap-4">
        <!-- Error -->
        <ng-container [ngTemplateOutlet]="errorTemplate" />

        <!-- Submit button -->
        <div class="flex items-center justify-center">
            <button
                [disabled]="isSubmitting()"
                (click)="invoke(request().validActions[0])"
                class="btn btn-success flex flex-row items-center gap-2"
            >
                <i class="fa fa-paper-plane"></i>
                <span>{{ 'translate_submit_request' | translate }}</span>
            </button>
        </div>
    </div>
} @else if (request().validActions.length !== 0) {
    <lib-service-request-detail-section
        sectionTitle="{{ 'translate_action' | translate }} ({{
            request().state?.name | translateMultilingualString
        }})"
    >
        <div class="flex flex-col gap-4">
            <!-- Error -->
            <ng-container [ngTemplateOutlet]="errorTemplate" />

            <!-- Action selector -->
            <div class="flex flex-row items-center gap-2">
                <span>{{ 'translate_action' | translate }}</span>
                <lib-select-input
                    class="grow"
                    [items]="validActions()"
                    [isNotSearchable]="true"
                    [isNotClearable]="true"
                    [(ngModel)]="selectedAction"
                    bindLabel="name"
                    placeholder="translate_action"
                />
            </div>

            <!-- Transaction data form -->
            <lib-service-form
                *libWaitUntilLoaded="transactionFormItems()"
                [(form)]="form"
                [formItems]="transactionFormItems()!"
                [formRefresher]="formRefresher()"
            />

            <!-- Submit button -->
            <button
                (click)="invoke(selectedAction()!.key)"
                [disabled]="!selectedAction() || isSubmitting()"
                class="btn btn-success flex flex-row items-center gap-2 self-center"
            >
                <i class="fa fa-paper-plane"></i>
                <span>{{ 'translate_submit' | translate }}</span>
            </button>
        </div>
    </lib-service-request-detail-section>
}

<ng-template #errorTemplate>
    @if (errors().length) {
        <lib-alert
            alertType="danger"
            alertTitle="{{ 'translate_an_error_has_occurred' | translate }}"
            [alertList]="errors()"
        />
    }
</ng-template>
