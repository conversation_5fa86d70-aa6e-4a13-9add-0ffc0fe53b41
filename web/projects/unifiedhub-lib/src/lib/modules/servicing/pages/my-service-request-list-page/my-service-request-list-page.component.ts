import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ServiceRequestListComponent } from '../../components';
import { TranslateModule } from '@ngx-translate/core';
import { AsyncPipe } from '@angular/common';
import { DynamicListPageComponent } from '../../../../features';
import { PageContainerComponent } from '../../../../ui';
import { AuthService } from '../../../../core';

@Component({
    selector: 'lib-my-service-request-list-page',
    templateUrl: './my-service-request-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicListPageComponent,
        ServiceRequestListComponent,
        PageContainerComponent,
        TranslateModule,
        AsyncPipe,
    ],
})
export class MyServiceRequestListPageComponent {
    public constructor(protected readonly authService: AuthService) {}
}
