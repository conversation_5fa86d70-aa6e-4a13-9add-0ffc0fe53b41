<lib-page-container
    pageTitle="{{
        (mode() === 'start'
            ? 'translate_start_service'
            : 'translate_update_request'
        ) | translate
    }}"
    [pageSubtitle]="service()?.name | translateMultilingualString"
>
    <div tools class="flex flex-row items-center gap-2">
        @if (service()) {
            <a
                [routerLink]="['', 'servicing', 'services', service()!.id]"
                class="btn btn-lg btn-primary flex flex-row items-center gap-2"
            >
                <i class="fa fa-circle-info"></i>
                <span>{{ 'translate_service_details' | translate }}</span>
            </a>
        }

        @if (request()) {
            <a
                [routerLink]="['', 'servicing', 'requests', request()!.id]"
                class="btn btn-lg btn-primary flex flex-row items-center gap-2"
            >
                <i class="fa fa-circle-info"></i>
                <span>{{ 'translate_request_details' | translate }}</span>
            </a>
        }
    </div>

    <div content class="flex flex-row gap-8">
        <!-- Stepper -->
        <div class="hidden w-80 shrink-0 bg-primary-50 p-4 md:block">
            <lib-service-state-list
                class="w-full"
                *libWaitUntilLoaded="service()"
                [states]="states() ?? service()!.states"
                [currentState]="states()?.[0] ?? service()!.states[0]"
                itemsGap="large"
            />
        </div>

        <!-- Form -->
        <lib-service-request-form
            class="grow"
            *libWaitUntilLoaded="service()"
            [service]="service()!"
            [request]="request()"
            (userChange)="user$.next($event)"
            (stateListFieldChange)="data$.next($event)"
        />
    </div>
</lib-page-container>
