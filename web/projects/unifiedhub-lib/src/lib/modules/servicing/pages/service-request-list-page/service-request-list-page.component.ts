import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ServiceRequestListComponent } from '../../components';
import { TranslateModule } from '@ngx-translate/core';
import { PageContainerComponent } from '../../../../ui';

@Component({
    selector: 'lib-service-request-list-page',
    templateUrl: './service-request-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ServiceRequestListComponent,
        PageContainerComponent,
        TranslateModule,
    ],
})
export class ServiceRequestListPageComponent {}
