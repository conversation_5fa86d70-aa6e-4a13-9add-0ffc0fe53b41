import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    signal,
} from '@angular/core';
import { Service, ServiceCategory } from '../../types';
import { ServicingService } from '../../servicing.service';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { translatingCardAnimation } from '../../animations';
import { groupBy, MultilingualString } from '../../../../common';
import {
    LoadingRingComponent,
    PageContainerComponent,
    ScreenToggleContainerComponent,
} from '../../../../ui';
import { TranslateMultilingualStringPipe } from '../../../../core';

type ServiceGrouping = {
    id: string;
    name: string | MultilingualString;
    count: number;
    items: Service[];
};

@Component({
    selector: 'lib-service-list-page',
    templateUrl: './service-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PageContainerComponent,
        TranslateModule,
        TranslateMultilingualStringPipe,
        NgClass,
        RouterLink,
        LoadingRingComponent,
        ScreenToggleContainerComponent,
    ],
    animations: [translatingCardAnimation],
})
export class ServiceListPageComponent {
    protected readonly services = signal<Service[] | undefined>(undefined);
    protected readonly category = signal<ServiceCategory | undefined>(
        undefined,
    );

    protected readonly selectedServiceGrouping = signal<
        ServiceGrouping | undefined
    >(undefined);

    protected readonly serviceGrouping = computed<
        ServiceGrouping[] | undefined
    >(() => {
        const services = this.services();
        if (!services) return undefined;

        const allGroup: ServiceGrouping = {
            id: 'all',
            name: 'translate_all_services',
            count: services.length,
            items: services,
        };

        const groups = groupBy(
            services.filter(x => x.subcategory),
            x => x.subcategory,
            (a, b) => a.id === b.id,
        ).map(x => ({
            id: x.key.id,
            name: x.key.name,
            count: x.items.length,
            items: x.items,
        }));

        return [allGroup, ...groups];
    });

    public constructor(
        servicingService: ServicingService,
        destroyRef: DestroyRef,
    ) {
        // Load services.
        servicingService
            .listServices({
                attrs: {
                    attributes: ['not_system_managed'],
                },
                pageNumber: 0,
                pageSize: -1,
            })
            .subscribe(data => this.services.set(data.items));

        // When the services first load, select,
        // all services as the selected group.
        toObservable(this.serviceGrouping)
            .pipe(takeUntilDestroyed(destroyRef))
            .subscribe(groups => {
                if (!groups?.length) return;
                this.selectedServiceGrouping.set(groups[0]);
            });
    }
}
