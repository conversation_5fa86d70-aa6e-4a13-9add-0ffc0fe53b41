import { Routes } from '@angular/router';
import { RecruitmentApplicantsComponent } from './recruitment-applicants.component';
import { RecruitmentApplicantListPageComponent } from './pages/recruitment-applicant-list-page/recruitment-applicant-list-page.component';
import { RecruitmentApplicantDetailPageComponent } from './pages/recruitment-applicant-detail-page/recruitment-applicant-detail-page.component';

export const routes = [
    {
        path: '',
        component: RecruitmentApplicantsComponent,
        children: [
            {
                path: '',
                component: RecruitmentApplicantListPageComponent,
                data: {
                    title: 'translate_recruitment_applicants',
                },
            },

            {
                path: ':id',
                component: RecruitmentApplicantDetailPageComponent,
                data: {
                    title: 'translate_recruitment_applicant_details',
                },
            },
        ],
    },
] satisfies Routes;
