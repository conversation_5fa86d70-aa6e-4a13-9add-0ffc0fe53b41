import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { EducationalQualificationsService } from '../../../../foundation/educational-qualifications/educational-qualifications.service';

@Component({
    selector: 'lib-recruitment-applicant-list-page',
    templateUrl: './recruitment-applicant-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
    providers: [CountriesService, EducationalQualificationsService],
})
export class RecruitmentApplicantListPageComponent {
    protected config = config(inject(Injector));
}
