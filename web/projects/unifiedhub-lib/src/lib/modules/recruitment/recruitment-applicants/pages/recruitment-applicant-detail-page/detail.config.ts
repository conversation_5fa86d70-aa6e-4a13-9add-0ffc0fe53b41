import { RecruitmentApplicantsService } from '../../recruitment-applicants.service';
import { DetailPageConfig } from '../../../../../features';
import {
    RecruitmentApplicant,
    VacancyApplicationFilterAttrs,
} from '../../../core';
import { Injector } from '@angular/core';
import { RecruitmentApplicantProfileComponent } from '../../../careers/recruitment-applicant-profile/recruitment-applicant-profile.component';
import { VacancyApplicationListComponent } from '../../../core/components/vacancy-application-list/vacancy-application-list.component';

export const config = (
    injector: Injector,
): DetailPageConfig<RecruitmentApplicant> => {
    const recruitmentApplicantsService = injector.get(
        RecruitmentApplicantsService,
    );

    return {
        loader: recruitmentApplicantsService.get.bind(
            recruitmentApplicantsService,
        ),
        config: (item, itemId) => ({
            title: 'translate_recruitment_applicants',
            subtitle: 'translate_recruitment_applicant_details',
            sectionConfigs: [
                {
                    title: 'translate_recruitment_applicant_details',
                    type: 'component',
                    component: RecruitmentApplicantProfileComponent,
                    inputs: {
                        preloadedApplicant: {
                            isLoading: !item,
                            applicant: item,
                        },
                        isEditable: false,
                    },
                },

                {
                    title: 'translate_vacancy_applications',
                    type: 'component',
                    component: VacancyApplicationListComponent,
                    inputs: {
                        filter: {
                            applicantIds: [itemId],
                            filterCountExclusions: ['applicant_ids'],
                        } satisfies VacancyApplicationFilterAttrs,
                        hiddenColumns: {
                            applicant: true,
                        },
                        hiddenFilters: {
                            applicant: true,
                        },
                    },
                },
            ],
        }),
    };
};
