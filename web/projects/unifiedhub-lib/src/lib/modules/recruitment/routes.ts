import { Routes } from '@angular/router';
import { loadTranslationsResolver } from '@ng-omar/translation';

export const routes = [
    {
        path: '',
        resolve: { translations: loadTranslationsResolver },
        data: {
            module: 'recruitment',
        },
        children: [
            {
                path: 'vacancies',
                loadChildren: () =>
                    import('./vacancies/routes').then(x => x.routes),
            },

            {
                path: 'applicants',
                loadChildren: () =>
                    import('./recruitment-applicants/routes').then(
                        x => x.routes,
                    ),
            },

            {
                path: 'applications',
                loadChildren: () =>
                    import('./vacancy-applications/routes').then(x => x.routes),
            },
        ],
    },
] satisfies Routes;
