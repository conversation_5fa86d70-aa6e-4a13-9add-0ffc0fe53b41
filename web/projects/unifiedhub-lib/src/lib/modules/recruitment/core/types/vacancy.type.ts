import { MultilingualString } from '../../../../common';
import { Country, EducationalQualification } from '../../../foundation/core';
import { JobClassification } from '../../../employment/core';
import { VacancyApplication } from './vacancy-application.type';

export type Vacancy = {
    id: string;
    name: MultilingualString;
    description?: MultilingualString;
    conditions: MultilingualString[];
    reapplicationWindowInDays?: number;
    returnedApplicationEditWindowInDays?: number;
    jobClassification?: JobClassification;
    isActive: boolean;
    isPublished: boolean;
    from?: Date;
    to?: Date;
    maxApplicationCount?: number;
    maxApplicationsPerDayCount?: number;
    genders: string[];
    ageFrom?: number;
    ageTo?: number;
    nationalities: Country[];
    educationalQualifications: EducationalQualification[];
    canApply: boolean;
    currentApplication?: VacancyApplication;
};
