import { RecruitmentApplicant } from './recruitment-applicant.type';
import { Vacancy } from './vacancy.type';
import { ServiceRequest } from '../../../servicing';

export type VacancyApplication = {
    id: string;
    createdTime: Date;
    referenceNumber: string;
    vacancy: Vacancy;
    applicant: RecruitmentApplicant;
    flowState: 'draft' | 'processing' | 'rejected' | 'approved';
    submissionTime?: Date;
    returnTime?: Date;
    decisionTime?: Date;
    canReapply: boolean;
    serviceRequest: ServiceRequest;
};
