import { DynamicListFullConfig } from '../../../../../features';
import { VacancyApplication, VacancyApplicationFilterAttrs } from '../../types';
import { p } from '../../../../../permissions';
import { Injector } from '@angular/core';
import { FlowsService } from '../../../../../features/flows/services/flows.service';
import { map, Observable } from 'rxjs';
import { RecruitmentApplicantsService } from '../../../recruitment-applicants';
import { VacanciesService } from '../../../vacancies/vacancies.service';
import { VacancyApplicationsService } from '../../../vacancy-applications/vacancy-applications.service';
import { Filter, PaginatedResult } from '../../../../../common';

export const config = (
    injector: Injector,
    filter?: VacancyApplicationFilterAttrs,
    fetcher?: (
        filter: Filter<VacancyApplicationFilterAttrs>,
    ) => Observable<PaginatedResult<VacancyApplication>>,
    hiddenColumns?: {
        vacancy?: boolean;
        applicant?: boolean;
    },
    hiddenFilters?: {
        vacancy?: boolean;
        applicant?: boolean;
    },
): DynamicListFullConfig<VacancyApplication, VacancyApplicationFilterAttrs> => {
    const vacancyApplicationsService = injector.get(VacancyApplicationsService);

    return {
        persistentFilter: filter,
        columnConfigFn: () => [
            {
                label: 'translate_vacancy_name',
                isHidden: hiddenColumns?.vacancy,
            },

            {
                label: 'translate_applicant_name',
                isHidden: hiddenColumns?.applicant,
            },

            {
                label: 'translate_reference_number',
            },

            {
                label: 'translate_application_time',
            },

            {
                label: 'translate_submission_time',
            },

            {
                label: 'translate_return_time',
            },

            {
                label: 'translate_decision_time',
            },

            {
                label: 'translate_state',
            },

            {
                label: 'translate_service_request',
                permissionId: p.servicing.requests.read,
            },
        ],

        valueConfigFn: item => [
            {
                value: item?.vacancy?.name,
                linkConfig: item
                    ? {
                          value: [
                              '',
                              'recruitment',
                              'vacancies',
                              item.vacancy.id,
                          ],
                          permissionId: p.recruitment.vacancies.read,
                          shouldOpenInNewTab: true,
                      }
                    : undefined,
            },

            {
                value: item?.applicant?.name,
                linkConfig: {
                    value: item
                        ? ['', 'recruitment', 'applicants', item.applicant.id]
                        : undefined,
                    permissionId: p.recruitment.applicants.read,
                    shouldOpenInNewTab: true,
                },
            },

            {
                value: item?.referenceNumber,
            },

            {
                value: item?.createdTime,
                type: 'datetime',
            },

            {
                value: item?.submissionTime,
                type: 'datetime',
            },

            {
                value: item?.returnTime,
                type: 'datetime',
            },

            {
                value: item?.decisionTime,
                type: 'datetime',
            },

            {
                value: item
                    ? `translate_flow_state_${item.flowState}`
                    : undefined,
            },

            {
                value: 'translate_go_to_request_details',
                linkConfig: {
                    value: [
                        '',
                        'servicing',
                        'requests',
                        item!.serviceRequest.id,
                    ],
                    shouldOpenInNewTab: true,
                },
            },
        ],

        filter: [
            {
                id: 'keyword',
                type: 'text',
                label: 'translate_search_by_name',
            },

            {
                id: 'from',
                type: 'datetime',
                label: 'translate_from',
            },

            {
                id: 'to',
                type: 'datetime',
                label: 'translate_to',
            },

            {
                id: 'referenceNumber',
                type: 'text',
                label: 'translate_reference_number',
            },

            {
                id: 'flowStates',
                type: 'select',
                label: 'translate_state',
                items$: injector
                    .get(FlowsService)
                    .states('vacancy-applications')
                    .pipe(
                        map(x =>
                            x.map(y => ({
                                id: y,
                                name: `translate_flow_state_${y}`,
                            })),
                        ),
                    ),
                bindValue: 'id',
                bindLabel: 'name',
                isMulti: true,
            },

            {
                id: 'vacancyIds',
                type: 'select',
                label: 'translate_vacancy',
                bindValue: 'id',
                bindLabel: 'name',
                isMulti: true,
                isHidden: hiddenFilters?.vacancy,
                loaderFetcher: keyword =>
                    injector
                        .get(VacanciesService)
                        .simpleList({ attrs: { keyword } })
                        .pipe(map(x => x.items)),
            },

            {
                id: 'applicantIds',
                type: 'select',
                label: 'translate_applicant',
                bindValue: 'id',
                bindLabel: 'name',
                isMulti: true,
                isHidden: hiddenFilters?.applicant,
                loaderFetcher: keyword =>
                    injector
                        .get(RecruitmentApplicantsService)
                        .simpleList({ attrs: { keyword } })
                        .pipe(map(x => x.items)),
            },
        ],
        isEditLinkHidden: true,
        actionsFn: item => [
            {
                type: 'success',
                iconClasses: 'fa fa-eye',
                linkConfig: {
                    value: ['', 'recruitment', 'applications', item.id],
                },
            },
        ],
        fetcher:
            fetcher ??
            vacancyApplicationsService.list.bind(vacancyApplicationsService),
    };
};
