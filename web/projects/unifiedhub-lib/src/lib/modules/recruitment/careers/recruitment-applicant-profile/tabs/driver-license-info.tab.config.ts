import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { FoundationService } from '../../../../foundation/foundation.service';
import { FormArray } from '@angular/forms';

export const driverLicenseInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_driver_license_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateDriverLicenseInfo(item),
                        formConfig: mode => [
                            {
                                type: 'input',
                                id: 'hasDriverLicense',
                                label: 'translate_has_driver_license',
                                disabled: mode === 'view',
                                props: {
                                    type: 'checkbox',
                                },
                                onValueChange: (value, group, fields) => {
                                    const licensesField =
                                        fields['driverLicenses'];
                                    const licenseControl = group.controls[
                                        'driverLicenses'
                                    ] as FormArray;

                                    if (!value) licenseControl?.clear();
                                    licensesField.hide = !value;
                                },
                            },

                            {
                                type: 'repeat',
                                id: 'driverLicenses',
                                label: 'translate_driver_licenses',
                                disabled: mode === 'view',
                                hide: !applicant?.hasDriverLicense,
                                props: {
                                    repeatedConfig: {
                                        type: 'group',
                                        direction: 'col',
                                        config: [
                                            {
                                                id: 'id',
                                                type: 'hidden',
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'type',
                                                        type: 'select',
                                                        label: 'translate_type',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    FoundationService,
                                                                )
                                                                .driverLicenseTypes(),
                                                            bindLabel: 'name',
                                                            bindValue: 'id',
                                                        },
                                                    },

                                                    {
                                                        id: 'number',
                                                        type: 'input',
                                                        label: 'translate_number',
                                                        disabled:
                                                            mode === 'view',
                                                    },

                                                    {
                                                        id: 'issuingAuthority',
                                                        type: 'input',
                                                        label: 'translate_issuing_authority',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'issueDate',
                                                        type: 'datetime',
                                                        label: 'translate_issue_date',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            dateOnly: true,
                                                        },
                                                    },

                                                    {
                                                        id: 'expirationDate',
                                                        type: 'datetime',
                                                        label: 'translate_expiration_date',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            dateOnly: true,
                                                        },
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
