import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { RecruitmentApplicantProfileFilesService } from '../services/recruitment-applicant-profile-files.service';

export const passportInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_passport_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updatePassportInfo(item),
                        formConfig: mode => [
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'passportNumber',
                                        label: 'translate_passport_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'passportIssueDate',
                                        label: 'translate_passport_issue_date',
                                        type: 'datetime',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'passportExpiryDate',
                                        label: 'translate_passport_expiry_date',
                                        type: 'datetime',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'passportIssueAuthority',
                                        label: 'translate_passport_issue_authority',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'passportCountry',
                                        label: 'translate_passport_country',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CountriesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'passportFrontFile',
                                        label: 'translate_passport_front_file',
                                        type: 'file',
                                        disabled: mode === 'view',
                                        props: {
                                            fileDownloader: file =>
                                                injector
                                                    .get(
                                                        RecruitmentApplicantProfileFilesService,
                                                    )
                                                    .getPassportFront(file.id!),
                                        },
                                    },

                                    {
                                        id: 'passportBackFile',
                                        label: 'translate_passport_back_file',
                                        type: 'file',
                                        disabled: mode === 'view',
                                        props: {
                                            fileDownloader: file =>
                                                injector
                                                    .get(
                                                        RecruitmentApplicantProfileFilesService,
                                                    )
                                                    .getPassportBack(file.id!),
                                        },
                                    },
                                ],
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
