import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { CitiesService } from '../../../../foundation/cities/cities.service';
import { SuburbsService } from '../../../../foundation/suburbs/suburbs.service';

export const addressInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_address_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateAddressInfo(item),
                        formConfig: mode => [
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'city',
                                        label: 'translate_city',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CitiesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },

                                    {
                                        id: 'suburb',
                                        label: 'translate_suburb',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(SuburbsService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'address',
                                        label: 'translate_address',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'phoneNumber',
                                        label: 'translate_phone_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'offshoreAddressCountry',
                                        label: 'translate_offshore_address_country',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CountriesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },

                                    {
                                        id: 'offshoreAddressCity',
                                        label: 'translate_offshore_address_city',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'offshoreAddressArea',
                                        label: 'translate_offshore_address_area',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'offshoreAddressStreet',
                                        label: 'translate_offshore_address_street',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'offshoreAddressBuildingNumber',
                                        label: 'translate_offshore_address_building_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'offshoreAddressFloorNumber',
                                        label: 'translate_offshore_address_floor_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'offshoreAddressUnitNumber',
                                        label: 'translate_offshore_address_unit_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'offshorePhoneNumber',
                                        label: 'translate_offshore_phone_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'offshoreLandlineNumber',
                                        label: 'translate_offshore_landline_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'offshoreOtherPhoneNumber',
                                        label: 'translate_offshore_other_phone_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
