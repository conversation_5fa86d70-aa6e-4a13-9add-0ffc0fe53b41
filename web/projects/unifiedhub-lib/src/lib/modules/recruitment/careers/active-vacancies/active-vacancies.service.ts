import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../../config';
import {
    Filter,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../../common';
import { Observable } from 'rxjs';
import { Vacancy, VacancyApplication } from '../../core';

@Injectable()
export class ActiveVacanciesService {
    public constructor(
        private readonly httpClient: HttpClient,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {}

    public list(filter: Filter<unknown>): Observable<PaginatedResult<Vacancy>> {
        return this.httpClient.get<PaginatedResult<Vacancy>>(
            `${this.config.apiUrl}/recruitment/vacancies/active`,
            {
                params: mapFilterToQueryParams(filter),
            },
        );
    }

    public get(id: string): Observable<Vacancy> {
        return this.httpClient.get<Vacancy>(
            `${this.config.apiUrl}/recruitment/vacancies/active/${id}`,
        );
    }

    public apply(id: string): Observable<VacancyApplication> {
        return this.httpClient.post<VacancyApplication>(
            `${this.config.apiUrl}/recruitment/vacancies/${id}/apply`,
            undefined,
        );
    }
}
