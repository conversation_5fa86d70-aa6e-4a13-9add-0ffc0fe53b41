import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { VacancyApplicationsService } from '../../vacancy-applications/vacancy-applications.service';
import { CareersVacancyApplicationsService } from './careers-vacancy-applications.service';

@Component({
    selector: 'lib-careers-vacancy-applications',
    template: '<router-outlet/>',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [CareersVacancyApplicationsService, VacancyApplicationsService],
    imports: [RouterOutlet],
})
export class CareersVacancyApplicationsComponent {}
