import { Routes } from '@angular/router';
import { loadTranslationsResolver } from '@ng-omar/translation';
import { authorizeGuard } from '../../../core';

export const routes = [
    {
        path: 'profile',
        loadChildren: () =>
            import('./recruitment-applicant-profile/routes').then(
                x => x.routes,
            ),
        canActivate: [authorizeGuard.canActivate],
        resolve: { translations: loadTranslationsResolver },
        data: { module: 'careers' },
    },

    {
        path: 'vacancies',
        loadChildren: () =>
            import('./active-vacancies/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: { module: 'careers' },
    },

    {
        path: 'applications',
        loadChildren: () =>
            import('./careers-vacancy-applications/routes').then(x => x.routes),
        canActivate: [authorizeGuard.canActivate],
        resolve: { translations: loadTranslationsResolver },
        data: { module: 'careers' },
    },

    {
        path: '',
        redirectTo: 'vacancies',
        pathMatch: 'full',
    },
] satisfies Routes;
