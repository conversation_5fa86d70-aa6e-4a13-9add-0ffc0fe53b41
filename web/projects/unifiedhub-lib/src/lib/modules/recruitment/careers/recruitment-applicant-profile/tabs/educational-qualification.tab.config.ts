import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import { FoundationService } from '../../../../foundation/foundation.service';
import { EducationalQualificationsService } from '../../../../foundation/educational-qualifications/educational-qualifications.service';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { EducationalSpecializationsService } from '../../../../foundation/educational-specializations/educational-specializations.service';
import { RecruitmentApplicantProfileFilesService } from '../services/recruitment-applicant-profile-files.service';

export const educationalQualificationTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_educational_qualifications',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateEducationalQualificationInfo(
                                item,
                            ),
                        formConfig: mode => [
                            {
                                type: 'repeat',
                                id: 'educationalQualifications',
                                label: 'translate_educational_qualifications',
                                disabled: mode === 'view',
                                props: {
                                    repeatedConfig: {
                                        type: 'group',
                                        direction: 'col',
                                        config: [
                                            {
                                                id: 'id',
                                                type: 'hidden',
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'qualification',
                                                        type: 'select',
                                                        label: 'translate_qualification',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    EducationalQualificationsService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                        },
                                                    },

                                                    {
                                                        id: 'specialization',
                                                        type: 'select',
                                                        label: 'translate_specialization',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    EducationalSpecializationsService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                        },
                                                    },

                                                    {
                                                        id: 'instituteName',
                                                        type: 'input',
                                                        label: 'translate_institute_name',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'placeOfStudy',
                                                        type: 'input',
                                                        label: 'translate_place_of_study',
                                                        disabled:
                                                            mode === 'view',
                                                    },

                                                    {
                                                        id: 'graduationDate',
                                                        type: 'datetime',
                                                        label: 'translate_graduation_date',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            dateOnly: true,
                                                        },
                                                    },

                                                    {
                                                        id: 'score',
                                                        type: 'input',
                                                        label: 'translate_score',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'grade',
                                                        type: 'select',
                                                        label: 'translate_grade',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    FoundationService,
                                                                )
                                                                .educationalQualificationGrades(),
                                                            bindLabel: 'name',
                                                            bindValue: 'id',
                                                        },
                                                    },

                                                    {
                                                        id: 'certificateFile',
                                                        type: 'file',
                                                        label: 'translate_certificate_file',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            fileDownloader:
                                                                file =>
                                                                    injector
                                                                        .get(
                                                                            RecruitmentApplicantProfileFilesService,
                                                                        )
                                                                        .getEducationalQualificationCertificate(
                                                                            file.id!,
                                                                        ),
                                                        },
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
