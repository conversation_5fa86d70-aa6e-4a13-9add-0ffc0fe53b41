import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    model,
    OnChanges,
    signal,
} from '@angular/core';
import {
    DynamicFormConfig,
    DynamicNewFormComponent,
    DynamicNewFormConfig,
} from '../../../../../../features';
import { TranslateModule } from '@ngx-translate/core';
import { RecruitmentApplicant } from '../../../../core';
import { Observable } from 'rxjs';
import { smartMap } from '../../../../../../common';

export type ApplicantDetailFormConfig = {
    updater: (item: unknown) => Observable<void>;
    formConfig: (mode: 'edit' | 'view') => DynamicFormConfig[];
};

@Component({
    selector: 'lib-applicant-detail-form',
    templateUrl: './applicant-detail-form.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, DynamicNewFormComponent],
})
export class ApplicantDetailFormComponent implements OnChanges {
    public readonly config = input.required<ApplicantDetailFormConfig>();
    public readonly applicant = input<RecruitmentApplicant>();
    public readonly isEditable = input<boolean>(true);

    protected readonly data = model<RecruitmentApplicant | undefined>(
        undefined,
    );

    protected readonly isEditing = signal<boolean>(false);
    protected readonly newFormConfig = computed<DynamicNewFormConfig<unknown>>(
        () => {
            return {
                creator: item => this.config().updater(item),
                submitButtonLabels: {
                    createLabel: 'translate_save',
                },
                onCreated: () => {
                    this.isEditing.set(false);
                    smartMap(this.applicant()!, this.data()!);
                },
                formConfig: this.config().formConfig(
                    this.isEditing() ? 'edit' : 'view',
                ),
            };
        },
    );

    public ngOnChanges(): void {
        this.data.set(structuredClone(this.applicant()));
    }

    protected startEditing(): void {
        this.isEditing.set(true);
    }

    protected cancelEditing(): void {
        this.data.set(structuredClone(this.applicant()));
        this.isEditing.set(false);
    }
}
