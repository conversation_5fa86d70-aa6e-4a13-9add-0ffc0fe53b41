import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { FoundationService } from '../../../../foundation/foundation.service';
import { MiscService } from '../services/misc.service';

export const residencyInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_residency_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateResidencyInfo(item),
                        formConfig: mode => [
                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'oldNationality',
                                        label: 'translate_old_nationality',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CountriesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },

                                    {
                                        id: 'secondNationality',
                                        label: 'translate_second_nationality',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CountriesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'nationalityAcquisitionMethod',
                                        label: 'translate_nationality_acquisition_method',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(FoundationService)
                                                .nationalityAcquisitionMethods(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },

                                    {
                                        id: 'nationalityAcquisitionDate',
                                        label: 'translate_nationality_acquisition_date',
                                        type: 'datetime',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'countryEntryDate',
                                        label: 'translate_country_entry_date',
                                        type: 'datetime',
                                        disabled: mode === 'view',
                                        props: {
                                            dateOnly: true,
                                        },
                                    },

                                    {
                                        id: 'visaType',
                                        label: 'translate_visa_type',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(MiscService)
                                                .visaTypes(),
                                            bindLabel: 'name',
                                            bindValue: 'id',
                                        },
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'visaNumber',
                                        label: 'translate_visa_number',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'portOfEntry',
                                        label: 'translate_port_of_entry',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },

                            {
                                type: 'group',
                                config: [
                                    {
                                        id: 'entryFromCountry',
                                        label: 'translate_entry_from_country',
                                        type: 'select',
                                        disabled: mode === 'view',
                                        props: {
                                            items$: injector
                                                .get(CountriesService)
                                                .listAll(),
                                            bindLabel: 'name',
                                        },
                                    },

                                    {
                                        id: 'currentSponsorName',
                                        label: 'translate_current_sponsor_name',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },

                                    {
                                        id: 'previousSponsorName',
                                        label: 'translate_previous_sponsor_name',
                                        type: 'input',
                                        disabled: mode === 'view',
                                    },
                                ],
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
