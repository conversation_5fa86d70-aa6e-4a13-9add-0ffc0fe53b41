import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { config } from './list.config';
import { DynamicListFullComponent } from '../../../../../../features';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'lib-careers-vacancy-application-list-page',
    templateUrl: './careers-vacancy-application-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListFullComponent],
    providers: [DatePipe],
})
export class CareersVacancyApplicationListPageComponent {
    protected readonly config = config(inject(Injector));
}
