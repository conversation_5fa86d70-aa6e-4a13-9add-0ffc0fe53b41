import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { FoundationService } from '../../../../foundation/foundation.service';

export const socialMediaAccountInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_social_media_accounts_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateSocialMediaAccountInfo(
                                item,
                            ),
                        formConfig: mode => [
                            {
                                type: 'repeat',
                                id: 'socialMediaAccounts',
                                label: 'translate_social_media_accounts',
                                disabled: mode === 'view',
                                props: {
                                    repeatedConfig: {
                                        type: 'group',
                                        config: [
                                            {
                                                id: 'id',
                                                type: 'hidden',
                                            },

                                            {
                                                id: 'type',
                                                type: 'select',
                                                label: 'translate_type',
                                                disabled: mode === 'view',
                                                props: {
                                                    items$: injector
                                                        .get(FoundationService)
                                                        .socialMediaPlatforms(),
                                                    bindLabel: 'name',
                                                    bindValue: 'id',
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
