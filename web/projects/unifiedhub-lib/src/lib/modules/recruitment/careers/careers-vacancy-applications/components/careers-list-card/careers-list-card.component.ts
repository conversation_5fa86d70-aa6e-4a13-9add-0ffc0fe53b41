import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MaybeAsync, RouterLink } from '@angular/router';
import { AsyncIfObservablePipe } from '../../../../../../common';

@Component({
    selector: 'lib-careers-list-card',
    templateUrl: 'careers-list-card.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, RouterLink, AsyncIfObservablePipe],
})
export class CareersListCardComponent {
    public readonly cardTitle = input.required<MaybeAsync<string>>();
    public readonly cardLink = input.required<MaybeAsync<string>>();
    public readonly cardDescription = input<MaybeAsync<string>>();
    public readonly cardBadges = input<MaybeAsync<string[]>>();
}
