import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { FamilyRelationTypesService } from '../../../../foundation/family-relation-types/family-relation-types.service';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { ReligionsService } from '../../../../foundation/religions/religions.service';

export const friendsAndFamilyInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_friends_and_family_info',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateRelationInfo(item),
                        formConfig: mode => [
                            {
                                type: 'repeat',
                                id: 'relations',
                                label: 'translate_relations',
                                disabled: mode === 'view',
                                props: {
                                    repeatedConfig: {
                                        type: 'group',
                                        direction: 'col',
                                        config: [
                                            {
                                                id: 'id',
                                                type: 'hidden',
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'name',
                                                        type: 'input',
                                                        label: 'translate_name',
                                                        disabled:
                                                            mode === 'view',
                                                    },

                                                    {
                                                        id: 'relationType',
                                                        type: 'select',
                                                        label: 'translate_relation_type',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    FamilyRelationTypesService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                            bindValue: 'id',
                                                        },
                                                    },

                                                    {
                                                        id: 'nationality',
                                                        label: 'translate_nationality',
                                                        type: 'select',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    CountriesService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                        },
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'placeOfBirth',
                                                        label: 'translate_place_of_birth',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                    },

                                                    {
                                                        id: 'dateOfBirth',
                                                        label: 'translate_date_of_birth',
                                                        type: 'datetime',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            dateOnly: true,
                                                        },
                                                    },

                                                    {
                                                        id: 'phoneNumber',
                                                        label: 'translate_phone_number',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'religion',
                                                        label: 'translate_religion',
                                                        type: 'select',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    ReligionsService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                        },
                                                    },

                                                    {
                                                        id: 'isWorkingWithPolice',
                                                        label: 'translate_is_working_with_police',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            type: 'checkbox',
                                                        },
                                                    },

                                                    {
                                                        id: 'jobName',
                                                        label: 'translate_job_name',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },

                                            {
                                                type: 'group',
                                                config: [
                                                    {
                                                        id: 'jobRole',
                                                        label: 'translate_job_role',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                    },

                                                    {
                                                        id: 'countryOfLiving',
                                                        label: 'translate_country_of_living',
                                                        type: 'select',
                                                        disabled:
                                                            mode === 'view',
                                                        props: {
                                                            items$: injector
                                                                .get(
                                                                    CountriesService,
                                                                )
                                                                .listAll(),
                                                            bindLabel: 'name',
                                                        },
                                                    },

                                                    {
                                                        id: 'placeOfLiving',
                                                        label: 'translate_place_of_living',
                                                        type: 'input',
                                                        disabled:
                                                            mode === 'view',
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
