import {
    ChangeDetectionStrategy,
    Component,
    input,
    output,
    signal,
} from '@angular/core';
import {
    AlertComponent,
    LoadingButtonComponent,
} from '../../../../../../../../ui';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActiveVacanciesService } from '../../../../active-vacancies.service';
import { finalize } from 'rxjs';
import { ToastService } from '../../../../../../../../core';
import { VacancyApplication } from '../../../../../../core';

@Component({
    selector: 'lib-vacancy-application',
    templateUrl: 'vacancy-application.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [AlertComponent, TranslateModule, LoadingButtonComponent],
})
export class VacancyApplicationComponent {
    public readonly vacancyId = input.required<string>();
    public readonly apply = output<VacancyApplication>();

    protected readonly isLoading = signal<boolean>(false);

    public constructor(
        private readonly activeVacanciesService: ActiveVacanciesService,
        private readonly toastService: ToastService,
        private readonly translateService: TranslateService,
    ) {}

    protected sendApplication(): void {
        if (this.isLoading()) return;
        this.isLoading.set(true);

        this.activeVacanciesService
            .apply(this.vacancyId())
            .pipe(finalize(() => this.isLoading.set(false)))
            .subscribe(application => {
                this.apply.emit(application);
                this.translateService
                    .get(
                        'translate_application_sent_successfully_your_reference_number_is_reference_number',
                        { referenceNumber: application.referenceNumber },
                    )
                    .subscribe(str => this.toastService.success(str));
            });
    }
}
