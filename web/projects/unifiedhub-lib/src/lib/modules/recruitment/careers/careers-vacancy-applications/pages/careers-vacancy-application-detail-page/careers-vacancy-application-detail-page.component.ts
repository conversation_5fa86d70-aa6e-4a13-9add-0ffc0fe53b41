import {
    ChangeDetectionStrategy,
    Component,
    computed,
    Injector,
    signal,
    viewChild,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { config } from './detail.config';
import {
    DynamicDetailComponent,
    SmartAlertService,
} from '../../../../../../features';
import { TranslateModule } from '@ngx-translate/core';
import { VacancyApplication } from '../../../../core';
import { ToastService } from '../../../../../../core';
import { LoadingButtonComponent } from '../../../../../../ui';
import { finalize } from 'rxjs';
import { CareersVacancyApplicationsService } from '../../careers-vacancy-applications.service';

@Component({
    selector: 'lib-careers-vacancy-application-detail-page',
    templateUrl: './careers-vacancy-application-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailComponent, TranslateModule, LoadingButtonComponent],
})
export class CareersVacancyApplicationDetailPageComponent {
    protected readonly application = signal<VacancyApplication | undefined>(
        undefined,
    );
    protected readonly isLoading = signal<boolean>(false);
    protected readonly config = computed(() => {
        if (!this.applicationId()) return;
        return config(this.applicationId()!, this.injector);
    });

    private readonly applicationId = signal<string | undefined>(undefined);

    private readonly detailComponent =
        viewChild<DynamicDetailComponent<VacancyApplication>>(
            'detailComponent',
        );

    public constructor(
        private readonly injector: Injector,
        private readonly smartAlertService: SmartAlertService,
        private readonly careersVacancyApplicationsService: CareersVacancyApplicationsService,
        private readonly toastService: ToastService,
        activatedRoute: ActivatedRoute,
    ) {
        const applicationId = activatedRoute.snapshot.paramMap.get(
            'id',
        )! as string;
        this.applicationId.set(applicationId);
    }

    protected submit(): void {
        this.smartAlertService
            .confirm(
                'translate_are_you_sure_you_want_to_resubmit_this_application',
            )
            .subscribe(isConfirmed => {
                if (!isConfirmed || this.isLoading()) return;
                this.isLoading.set(true);
                this.careersVacancyApplicationsService
                    .resubmit(this.applicationId()!)
                    .pipe(finalize(() => this.isLoading.set(false)))
                    .subscribe(() => {
                        this.toastService.success(
                            'translate_operation_successful',
                        );
                        this.detailComponent()!.reload();
                    });
            });
    }
}
