import { RecruitmentApplicantProfileService } from '../services/recruitment-applicant-profile.service';
import { Injector } from '@angular/core';
import {
    ApplicantDetailFormComponent,
    ApplicantDetailFormConfig,
} from '../components/applicant-detail-form/applicant-detail-form.component';
import { RecruitmentApplicant } from '../../../core';
import { TalentsService } from '../../../../foundation/talents/talents.service';
import { MiscService } from '../services/misc.service';

export const talentInfoTabConfig = (
    applicant: RecruitmentApplicant | undefined,
    isEditable: boolean,
    injector: Injector,
): any => {
    const applicantsService = injector.get(RecruitmentApplicantProfileService);
    return {
        name: 'translate_talents',
        sectionConfigs: [
            {
                type: 'component',
                noContainer: true,
                component: ApplicantDetailFormComponent,
                inputs: {
                    applicant,
                    isEditable,
                    config: {
                        updater: item =>
                            applicantsService.updateTalentInfo(item),
                        formConfig: mode => [
                            {
                                type: 'repeat',
                                id: 'talents',
                                label: 'translate_talents',
                                disabled: mode === 'view',
                                props: {
                                    repeatedConfig: {
                                        type: 'group',
                                        config: [
                                            {
                                                id: 'id',
                                                type: 'hidden',
                                            },

                                            {
                                                id: 'name',
                                                type: 'input',
                                                label: 'translate_name',
                                                disabled: mode === 'view',
                                            },

                                            {
                                                id: 'talent',
                                                type: 'select',
                                                label: 'translate_talent',
                                                disabled: mode === 'view',
                                                props: {
                                                    items$: injector
                                                        .get(TalentsService)
                                                        .listAll(),
                                                    bindLabel: 'name',
                                                },
                                            },

                                            {
                                                id: 'category',
                                                type: 'select',
                                                label: 'translate_category',
                                                disabled: mode === 'view',
                                                props: {
                                                    items$: injector
                                                        .get(MiscService)
                                                        .talentCategories(),
                                                    bindLabel: 'name',
                                                    bindValue: 'id',
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    } satisfies ApplicantDetailFormConfig,
                },
            },
        ],
    };
};
