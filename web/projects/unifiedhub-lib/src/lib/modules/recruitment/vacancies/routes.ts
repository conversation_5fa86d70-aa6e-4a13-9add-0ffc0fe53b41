import { Routes } from '@angular/router';
import { VacanciesComponent } from './vacancies.component';
import { VacancyNewPageComponent } from './pages/vacancy-new-page/vacancy-new-page.component';
import { VacancyListPageComponent } from './pages/vacancy-list-page/vacancy-list-page.component';
import { VacancyDetailPageComponent } from './pages/vacancy-detail-page/vacancy-detail-page.component';

export const routes = [
    {
        path: '',
        component: VacanciesComponent,
        children: [
            {
                path: '',
                component: VacancyListPageComponent,
                data: {
                    title: 'translate_vacancies',
                },
            },

            {
                path: 'new',
                component: VacancyNewPageComponent,
                data: {
                    title: 'translate_add_new_vacancy',
                },
            },

            {
                path: 'edit/:id',
                component: VacancyNewPageComponent,
                data: {
                    title: 'translate_update_vacancy_details',
                },
            },

            {
                path: ':id',
                component: VacancyDetailPageComponent,
                data: {
                    title: 'translate_vacancy_details',
                },
            },
        ],
    },
] satisfies Routes;
