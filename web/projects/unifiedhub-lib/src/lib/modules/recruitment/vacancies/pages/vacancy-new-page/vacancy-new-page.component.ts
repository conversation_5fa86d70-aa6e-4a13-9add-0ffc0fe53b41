import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { CountriesService } from '../../../../foundation/countries/countries.service';
import { EducationalQualificationsService } from '../../../../foundation/educational-qualifications/educational-qualifications.service';
import { JobClassificationsService } from '../../../../employment/job-classifications/job-classifications.service';

@Component({
    selector: 'lib-vacancy-new-page',
    templateUrl: './vacancy-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        DynamicNewPageComponent,
    ],
    providers: [
        CountriesService,
        EducationalQualificationsService,
        JobClassificationsService,
    ],
})
export class VacancyNewPageComponent {
    protected configFn = configFn(inject(Injector));
}
