import { VacanciesService } from '../../vacancies.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { Vacancy, VacancyFilterAttrs } from '../../../core';

export const config = (
    vacanciesServices: VacanciesService,
): DynamicListPageConfig<Vacancy, VacancyFilterAttrs> => {
    return {
        title: 'translate_vacancies',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_job_classification',
                },

                {
                    label: 'translate_is_active',
                },

                {
                    label: 'translate_is_published',
                },

                {
                    label: 'translate_from',
                },

                {
                    label: 'translate_to',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'recruitment', 'vacancies', item.id],
                    },
                },

                {
                    value: item.jobClassification?.name,
                },

                {
                    value: item.isActive,
                    type: 'boolean',
                },

                {
                    value: item.isPublished,
                    type: 'boolean',
                },

                {
                    value: item.from,
                    type: 'date',
                },

                {
                    value: item.to,
                    type: 'date',
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.recruitment.vacancies.write,
                },
                deleteButtonConfig: {
                    permissionId: p.recruitment.vacancies.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: vacanciesServices.list.bind(vacanciesServices),
            deleter: vacanciesServices.delete.bind(vacanciesServices),
        },

        newButtonConfig: {
            permissionId: p.foundation.write,
        },
    };
};
