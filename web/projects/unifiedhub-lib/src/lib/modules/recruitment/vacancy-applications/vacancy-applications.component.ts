import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { VacancyApplicationsService } from './vacancy-applications.service';

@Component({
    selector: 'lib-vacancy-applications',
    template: '<router-outlet/>',
    standalone: true,
    providers: [VacancyApplicationsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VacancyApplicationsComponent {}
