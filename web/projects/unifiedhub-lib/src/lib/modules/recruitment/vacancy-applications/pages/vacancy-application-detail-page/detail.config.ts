import { VacancyApplicationsService } from '../../vacancy-applications.service';
import { DetailPageConfig } from '../../../../../features';
import { VacancyApplication } from '../../../core';
import { p } from '../../../../../permissions';

export const config = (
    vacancyApplicationsService: VacancyApplicationsService,
): DetailPageConfig<VacancyApplication> => {
    return {
        loader: vacancyApplicationsService.get.bind(vacancyApplicationsService),
        config: item => ({
            title: 'translate_vacancy_applications',
            subtitle: 'translate_vacancy_application_details',
            editButtonConfig: {
                isHidden: true,
            },
            sectionConfigs: [
                {
                    title: 'translate_vacancy_application_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_vacancy_name',
                        },

                        {
                            label: 'translate_applicant_name',
                        },

                        {
                            label: 'translate_reference_number',
                        },

                        {
                            label: 'translate_application_time',
                        },

                        {
                            label: 'translate_submission_time',
                        },

                        {
                            label: 'translate_return_time',
                        },

                        {
                            label: 'translate_decision_time',
                        },

                        {
                            label: 'translate_state',
                        },

                        {
                            label: 'translate_service_request',
                            permissionId: p.servicing.requests.read,
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.vacancy?.name,
                            linkConfig: item
                                ? {
                                      value: [
                                          '',
                                          'recruitment',
                                          'vacancies',
                                          item.vacancy.id,
                                      ],
                                      permissionId:
                                          p.recruitment.vacancies.read,
                                      shouldOpenInNewTab: true,
                                  }
                                : undefined,
                        },

                        {
                            value: item?.applicant?.name,
                            linkConfig: {
                                value: item
                                    ? [
                                          '',
                                          'recruitment',
                                          'applicants',
                                          item.applicant.id,
                                      ]
                                    : undefined,
                                permissionId: p.recruitment.applicants.read,
                                shouldOpenInNewTab: true,
                            },
                        },

                        {
                            value: item?.referenceNumber,
                        },

                        {
                            value: item?.createdTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.submissionTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.returnTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.decisionTime,
                            type: 'datetime',
                        },

                        {
                            value: item
                                ? `translate_flow_state_${item.flowState}`
                                : undefined,
                        },

                        {
                            value: 'translate_go_to_request_details',
                            linkConfig: {
                                value: [
                                    '',
                                    'servicing',
                                    'requests',
                                    item!.serviceRequest.id,
                                ],
                                shouldOpenInNewTab: true,
                            },
                        },
                    ],
                },
            ],
        }),
    };
};
