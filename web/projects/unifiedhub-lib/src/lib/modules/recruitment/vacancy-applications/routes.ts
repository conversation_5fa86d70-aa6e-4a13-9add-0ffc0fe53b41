import { Routes } from '@angular/router';
import { VacancyApplicationsComponent } from './vacancy-applications.component';
import { VacancyApplicationListPageComponent } from './pages/vacancy-application-list-page/vacancy-application-list-page.component';
import { VacancyApplicationDetailPageComponent } from './pages/vacancy-application-detail-page/vacancy-application-detail-page.component';

export const routes = [
    {
        path: '',
        component: VacancyApplicationsComponent,
        children: [
            {
                path: '',
                component: VacancyApplicationListPageComponent,
                data: {
                    title: 'translate_vacancy_applications',
                },
            },

            {
                path: ':id',
                component: VacancyApplicationDetailPageComponent,
                data: {
                    title: 'translate_vacancy_application_details',
                },
            },
        ],
    },
] satisfies Routes;
