import { SchedulesService } from '../../schedules.service';
import { Schedule } from '../../types';
import { p } from '../../../../../permissions';
import { DetailPageConfig, LinkerConfig } from '../../../../../features';
import { Filter } from '../../../../../common';
import { Employee } from '../../../../employment/core';
import { SampleScheduleComponent } from '../../components';

export const config = (
    schedulesService: SchedulesService,
): DetailPageConfig<Schedule> => {
    return {
        loader: schedulesService.get.bind(schedulesService),
        config: item => ({
            title: 'translate_schedules',
            subtitle: 'translate_schedule_details',
            editButtonConfig: {
                permissionId: p.attendance.schedules.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_basic_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_schedule_start_time',
                        },

                        {
                            label: 'translate_flex_duration_in_minutes',
                        },

                        {
                            label: 'translate_earliest_start_in_minutes',
                        },

                        {
                            label: 'translate_latest_end_in_minutes',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.startTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.flexOffsetInMinutes,
                        },

                        {
                            value: item?.earlyStartOffsetInMinutes,
                        },

                        {
                            value: item?.lateEndOffsetInMinutes,
                        },
                    ],
                },

                {
                    type: 'linker',
                    title: 'translate_employees_assigned_to_this_schedule',
                    config: {
                        linked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_name',
                                },
                            ],
                            valueConfigFn: item => [
                                {
                                    value: item.name,
                                },
                            ],
                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_search_by_name',
                                },
                            ],
                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                schedulesService.getEmployeeListAssignedToSchedule(
                                    item!.id,
                                    filter,
                                ),
                            transfer: employees =>
                                schedulesService.removeEmployeesFromSchedules(
                                    item!.id,
                                    employees.map(x => x.id),
                                ),
                            transferPermissionId: p.attendance.schedules.write,
                        },
                        unlinked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_name',
                                },
                            ],
                            valueConfigFn: employee => [
                                {
                                    value: employee.name,
                                },
                            ],
                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_search_by_name',
                                },
                            ],
                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                schedulesService.getEmployeeListNotAssignedToSchedule(
                                    item!.id,
                                    filter,
                                ),
                            transfer: employees =>
                                schedulesService.addEmployeesToSchedules(
                                    item!.id,
                                    employees.map(x => x.id),
                                ),
                        },
                    } satisfies LinkerConfig<Employee>,
                },

                {
                    type: 'component',
                    noContainer: true,
                    component: SampleScheduleComponent,
                    inputs: {
                        startTime: item ? new Date(item.startTime) : undefined,
                        records: item?.records,
                    },
                },
            ],
        }),
    };
};
