import { ScheduleRecord } from './schedule-record.type';
import { MultilingualString } from '../../../../common';

export type Schedule = {
    id: string;
    name: MultilingualString;
    startTime: Date;
    flexOffsetInMinutes: number;
    earlyStartOffsetInMinutes: number;
    lateStartOffsetInMinutes: number;
    earlyEndOffsetInMinutes: number;
    lateEndOffsetInMinutes: number;
    records: ScheduleRecord[];
};
