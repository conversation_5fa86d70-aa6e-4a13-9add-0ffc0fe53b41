@if (isValidState()) {
    <lib-content-container
        contentTitle="{{ 'translate_next_30_days_schedule' | translate }}"
    >
        <table content>
            <thead>
                <tr>
                    <th>
                        {{ 'translate_start_time' | translate }}
                    </th>
                    <th>
                        {{ 'translate_end_time' | translate }}
                    </th>
                </tr>
            </thead>
            <tbody>
                @for (
                    item of materializedSchedule();
                    let idx = $index;
                    track idx
                ) {
                    <tr>
                        <td class="text-center">
                            {{ item.start | date: 'EEEE yyyy-MM-dd hh:mm a' }}
                        </td>
                        <td class="text-center">
                            {{ item.end | date: 'EEEE yyyy-MM-dd  hh:mm a' }}
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </lib-content-container>
}
