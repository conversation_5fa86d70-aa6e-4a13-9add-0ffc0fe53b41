import {
    ChangeDetectionStrategy,
    Component,
    computed,
    signal,
    TemplateRef,
    viewChild,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { SchedulesService } from '../../schedules.service';
import { FormGroup } from '@angular/forms';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { map, of, switchMap } from 'rxjs';
import { SampleScheduleComponent } from '../../components';
import { ScheduleRecord } from '../../types';

@Component({
    selector: 'lib-schedule-new-page',
    templateUrl: './schedule-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        RouterLink,
        DynamicNewPageComponent,
        SampleScheduleComponent,
    ],
})
export class ScheduleNewPageComponent {
    protected configFn = computed(() => {
        return configFn(this.schedulesService, this.sampleScheduleTemplate());
    });
    protected form = signal<FormGroup | undefined>(undefined);

    protected startTime = toSignal<Date | undefined>(
        toObservable(this.form).pipe(
            switchMap(form => {
                if (!form) return of(undefined);
                return form.controls['startTime'].valueChanges.pipe(
                    map(x => new Date(x)),
                );
            }),
        ),
    );

    protected records = toSignal<ScheduleRecord[] | undefined>(
        toObservable(this.form).pipe(
            switchMap(form => {
                if (!form) return of(undefined);
                return form.controls['records'].valueChanges;
            }),
        ),
    );

    private sampleScheduleTemplate = viewChild<TemplateRef<any>>(
        'sampleScheduleTemplate',
    );

    public constructor(private readonly schedulesService: SchedulesService) {}
}
