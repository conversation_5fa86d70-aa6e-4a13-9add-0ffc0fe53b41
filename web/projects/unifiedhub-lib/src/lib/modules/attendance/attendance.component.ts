import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AttendanceService } from './attendance.service';
import { UsersService } from '../users/users.service';
import { EmployeesService } from '../employment/employees/employees.service';

@Component({
    selector: 'lib-attendance',
    template: '<router-outlet/>',
    standalone: true,
    providers: [AttendanceService, UsersService, EmployeesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AttendanceComponent {}
