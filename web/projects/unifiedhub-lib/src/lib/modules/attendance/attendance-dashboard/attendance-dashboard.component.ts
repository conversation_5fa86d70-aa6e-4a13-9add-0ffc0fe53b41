import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Chart, registerables } from 'chart.js';
import { TranslateModule } from '@ngx-translate/core';
import {
    AttendanceStatisticsComponent,
    AttendanceStatisticsPieChartComponent,
    ComplianceRateLineChartComponent,
} from './components';

Chart.register(...registerables);

@Component({
    selector: 'lib-attendance-dashboard',
    templateUrl: './attendance-dashboard.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        TranslateModule,
        AttendanceStatisticsComponent,
        ComplianceRateLineChartComponent,
        AttendanceStatisticsPieChartComponent,
    ],
})
export class AttendanceDashboardComponent {}
