<lib-content-container
    contentTitle="{{ 'translate_general_statistics' | translate }}"
>
    <div
        content
        *libWaitUntilListLoaded="generalStatisticsList()"
        [@verticalFlippingCardGrid]="generalStatisticsList()?.length"
        class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    >
        @for (item of generalStatisticsList(); track item.label) {
            <div
                [@verticalFlippingCard]
                class="flex flex-col items-center justify-center gap-2 rounded {{
                    item.color
                }} p-4 hover:-translate-y-1 transition-transform"
            >
                <div>{{ item.label | translate }}</div>

                <div class="text-3xl font-bold">
                    @if (item.value) {
                        @if (item.type === 'percentage') {
                            {{ item.value * 100 | round: 2 }}%
                        } @else {
                            {{ item.value }}
                        }
                    } @else {
                        N/A
                    }
                </div>
            </div>
        }
    </div>
</lib-content-container>
