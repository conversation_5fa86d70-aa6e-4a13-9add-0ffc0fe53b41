import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    input,
    signal,
} from '@angular/core';
import { AttendanceService } from '../../../attendance.service';
import { TranslateModule } from '@ngx-translate/core';
import { AttendanceGeneralStatistics } from '../../../types';
import { RoundPipe, verticalFlippingCard } from '../../../../../common';
import {
    ContentContainerComponent,
    WaitUntilListLoadedDirective,
} from '../../../../../ui';

@Component({
    selector: 'lib-attendance-statistics',
    templateUrl: './attendance-statistics.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [verticalFlippingCard],
    imports: [
        ContentContainerComponent,
        TranslateModule,
        WaitUntilListLoadedDirective,
        RoundPipe,
    ],
})
export class AttendanceStatisticsComponent {
    public readonly from = input<Date>(
        new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000),
    );
    public readonly to = input<Date>(new Date());

    protected readonly generalStatisticsList = computed<
        | {
              label: string;
              value?: number;
              type: 'number' | 'percentage';
              color: string;
          }[]
        | undefined
    >(() => {
        const generalStatistics = this.generalStatistics();
        if (!generalStatistics) return undefined;
        return [
            {
                label: 'translate_number_of_employees',
                value: generalStatistics.activeEmployeeCount,
                type: 'number',
                color: 'bg-blue-500',
            },

            {
                label: 'translate_number_of_transactions',
                value: generalStatistics.transactionCount,
                type: 'number',
                color: 'bg-green-500',
            },

            {
                label: 'translate_number_of_late_in',
                value: generalStatistics.lateInCount,
                type: 'number',
                color: 'bg-primary-500',
            },

            {
                label: 'translate_number_of_early_out',
                value: generalStatistics.earlyOutCount,
                type: 'number',
                color: 'bg-orange-500',
            },

            {
                label: 'translate_number_of_absence',
                value: generalStatistics.absenceCount,
                type: 'number',
                color: 'bg-red-500',
            },

            {
                label: 'translate_number_of_punches',
                value: generalStatistics.punchCount,
                type: 'number',
                color: 'bg-purple-500',
            },

            {
                label: 'translate_compliance_rate',
                value: generalStatistics.complianceRate,
                type: 'percentage',
                color: 'bg-yellow-500',
            },
        ];
    });

    private readonly generalStatistics = signal<
        AttendanceGeneralStatistics | undefined
    >(undefined);

    public constructor(attendanceService: AttendanceService) {
        effect(
            () => {
                attendanceService
                    .getGeneralStatistics(this.from(), this.to())
                    .subscribe(data => this.generalStatistics.set(data));
            },
            { allowSignalWrites: true },
        );
    }
}
