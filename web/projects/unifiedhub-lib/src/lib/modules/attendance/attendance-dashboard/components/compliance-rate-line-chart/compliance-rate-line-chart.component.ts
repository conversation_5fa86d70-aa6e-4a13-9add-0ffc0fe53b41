import {
    ChangeDetectionStrategy,
    Component,
    effect,
    ElementRef,
    Inject,
    input,
    signal,
    viewChild,
} from '@angular/core';
import { AttendanceComplianceRateTrendPoint } from '../../../types';
import { AttendanceService } from '../../../attendance.service';
import { forkJoin } from 'rxjs';
import { Chart } from 'chart.js';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/common';
import {
    ContentContainerComponent,
    WaitUntilListLoadedDirective,
} from '../../../../../ui';

@Component({
    selector: 'lib-compliance-rate-line-chart',
    templateUrl: 'compliance-rate-line-chart.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ContentContainerComponent,
        TranslateModule,
        WaitUntilListLoadedDirective,
    ],
})
export class ComplianceRateLineChartComponent {
    public readonly from = input<Date>(
        new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000),
    );
    public readonly to = input<Date>(new Date());

    protected readonly complianceRateTrend = signal<
        AttendanceComplianceRateTrendPoint[] | undefined
    >(undefined);

    private readonly complianceRateCanvas = viewChild<ElementRef>(
        'complianceRateCanvas',
    );

    public constructor(
        attendanceService: AttendanceService,
        translateService: TranslateService,
        @Inject(DOCUMENT) document: Document,
    ) {
        const style = getComputedStyle(document.documentElement);
        const primaryColor = style.getPropertyValue('--primary-500');
        effect(
            () => {
                attendanceService
                    .getComplianceRateTrend(this.from(), this.to())
                    .subscribe(items => this.complianceRateTrend.set(items));
            },
            { allowSignalWrites: true },
        );

        effect(() => {
            const complianceRateCanvas = this.complianceRateCanvas();
            const complianceRateTrend = this.complianceRateTrend();
            if (!complianceRateTrend || !complianceRateCanvas) return;

            forkJoin([
                translateService.get('translate_compliance_rate'),
            ]).subscribe(([title]) => {
                new Chart(complianceRateCanvas.nativeElement, {
                    type: 'line',
                    data: {
                        labels: complianceRateTrend.map(x =>
                            new Date(x.date).toLocaleDateString(),
                        ),
                        datasets: [
                            {
                                label: title,
                                data: complianceRateTrend.map(
                                    x => (x.value ?? 0) * 100,
                                ),
                                tension: 0.3,
                                backgroundColor: `rgb(${primaryColor})`,
                                borderColor: `rgb(${primaryColor})`,
                            },
                        ],
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                    },
                });
            });
        });
    }
}
