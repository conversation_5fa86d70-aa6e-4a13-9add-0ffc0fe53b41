import { Punch } from '../../punches';
import { MultilingualString } from '../../../../common';
import { Employee } from '../../../employment/core';

export type Transaction = {
    id: string;
    employee: Employee;
    scheduleName: MultilingualString;
    inTime: Date;
    inZoneName?: MultilingualString;
    inLongitude?: number;
    inLatitude?: number;
    inRadiusInMeters?: number;
    outZoneName?: MultilingualString;
    outLongitude?: number;
    outLatitude?: number;
    outRadiusInMeters?: number;
    outTime: Date;
    inPunch?: Punch;
    outPunch?: Punch;
    isAbsent: boolean;
    isLate: boolean;
    isEarly: boolean;
};
