import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TransactionsService } from './transactions.service';

@Component({
    selector: 'lib-transactions',
    template: '<router-outlet/>',
    standalone: true,
    providers: [TransactionsService],
    imports: [RouterOutlet],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TransactionsComponent {}
