import { ChangeDetectionStrategy, Component } from '@angular/core';
import { p } from '../../../../../permissions';
import { AttendancePermissionListComponent } from '../../components';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';
import { PageContainerComponent } from '../../../../../ui';
import { RbacDirective } from '../../../../../core';

@Component({
    selector: 'lib-zone-list-page',
    templateUrl: './attendance-permission-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PageContainerComponent,
        AttendancePermissionListComponent,
        TranslateModule,
        RouterLink,
        RbacDirective,
    ],
})
export class AttendancePermissionListPageComponent {
    protected readonly p = p;
}
