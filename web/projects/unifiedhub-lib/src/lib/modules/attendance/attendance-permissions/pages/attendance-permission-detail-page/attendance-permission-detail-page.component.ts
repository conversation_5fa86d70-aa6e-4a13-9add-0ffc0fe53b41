import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { AttendancePermissionsService } from '../../attendance-permissions.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-attendance-permission-detail-page',
    templateUrl: './attendance-permission-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class AttendancePermissionDetailPageComponent {
    protected readonly config = config(inject(AttendancePermissionsService));
}
