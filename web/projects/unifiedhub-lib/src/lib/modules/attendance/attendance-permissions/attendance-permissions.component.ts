import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { AttendancePermissionsService } from './attendance-permissions.service';

@Component({
    selector: 'lib-zones',
    template: '<router-outlet/>',
    standalone: true,
    providers: [AttendancePermissionsService],
    imports: [RouterOutlet],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AttendancePermissionsComponent {}
