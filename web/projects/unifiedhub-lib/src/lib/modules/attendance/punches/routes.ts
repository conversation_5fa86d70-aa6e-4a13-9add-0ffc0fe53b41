import { Routes } from '@angular/router';
import { PunchesComponent } from './punches.component';
import { PunchListPageComponent } from './pages/punch-list-page/punch-list-page.component';
import { p } from '../../../permissions';

export default [
    {
        path: '',
        component: PunchesComponent,
        children: [
            {
                path: '',
                component: PunchListPageComponent,
                data: {
                    title: 'translate_punches',
                    permissionIds: [p.attendance.punches.read],
                },
            },
        ],
    },
] satisfies Routes;
