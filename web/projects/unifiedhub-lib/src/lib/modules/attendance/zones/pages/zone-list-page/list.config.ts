import { ZonesService } from '../../zones.service';
import { inject } from '@angular/core';
import { Zone, ZoneFilterAttrs } from '../../types';
import { p } from '../../../../../permissions';
import { DynamicListPageConfig } from '../../../../../features';
import { Location } from '../../../../../common';

export const config = (): DynamicListPageConfig<Zone, ZoneFilterAttrs> => {
    const zonesService = inject(ZonesService);

    return {
        title: 'translate_attendance_zones',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_radius_in_meters',
                },

                {
                    label: 'translate_employee_count',
                },

                {
                    label: 'translate_location',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: { value: ['', 'attendance', 'zones', item.id] },
                },

                {
                    value: item.radiusInMeters,
                },

                {
                    value: item.employeeCount,
                },

                {
                    type: 'location',
                    value: {
                        longitude: item.longitude,
                        latitude: item.latitude,
                    } satisfies Location,
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.attendance.zones.write,
                },
                deleteButtonConfig: {
                    permissionId: p.attendance.zones.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],

            fetcher: zonesService.list.bind(zonesService),
            deleter: zonesService.delete.bind(zonesService),
        },

        newButtonConfig: {
            permissionId: p.attendance.zones.write,
        },
    };
};
