import { ZonesService } from '../../zones.service';
import { Zone } from '../../types';
import { p } from '../../../../../permissions';
import { DetailPageConfig, LinkerConfig } from '../../../../../features';
import { Filter } from '../../../../../common';
import { Employee } from '../../../../employment/core';

export const config = (zonesService: ZonesService): DetailPageConfig<Zone> => {
    return {
        loader: zonesService.get.bind(zonesService),
        config: item => ({
            title: 'translate_attendance_zones',
            subtitle: 'translate_zone_details',
            editButtonConfig: {
                permissionId: p.attendance.zones.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_basic_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_longitude',
                        },

                        {
                            label: 'translate_latitude',
                        },

                        {
                            label: 'translate_radius_in_meters',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.longitude,
                            type: 'number',
                        },

                        {
                            value: item?.latitude,
                            type: 'number',
                        },

                        {
                            value: item?.radiusInMeters,
                            type: 'number',
                        },
                    ],
                },

                {
                    type: 'linker',
                    title: 'translate_employees_assigned_to_this_zone',
                    config: {
                        linked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_name',
                                },
                            ],
                            valueConfigFn: employee => [
                                {
                                    value: employee.name,
                                },
                            ],
                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_search_by_name',
                                },
                            ],
                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                zonesService.getEmployeeListAssignedToZone(
                                    item!.id,
                                    filter,
                                ),
                            transfer: employees =>
                                zonesService.removeEmployeesFromZones(
                                    [item!.id],
                                    employees.map(x => x.id),
                                ),
                            transferPermissionId: p.attendance.zones.write,
                        },
                        unlinked: {
                            columnConfigFn: () => [
                                {
                                    label: 'translate_name',
                                },
                            ],
                            valueConfigFn: employee => [
                                {
                                    value: employee.name,
                                },
                            ],

                            filter: [
                                {
                                    id: 'keyword',
                                    type: 'text',
                                    label: 'translate_search_by_name',
                                },
                            ],

                            fetcher: (filter: Filter<{ keyword?: string }>) =>
                                zonesService.getEmployeeListNotAssignedToZone(
                                    item!.id,
                                    filter,
                                ),

                            transfer: employees => {
                                return zonesService.addEmployeesToZones(
                                    [item!.id],
                                    employees.map(x => x.id),
                                );
                            },
                        },
                    } satisfies LinkerConfig<Employee>,
                },
            ],
        }),
    };
};
