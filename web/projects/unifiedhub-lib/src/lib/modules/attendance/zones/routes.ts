import { Routes } from '@angular/router';
import { ZonesComponent } from './zones.component';
import { ZoneListPageComponent } from './pages/zone-list-page/zone-list-page.component';
import { ZoneNewPageComponent } from './pages/zone-new-page/zone-new-page.component';
import { ZoneDetailPageComponent } from './pages/zone-detail-page/zone-detail-page.component';
import { p } from '../../../permissions';

export default [
    {
        path: '',
        component: ZonesComponent,
        children: [
            {
                path: '',
                component: ZoneListPageComponent,
                data: {
                    title: 'translate_attendance_zones',
                    permissionIds: [p.attendance.zones.read],
                },
            },

            {
                path: 'new',
                component: ZoneNewPageComponent,
                data: {
                    title: 'translate_add_new_zone',
                    permissionIds: [p.attendance.zones.write],
                },
            },

            {
                path: 'edit/:id',
                component: ZoneNewPageComponent,
                data: {
                    title: 'translate_update_zone_details',
                    permissionIds: [p.attendance.zones.write],
                },
            },

            {
                path: ':id',
                component: ZoneDetailPageComponent,
                data: {
                    title: 'translate_zone_details',
                    permissionIds: [p.attendance.zones.read],
                },
            },
        ],
    },
] satisfies Routes;
