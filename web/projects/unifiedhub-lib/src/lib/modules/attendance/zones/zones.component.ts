import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ZonesService } from './zones.service';

@Component({
    selector: 'lib-zones',
    template: '<router-outlet/>',
    standalone: true,
    providers: [ZonesService],
    imports: [RouterOutlet],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ZonesComponent {}
