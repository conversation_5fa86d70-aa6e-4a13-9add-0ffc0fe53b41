import { MultilingualString } from '../../../../common';
import { User } from '../../../users/core';
import { SurveyQuestion } from './survey-question.type';

export type Survey = {
    id: string;
    name: MultilingualString;
    description?: MultilingualString;
    from: Date;
    to: Date;
    state: 'published' | 'unpublished';
    questionCount: number;
    questions: SurveyQuestion[];
    users: User[];
    createdTime: Date;
    modifiedTime?: Date;
    responseRate: number;
};
