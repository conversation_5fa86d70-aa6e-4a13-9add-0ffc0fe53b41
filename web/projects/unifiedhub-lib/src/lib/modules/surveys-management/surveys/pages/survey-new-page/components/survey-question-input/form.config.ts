import { DynamicFormConfig } from '../../../../../../../features';
import { SurveysService } from '../../../../surveys.service';

export const config = (surveysService: SurveysService): DynamicFormConfig[] => {
    return [
        {
            id: 'id',
            type: 'hidden',
        },

        {
            id: 'name',
            type: 'multilingualTextInput',
            label: 'translate_question',
            required: true,
        },

        {
            id: 'description',
            type: 'multilingualTextInput',
            label: 'translate_question_description',
            props: {
                type: 'long',
            },
        },

        {
            id: 'type',
            type: 'select',
            label: 'translate_question_type',
            required: true,
            defaultValue: 'text',
            props: {
                items$: surveysService.questionTypes(),
                bindLabel: 'name',
                bindValue: 'id',
            },
            onValueChange: (value, _, fields) => {
                const options = fields['options'];
                if (options) options.hide = value !== 'select';
            },
        },

        {
            id: 'isRequired',
            type: 'input',
            label: 'translate_required_question',
            props: {
                type: 'checkbox',
            },
        },

        {
            id: 'order',
            type: 'input',
            label: 'translate_question_order',
            props: {
                type: 'number',
            },
        },

        {
            id: 'options',
            type: 'repeat',
            label: 'translate_question_options',
            defaultValue: [],
            props: {
                repeatedConfig: {
                    type: 'group',
                    direction: 'row',
                    config: [
                        {
                            id: 'value',
                            type: 'input',
                            label: 'translate_option_value',
                            required: true,
                        },
                    ],
                },
            },
        },
    ];
};
