import { SurveysService } from '../../surveys.service';
import { DynamicListPageConfig } from '../../../../../features';
import { SurveyResponsePair } from '../../../core';

export const config = (
    surveysService: SurveysService,
    showViewer: (
        surveyId: string,
        responseId: string | undefined,
        reloadFn: () => void,
    ) => void,
): DynamicListPageConfig<
    SurveyResponsePair,
    { keyword?: string; responseStates?: ('completed' | 'incomplete')[] }
> => {
    return {
        title: 'translate_targeted_surveys',
        newButtonConfig: {
            isHidden: true,
        },
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_created_time',
                },

                {
                    label: 'translate_modified_time',
                },

                {
                    label: 'translate_completed',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.survey.name,
                },

                {
                    value: item.survey.createdTime,
                    type: 'datetime',
                },

                {
                    value: item.survey.modifiedTime,
                    type: 'datetime',
                },

                {
                    value: item.response && item.response.isCompleted,
                    type: 'boolean',
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    isHidden: true,
                },
                deleteButtonConfig: {
                    isHidden: true,
                },
            }),

            actionsFn: (item, _, __, reload) => [
                {
                    type: 'success',
                    iconClasses: 'fa fa-eye',
                    onClickFn: () => {
                        showViewer(item.survey.id, item.response?.id, reload);
                    },
                },
            ],

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },

                {
                    id: 'responseStates',
                    type: 'select',
                    label: 'translate_states',
                    items: [
                        {
                            id: 'completed',
                            name: 'translate_completed',
                        },

                        {
                            id: 'incomplete',
                            name: 'translate_incomplete',
                        },
                    ],
                    bindValue: 'id',
                    bindLabel: 'name',
                    isMulti: true,
                },
            ],
            fetcher: surveysService.listTargeted.bind(surveysService),
        },
    };
};
