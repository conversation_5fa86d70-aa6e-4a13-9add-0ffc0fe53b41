import {
    ChangeDetectionStrategy,
    Component,
    effect,
    input,
    output,
    signal,
} from '@angular/core';
import {
    ToastService,
    TranslateMultilingualStringPipe,
} from '../../../../../../core';
import { SurveyQuestion } from '../../../../core';
import { SurveysService } from '../../../surveys.service';
import { FormsModule } from '@angular/forms';
import {
    LoadingButtonComponent, SelectInputComponent,
    WaitUntilListLoadedDirective
} from '../../../../../../ui';
import { TranslateModule } from '@ngx-translate/core';
import { finalize } from 'rxjs';
import { SurveyResponsesService } from '../../../survey-responses.service';

@Component({
    selector: 'lib-survey-response-viewer',
    templateUrl: 'survey-response-viewer.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        TranslateMultilingualStringPipe,
        FormsModule,
        LoadingButtonComponent,
        TranslateModule,
        WaitUntilListLoadedDirective,
        SelectInputComponent,
    ],
})
export class SurveyResponseViewerComponent {
    public readonly surveyId = input.required<string>();
    public readonly responseId = input<string>();
    public readonly mode = input<'default' | 'view'>('default');

    public readonly respond = output<void>();

    protected readonly questionAnswerPairs = signal<
        { question: SurveyQuestion; answer?: string }[] | undefined
    >(undefined);

    protected readonly isSubmitting = signal<boolean>(false);

    public constructor(
        private readonly surveysService: SurveysService,
        private readonly toastService: ToastService,
        surveyResponsesService: SurveyResponsesService,
    ) {
        effect(
            () => {
                if (this.responseId()) {
                    surveyResponsesService
                        .get(this.responseId()!)
                        .subscribe(response => {
                            this.questionAnswerPairs.set(
                                response.answers.map(x => ({
                                    question: x.question,
                                    answer: x.value,
                                })),
                            );
                        });
                } else {
                    this.surveysService
                        .getTargeted(this.surveyId())
                        .subscribe(survey => {
                            this.questionAnswerPairs.set(
                                survey.questions.map(x => ({
                                    question: x,
                                    answer: '',
                                })),
                            );
                        });
                }
            },
            { allowSignalWrites: true },
        );
    }

    protected submit(): void {
        if (this.isSubmitting() || !this.questionAnswerPairs()) return;
        this.isSubmitting.set(true);

        const answers = this.questionAnswerPairs()!.map(x => ({
            questionId: x.question.id,
            value: x.answer,
        }));

        this.surveysService
            .respond(this.surveyId(), answers)
            .pipe(finalize(() => this.isSubmitting.set(false)))
            .subscribe(() => {
                this.respond.emit();
                this.toastService.success('translate_operation_successful');
            });
    }

    protected isOptionSelected(
        answer: string | undefined,
        optionValue: string | undefined,
    ): boolean {
        if (!answer) return false;
        try {
            const selectedValues = JSON.parse(answer) as string[];
            return !!optionValue && selectedValues.includes(optionValue);
        } catch {
            return answer === optionValue;
        }
    }

    protected toggleCheckboxOption(
        pair: { question: SurveyQuestion; answer?: string },
        optionValue: string,
    ): void {
        let selectedValues: string[] = [];

        if (pair.answer) {
            try {
                selectedValues = JSON.parse(pair.answer) as string[];
            } catch {
                selectedValues = [];
            }
        }

        const index = selectedValues.indexOf(optionValue);
        if (index > -1) {
            selectedValues.splice(index, 1);
        } else {
            selectedValues.push(optionValue);
        }

        pair.answer = JSON.stringify(selectedValues);
    }
}
