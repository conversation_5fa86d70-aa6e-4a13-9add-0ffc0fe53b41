import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { UsersService } from '../../../../users/users.service';

@Component({
    selector: 'lib-survey-new-page',
    templateUrl: './survey-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent],
    providers: [UsersService],
})
export class SurveyNewPageComponent {
    protected configFn = configFn(inject(Injector));
}
