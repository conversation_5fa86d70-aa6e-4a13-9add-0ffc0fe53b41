import { SurveysService } from '../../surveys.service';
import { DetailPageConfig, ModalService } from '../../../../../features';
import { Survey, SurveyQuestion, SurveyResponse } from '../../../core';
import { Injector } from '@angular/core';
import { p } from '../../../../../permissions';
import { Filter, roundNumber } from '../../../../../common';
import { NEVER } from 'rxjs';
import { SurveyResponseViewerComponent } from '../components/survey-response-viewer/survey-response-viewer.component';
import { User } from '../../../../users/core';

export const config = (injector: Injector): DetailPageConfig<Survey> => {
    const surveysService = injector.get(SurveysService);

    return {
        loader: surveysService.get.bind(surveysService),
        config: item => ({
            title: 'translate_surveys',
            subtitle: 'translate_survey_details',
            editButtonConfig: {
                permissionId: p.surveys.write,
                isHidden: !item || item.state === 'published',
            },
            sectionConfigs: [
                {
                    title: 'translate_survey_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_created_time',
                        },

                        {
                            label: 'translate_modified_time',
                        },

                        {
                            label: 'translate_from',
                        },

                        {
                            label: 'translate_to',
                        },

                        {
                            label: 'translate_state',
                        },

                        {
                            label: 'translate_response_rate',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.createdTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.modifiedTime,
                            type: 'datetime',
                        },

                        {
                            value: item?.from,
                            type: 'date',
                        },

                        {
                            value: item?.to,
                            type: 'date',
                        },

                        {
                            value: item?.state,
                            type: 'id_to_name',
                            idToName$: surveysService.states(),
                        },

                        {
                            value:
                                item?.state === 'published'
                                    ? `${roundNumber(item!.responseRate * 100, 2)}%`
                                    : '-',
                        },
                    ],
                },

                {
                    title: 'translate_questions',
                    type: 'list',
                    items: item?.questions,
                    columnConfigFn: () => [
                        {
                            label: 'translate_question',
                        },

                        {
                            label: 'translate_question_description',
                        },

                        {
                            label: 'translate_question_type',
                        },

                        {
                            label: 'translate_required',
                        },

                        {
                            label: 'translate_order',
                        },

                        {
                            label: 'translate_options',
                        },
                    ],
                    valueConfigFn: tmp => {
                        const question = tmp as SurveyQuestion;
                        return [
                            {
                                value: question.name,
                            },

                            {
                                value: question.description,
                            },

                            {
                                value: question.type,
                                // type: 'id_to_name',
                                // idToName: [
                                //     {
                                //         id: 'text',
                                //         name: 'translate_text_question',
                                //     },
                                //     {
                                //         id: 'select',
                                //         name: 'translate_select_question',
                                //     },
                                //     {
                                //         id: 'checkbox',
                                //         name: 'translate_checkbox_question',
                                //     },
                                // ],
                            },

                            {
                                value: question.isRequired
                                    ? 'translate_yes'
                                    : 'translate_no',
                            },

                            {
                                value: question.order,
                                type: 'number',
                            },

                            {
                                value:
                                    question.options?.length > 0
                                        ? question.options.join(', ')
                                        : '-',
                            },
                        ];
                    },
                },

                {
                    title: 'translate_responses',
                    type: 'list_full',
                    isEditLinkHidden: true,
                    fetcher: item
                        ? (
                              filter: Filter<{
                                  keyword?: string;
                                  responseStates?: (
                                      | 'completed'
                                      | 'incomplete'
                                  )[];
                              }>,
                          ) => surveysService.listResponses(item!.id, filter)
                        : () => NEVER,
                    columnConfigFn: () => [
                        {
                            label: 'translate_user',
                        },

                        {
                            label: 'translate_created_time',
                        },

                        {
                            label: 'translate_modified_time',
                        },

                        {
                            label: 'translate_completed',
                        },
                    ],
                    valueConfigFn: tmp => {
                        const pair = tmp as {
                            user: User;
                            response?: SurveyResponse;
                        };
                        return [
                            {
                                value: pair.user.name,
                            },

                            {
                                value: pair.response?.createdTime,
                                type: 'datetime',
                            },

                            {
                                value: pair.response?.modifiedTime,
                                type: 'datetime',
                            },

                            {
                                value: pair.response?.isCompleted ?? false,
                                type: 'boolean',
                            },
                        ];
                    },

                    filter: [
                        {
                            id: 'keyword',
                            type: 'text',
                            label: 'translate_search_by_name',
                        },
                        {
                            id: 'responseStates',
                            type: 'select',
                            label: 'translate_states',
                            items: [
                                {
                                    id: 'completed',
                                    name: 'translate_completed',
                                },

                                {
                                    id: 'incomplete',
                                    name: 'translate_incomplete',
                                },
                            ],
                            bindValue: 'id',
                            bindLabel: 'name',
                            isMulti: true,
                        },
                    ],

                    actionsFn: tmp => {
                        const pair = tmp as {
                            user: User;
                            response?: SurveyResponse;
                        };
                        return [
                            {
                                type: 'success',
                                iconClasses: 'fa fa-eye',
                                label: 'translate_view_response',
                                isHiddenFn: () => !pair.response,
                                onClickFn: async () => {
                                    const modalService =
                                        injector.get(ModalService);
                                    await modalService.show(
                                        SurveyResponseViewerComponent,
                                        {
                                            injector,
                                            title: 'translate_view_response',
                                            inputs: {
                                                surveyId: item!.id,
                                                responseId: pair.response!.id,
                                                mode: 'view',
                                            },
                                        },
                                    );
                                },
                            },
                        ];
                    },
                },
            ],
        }),
    };
};
