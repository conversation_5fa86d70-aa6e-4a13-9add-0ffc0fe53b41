import { SurveysService } from '../../surveys.service';
import { Survey } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';
import { Injector } from '@angular/core';
import { UsersService } from '../../../../users/users.service';
import { map } from 'rxjs';
import { SurveyQuestionInputComponent } from './components/survey-question-input/survey-question-input.component';

export const configFn = (injector: Injector): NewPageConfigFn<Survey> => {
    const surveysService = injector.get(SurveysService);

    return mode => ({
        title: 'translate_surveys',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_survey'
                : 'translate_update_survey_details',
        newFormConfig: {
            loader: surveysService.get.bind(surveysService),
            creator: surveysService.create.bind(surveysService),
            editor: surveysService.update.bind(surveysService),
            onSubmitting: item => {
                item.questions.forEach(x => {
                    x.options = x.options?.map(
                        x => (x as unknown as { value: string }).value,
                    );
                });
            },
            onLoading: item => {
                item.questions.forEach(x => {
                    x.options = x.options?.map(
                        x => ({ value: x }) as unknown as string,
                    );
                });
            },
            formConfig: [
                {
                    id: 'name',
                    type: 'multilingualTextInput',
                    label: 'translate_name',
                    required: true,
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'from',
                            type: 'datetime',
                            label: 'translate_from',
                            required: true,
                        },

                        {
                            id: 'to',
                            type: 'datetime',
                            label: 'translate_to',
                            required: true,
                        },
                    ],
                },

                {
                    id: 'users',
                    type: 'select',
                    label: 'translate_target_audience',
                    note: 'translate_leave_empty_for_all_users',
                    props: {
                        loaderFetcher: keyword =>
                            injector
                                .get(UsersService)
                                .simpleList({ attrs: { keyword } })
                                .pipe(map(data => data.items)),
                        isMulti: true,
                        bindLabel: 'name',
                    },
                },

                {
                    id: 'dummy',
                    type: 'component',
                    props: {
                        component: SurveyQuestionInputComponent,
                    },
                },

                {
                    id: 'questions',
                    type: 'repeat',
                    label: 'translate_questions',
                    props: {
                        repeatedConfig: {
                            type: 'component',
                            props: {
                                component: SurveyQuestionInputComponent,
                            },
                        },
                    },
                },
            ],
        },
    });
};
