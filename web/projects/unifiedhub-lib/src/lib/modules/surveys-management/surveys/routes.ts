import { Routes } from '@angular/router';
import { SurveysComponent } from './surveys.component';
import { SurveyListPageComponent } from './pages/survey-list-page/survey-list-page.component';
import { SurveyNewPageComponent } from './pages/survey-new-page/survey-new-page.component';
import { SurveyDetailPageComponent } from './pages/survey-detail-page/survey-detail-page.component';
import { TargetedSurveyListPageComponent } from './pages/targeted-survey-list-page/targeted-survey-list-page.component';

export const routes = [
    {
        path: '',
        component: SurveysComponent,
        children: [
            {
                path: '',
                component: SurveyListPageComponent,
                data: {
                    title: 'translate_surveys',
                },
            },

            {
                path: 'new',
                component: SurveyNewPageComponent,
                data: {
                    title: 'translate_add_new_survey',
                },
            },

            {
                path: 'edit/:id',
                component: SurveyNewPageComponent,
                data: {
                    title: 'translate_update_survey_details',
                },
            },

            {
                path: 'targeted',
                component: TargetedSurveyListPageComponent,
                data: {
                    title: 'translate_targeted_surveys',
                },
            },

            {
                path: ':id',
                component: SurveyDetailPageComponent,
                data: {
                    title: 'translate_survey_details',
                },
            },
        ],
    },
] satisfies Routes;
