import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import {
    Survey,
    SurveyFilterAttrs,
    SurveyResponse,
    SurveyResponsePair,
} from '../core';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import {
    Filter,
    Item,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';
import { User } from '../../users/core';

@Injectable()
export class SurveysService extends CrudService<Survey, SurveyFilterAttrs> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public listTargeted(
        filter: Filter<{
            keyword?: string;
            responseStates?: ('completed' | 'incomplete')[];
        }>,
    ): Observable<PaginatedResult<SurveyResponsePair>> {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<SurveyResponsePair>>(
            `${this.getBaseEndpoint()}/targeted`,
            { params },
        );
    }

    public getTargeted(id: string): Observable<Survey> {
        return this.httpClient.get<Survey>(
            `${this.getBaseEndpoint()}/targeted/${id}`,
        );
    }

    public listResponses(
        id: string,
        filter: Filter<{
            keyword?: string;
            responseStates?: ('completed' | 'incomplete')[];
        }>,
    ): Observable<
        PaginatedResult<{
            user: User;
            response?: SurveyResponse;
        }>
    > {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<SurveyResponse>>(
            `${this.getBaseEndpoint()}/${id}/responses`,
            { params },
        );
    }

    public publish(id: string): Observable<void> {
        return this.httpClient.put<void>(
            `${this.getBaseEndpoint()}/${id}/publish`,
            undefined,
        );
    }

    public unpublish(id: string): Observable<void> {
        return this.httpClient.put<void>(
            `${this.getBaseEndpoint()}/${id}/unpublish`,
            undefined,
        );
    }

    public respond(
        id: string,
        answers: { questionId: string; value?: string }[],
    ): Observable<SurveyResponse> {
        return this.httpClient.post<SurveyResponse>(
            `${this.getBaseEndpoint()}/${id}/respond`,
            {
                answers,
            },
        );
    }

    public states(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(`${this.getBaseEndpoint()}/states`, {
            context: new HttpContext().set(CACHE_INTERCEPTOR, true),
        });
    }

    public questionTypes(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.getBaseEndpoint()}/question-types`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/surveys`;
    }
}
