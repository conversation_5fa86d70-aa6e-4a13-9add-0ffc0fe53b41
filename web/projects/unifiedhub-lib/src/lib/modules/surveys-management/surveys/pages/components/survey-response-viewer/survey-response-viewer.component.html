<div
    *libWaitUntilListLoaded="questionAnswerPairs()"
    class="flex flex-col gap-8"
>
    @for (pair of questionAnswerPairs(); track pair.question.id) {
        <div class="flex flex-col gap-2">
            <!-- Question part -->
            <div>
                <div>
                    {{ pair.question.name | translateMultilingualString }}
                </div>
                @if (pair.question.description) {
                    <div class="mt-1 text-sm text-gray-400">
                        {{
                            pair.question.description
                                | translateMultilingualString
                        }}
                    </div>
                }
            </div>

            <!-- Answer part -->
            <textarea
                [(ngModel)]="pair.answer"
                [disabled]="mode() === 'view'"
            ></textarea>
        </div>
    }

    @if (mode() === 'default') {
        <lib-loading-button
            class="self-center"
            (click)="submit()"
            [isLoading]="isSubmitting()"
            label="{{ 'translate_save' | translate }}"
            icon="fa fa-save"
        />
    }
</div>
