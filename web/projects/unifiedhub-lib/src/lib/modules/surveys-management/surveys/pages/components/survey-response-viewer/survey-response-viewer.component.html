<div
    *libWaitUntilListLoaded="questionAnswerPairs()"
    class="flex flex-col gap-8"
>
    @for (pair of questionAnswerPairs(); track pair.question.id) {
        <div class="flex flex-col gap-2">
            <!-- Question part -->
            <div>
                <div>
                    {{ pair.question.name | translateMultilingualString }}
                </div>
                @if (pair.question.description) {
                    <div class="mt-1 text-sm text-gray-400">
                        {{
                            pair.question.description
                                | translateMultilingualString
                        }}
                    </div>
                }
            </div>

            <!-- Answer part -->
            @switch (pair.question.type) {
                @case ('text') {
                    <textarea
                        [(ngModel)]="pair.answer"
                        [disabled]="mode() === 'view'"
                        [required]="pair.question.isRequired"
                        class="w-full p-2 border border-gray-300 rounded-md"
                    ></textarea>
                }
                @case ('select') {
                    <lib-select-input
                        [(ngModel)]="pair.answer"
                        [disabled]="mode() === 'view'"
                        [required]="pair.question.isRequired"
                        [items]="pair.question.options"
                    />
                }
                @case ('checkbox') {
                    <input type="checkbox" />
                    <div class="flex flex-col gap-2">
                        <input type="checkbox"
                               [(ngModel)]="pair.answer"
                               [disabled]="mode() === 'view'"
                               [required]="pair.question.isRequired">
                    </div>
                }
            }
        </div>
    }

    @if (mode() === 'default') {
        <lib-loading-button
            class="self-center"
            (click)="submit()"
            [isLoading]="isSubmitting()"
            label="{{ 'translate_save' | translate }}"
            icon="fa fa-save"
        />
    }
</div>
