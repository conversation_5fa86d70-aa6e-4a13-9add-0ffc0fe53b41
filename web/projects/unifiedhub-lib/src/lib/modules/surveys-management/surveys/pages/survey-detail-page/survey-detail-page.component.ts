import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
    signal,
    viewChild,
} from '@angular/core';
import { config } from './detail.config';
import {
    DynamicDetailPageComponent,
    SmartAlertService,
} from '../../../../../features';
import { EmployeesService } from '../../../../employment/employees/employees.service';
import { DepartmentsService } from '../../../../departments/departments.service';
import { JobLevelsService } from '../../../../employment/job-levels/job-levels.service';
import { JobTitlesService } from '../../../../employment/job-titles/job-titles.service';
import { TranslateModule } from '@ngx-translate/core';
import { Survey } from '../../../core';
import { NgClass } from '@angular/common';
import { SurveysService } from '../../surveys.service';
import { ToastService } from '../../../../../core';
import { finalize } from 'rxjs';

@Component({
    selector: 'lib-survey-detail-page',
    templateUrl: './survey-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        EmployeesService,
        DepartmentsService,
        JobLevelsService,
        JobTitlesService,
    ],
    imports: [DynamicDetailPageComponent, TranslateModule, NgClass],
})
export class SurveyDetailPageComponent {
    protected readonly item = signal<Survey | undefined>(undefined);
    protected readonly isProcessing = signal<boolean>(false);

    protected readonly config = config(inject(Injector));

    private readonly detailPage =
        viewChild<DynamicDetailPageComponent<Survey>>('detailPage');

    public constructor(
        private readonly smartAlertService: SmartAlertService,
        private readonly surveysService: SurveysService,
        private readonly toastService: ToastService,
    ) {}

    protected togglePublish(): void {
        if (!this.item() || this.isProcessing()) return;

        this.smartAlertService
            .confirm(
                `translate_are_you_sure_you_want_to_${this.item()?.state === 'published' ? 'unpublish' : 'publish'}_this_survey`,
            )
            .subscribe(isConfirmed => {
                if (!isConfirmed) return;

                this.isProcessing.set(true);

                const observable =
                    this.item()!.state === 'published'
                        ? this.surveysService.unpublish(this.item()!.id)
                        : this.surveysService.publish(this.item()!.id);

                observable
                    .pipe(finalize(() => this.isProcessing.set(false)))
                    .subscribe(() => {
                        this.toastService.success(
                            'translate_operation_successful',
                        );

                        this.detailPage()!.reload();
                    });
            });
    }
}
