import {
    ChangeDetectionStrategy,
    Component,
    forwardRef,
    inject,
    signal,
} from '@angular/core';
import {
    ControlValueAccessor,
    FormsModule,
    NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { SurveyQuestion } from '../../../../../core';
import { DynamicFormComponent } from '../../../../../../../features';
import { config } from './form.config';
import { SurveysService } from '../../../../surveys.service';

@Component({
    selector: 'lib-survey-question-input',
    templateUrl: 'survey-question-input.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SurveyQuestionInputComponent),
            multi: true,
        },
    ],
    imports: [FormsModule, DynamicFormComponent],
})
export class SurveyQuestionInputComponent implements ControlValueAccessor {
    protected readonly config = config(inject(SurveysService));
    protected readonly value = signal<SurveyQuestion>({
        id: '',
        name: { ar: '', en: '' },
        description: { ar: '', en: '' },
        type: 'text',
        order: 0,
        isRequired: false,
        options: [],
    });
    private onChange?: (value: SurveyQuestion) => void;

    public writeValue(value: SurveyQuestion | undefined): void {
        this.value.set(
            value ?? {
                id: '',
                name: { ar: '', en: '' },
                description: { ar: '', en: '' },
                type: 'text',
                order: 0,
                isRequired: false,
                options: [],
            },
        );
    }

    public registerOnChange(fn: (value: SurveyQuestion) => void): void {
        this.onChange = fn;
    }

    public registerOnTouched(): void {
        // ignored
    }

    public setDisabledState?(): void {
        // ignored
    }

    protected reportChanges(): void {
        // this.onChange?.(this.value());
    }
}
