import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { EducationalQualificationsService } from '../../educational-qualifications.service';

@Component({
    selector: 'lib-educational-qualification-new-page',
    templateUrl: './educational-qualification-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        RouterLink,
        DynamicNewPageComponent,
    ],
})
export class EducationalQualificationNewPageComponent {
    protected configFn = configFn(inject(EducationalQualificationsService));
}
