import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { EducationalQualificationsService } from '../../educational-qualifications.service';

@Component({
    selector: 'lib-educational-qualification-list-page',
    templateUrl: './educational-qualification-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class EducationalQualificationListPageComponent {
    protected config = config(inject(EducationalQualificationsService));
}
