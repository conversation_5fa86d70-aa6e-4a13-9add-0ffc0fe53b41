import { CountriesService } from '../../countries.service';
import { p } from '../../../../../permissions';
import { Country } from '../../../core';
import { DetailPageConfig } from '../../../../../features';

export const config = (
    countriesServices: CountriesService,
): DetailPageConfig<Country> => {
    return {
        loader: countriesServices.get.bind(countriesServices),
        config: item => ({
            title: 'translate_countries',
            subtitle: 'translate_country_details',
            editButtonConfig: {
                permissionId: p.foundation.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_country_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },

                        {
                            label: 'translate_order',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },

                        {
                            value: item?.order,
                        },
                    ],
                },
            ],
        }),
    };
};
