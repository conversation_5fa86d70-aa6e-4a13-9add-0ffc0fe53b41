import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { CountriesService } from '../../countries.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-country-detail-page',
    templateUrl: './country-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class CountryDetailComponent {
    protected readonly config = config(inject(CountriesService));
}
