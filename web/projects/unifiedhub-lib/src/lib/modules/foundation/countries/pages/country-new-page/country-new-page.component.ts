import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { CountriesService } from '../../countries.service';

@Component({
    selector: 'lib-country-new-page',
    templateUrl: './country-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        TranslateModule,
        DynamicNewPageComponent,
    ],
})
export class CountryNewPageComponent {
    protected configFn = configFn(inject(CountriesService));
}
