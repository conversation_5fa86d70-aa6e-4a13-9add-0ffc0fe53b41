import { CountriesService } from '../../countries.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { Country } from '../../../core';

export const config = (
    countriesServices: CountriesService,
): DynamicListPageConfig<Country, { keyword?: string }> => {
    return {
        title: 'translate_countries',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'foundation', 'countries', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.foundation.write,
                },
                deleteButtonConfig: {
                    permissionId: p.foundation.write,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: countriesServices.list.bind(countriesServices),
            deleter: countriesServices.delete.bind(countriesServices),
        },

        newButtonConfig: {
            permissionId: p.foundation.write,
        },
    };
};
