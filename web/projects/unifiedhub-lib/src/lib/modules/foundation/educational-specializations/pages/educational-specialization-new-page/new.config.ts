import { EducationalSpecializationsService } from '../../educational-specializations.service';
import { EducationalSpecialization } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';
import { EducationalMajorsService } from '../../../educational-majors/educational-majors.service';

export const configFn = (
    educationalSpecializationsService: EducationalSpecializationsService,
    educationalMajorsService: EducationalMajorsService,
): NewPageConfigFn<EducationalSpecialization> => {
    return mode => ({
        title: 'translate_educational_specializations',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_educational_specialization'
                : 'translate_update_educational_specialization_details',
        newFormConfig: {
            loader: educationalSpecializationsService.get.bind(
                educationalSpecializationsService,
            ),
            creator: educationalSpecializationsService.create.bind(
                educationalSpecializationsService,
            ),
            editor: educationalSpecializationsService.update.bind(
                educationalSpecializationsService,
            ),
            formConfig: [
                {
                    id: 'name',
                    type: 'multilingualTextInput',
                    label: 'translate_name',
                    required: true,
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'major',
                            type: 'select',
                            label: 'translate_major',
                            required: true,
                            props: {
                                items$: educationalMajorsService.listAll(),
                                bindLabel: 'name',
                                compareWith: (a, b) => a.id === b.id,
                            },
                        },
                        {
                            id: 'order',
                            label: 'translate_order',
                            type: 'input',
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
