import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { EducationalSpecializationsService } from '../../educational-specializations.service';

@Component({
    selector: 'lib-educational-specialization-list-page',
    templateUrl: './educational-specialization-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class EducationalSpecializationListPageComponent {
    protected config = config(inject(EducationalSpecializationsService));
}
