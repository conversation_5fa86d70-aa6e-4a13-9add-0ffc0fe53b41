import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { EducationalSpecializationsService } from '../../educational-specializations.service';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-educational-specialization-detail-page',
    templateUrl: 'educational-specialization-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule, DynamicDetailPageComponent],
})
export class EducationalSpecializationDetailPageComponent {
    protected readonly config = config(
        inject(EducationalSpecializationsService),
    );
}
