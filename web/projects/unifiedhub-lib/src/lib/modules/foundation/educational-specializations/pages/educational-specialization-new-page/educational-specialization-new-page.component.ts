import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { configFn } from './new.config';
import { EducationalSpecializationsService } from '../../educational-specializations.service';
import { DynamicNewPageComponent } from '../../../../../features';
import { EducationalMajorsService } from '../../../educational-majors/educational-majors.service';

@Component({
    selector: 'lib-educational-specialization-new-page',
    templateUrl: './educational-specialization-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [EducationalMajorsService],
    imports: [TranslateModule, DynamicNewPageComponent],
})
export class EducationalSpecializationNewPageComponent {
    protected configFn = configFn(
        inject(EducationalSpecializationsService),
        inject(EducationalMajorsService),
    );
}
