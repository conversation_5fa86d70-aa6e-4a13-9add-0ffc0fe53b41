import { EducationalMajorsService } from '../../educational-majors.service';
import { EducationalMajor } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    educationalMajorsService: EducationalMajorsService,
): NewPageConfigFn<EducationalMajor> => {
    return mode => ({
        title: 'translate_educational_majors',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_educational_major'
                : 'translate_update_educational_major_details',
        newFormConfig: {
            loader: educationalMajorsService.get.bind(educationalMajorsService),
            creator: educationalMajorsService.create.bind(
                educationalMajorsService,
            ),
            editor: educationalMajorsService.update.bind(
                educationalMajorsService,
            ),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                            size: 2,
                        },
                        {
                            id: 'order',
                            label: 'translate_order',
                            type: 'input',
                            size: 1,
                            props: {
                                type: 'number',
                            },
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
