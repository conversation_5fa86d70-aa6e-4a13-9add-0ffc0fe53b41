import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { EducationalMajorsService } from '../../educational-majors.service';
import { FormsModule } from '@angular/forms';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';

@Component({
    selector: 'lib-educational-major-detail-page',
    templateUrl: './educational-major-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent, FormsModule],
})
export class EducationalMajorDetailPageComponent {
    protected readonly config = config(inject(EducationalMajorsService));
}
