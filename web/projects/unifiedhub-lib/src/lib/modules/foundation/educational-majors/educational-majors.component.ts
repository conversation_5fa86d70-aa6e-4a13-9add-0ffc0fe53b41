import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { EducationalMajorsService } from './educational-majors.service';

@Component({
    selector: 'lib-educational-majors',
    template: '<router-outlet/>',
    standalone: true,
    providers: [EducationalMajorsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EducationalMajorsComponent {}
