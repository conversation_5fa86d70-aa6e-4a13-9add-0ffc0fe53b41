import { Routes } from '@angular/router';
import { loadTranslationsResolver } from '@ng-omar/translation';
import { p } from '../../permissions';

export const routes = [
    {
        path: 'institutions',
        loadChildren: () =>
            import('./course-institutions/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: 'majors',
        loadChildren: () =>
            import('./course-majors/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: 'results',
        loadChildren: () =>
            import('./course-results/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: 'status-types',
        loadChildren: () =>
            import('./course-status-types/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: 'training-types',
        loadChildren: () =>
            import('./course-training-types/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: 'types',
        loadChildren: () => import('./course-types/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.manageLists],
        },
    },

    {
        path: '',
        loadChildren: () => import('./courses/routes').then(x => x.routes),
        resolve: { translations: loadTranslationsResolver },
        data: {
            permissionIds: [p.courses.read],
        },
    },
] satisfies Routes;
