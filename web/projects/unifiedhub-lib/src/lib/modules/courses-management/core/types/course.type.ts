import { MultilingualString } from '../../../../common';
import { CourseType } from './course-type.type';
import { CourseMajor } from './course-major.type';
import { City, Country } from '../../../foundation/core';
import { CourseInstitution } from './course-institution.type';
import { CourseStatusType } from './course-status-type.type';
import { CourseTrainingType } from './course-training-type.type';

export type Course = {
    id: string;
    name: MultilingualString;
    description?: MultilingualString;
    startDate: Date;
    endDate: Date;
    type?: CourseType;
    major?: CourseMajor;
    employeeCount: number;
    isOfficial: boolean;
    country?: Country;
    city?: City;
    institution?: CourseInstitution;
    address?: string;
    isAcquiredBeforeHiring: boolean;
    impact?: string;
    statusType?: CourseStatusType;
    trainingType?: CourseTrainingType;
    notes?: string;
};
