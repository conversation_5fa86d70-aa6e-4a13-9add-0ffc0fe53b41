import { CourseTrainingTypesService } from '../../course-training-types.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { CourseTrainingType } from '../../../core';

export const config = (
    courseTrainingTypesService: CourseTrainingTypesService,
): DynamicListPageConfig<CourseTrainingType, { keyword?: string }> => {
    return {
        title: 'translate_course_training_types',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'courses', 'training-types', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: courseTrainingTypesService.list.bind(
                courseTrainingTypesService,
            ),
            deleter: courseTrainingTypesService.delete.bind(
                courseTrainingTypesService,
            ),
        },

        newButtonConfig: {
            permissionId: p.courses.manageLists,
        },
    };
};
