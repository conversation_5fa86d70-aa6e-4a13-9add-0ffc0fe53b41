import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CourseTrainingTypesService } from './course-training-types.service';

@Component({
    selector: 'lib-course-training-types',
    template: '<router-outlet/>',
    standalone: true,
    providers: [CourseTrainingTypesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CourseTrainingTypesComponent {}
