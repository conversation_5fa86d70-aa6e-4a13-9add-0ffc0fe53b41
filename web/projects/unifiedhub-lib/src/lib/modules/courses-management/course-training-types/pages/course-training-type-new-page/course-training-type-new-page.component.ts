import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { CourseTrainingTypesService } from '../../course-training-types.service';

@Component({
    selector: 'lib-course-training-type-new-page',
    templateUrl: './course-training-type-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent],
})
export class CourseTrainingTypeNewPageComponent {
    protected configFn = configFn(inject(CourseTrainingTypesService));
}
