import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';
import { CourseTrainingTypesService } from '../../course-training-types.service';

@Component({
    selector: 'lib-course-training-type-detail-page',
    templateUrl: './course-training-type-detail.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicDetailPageComponent],
})
export class CourseTrainingTypeDetailComponent {
    protected readonly config = config(inject(CourseTrainingTypesService));
}
