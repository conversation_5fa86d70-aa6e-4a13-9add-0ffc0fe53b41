import { CourseTrainingTypesService } from '../../course-training-types.service';
import { CourseTrainingType } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    courseTrainingTypesService: CourseTrainingTypesService,
): NewPageConfigFn<CourseTrainingType> => {
    return mode => ({
        title: 'translate_course_training_types',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_course_training_type'
                : 'translate_update_course_training_type_details',
        newFormConfig: {
            loader: courseTrainingTypesService.get.bind(
                courseTrainingTypesService,
            ),
            creator: courseTrainingTypesService.create.bind(
                courseTrainingTypesService,
            ),
            editor: courseTrainingTypesService.update.bind(
                courseTrainingTypesService,
            ),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
