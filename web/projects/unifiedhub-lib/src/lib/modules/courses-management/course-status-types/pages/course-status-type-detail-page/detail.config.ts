import { CourseStatusTypesService } from '../../course-status-types.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { CourseStatusType } from '../../../core';

export const config = (
    courseStatusTypesService: CourseStatusTypesService,
): DetailPageConfig<CourseStatusType> => {
    return {
        loader: courseStatusTypesService.get.bind(courseStatusTypesService),
        config: item => ({
            title: 'translate_course_status_types',
            subtitle: 'translate_course_status_type_details',
            editButtonConfig: {
                permissionId: p.courses.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_course_status_type_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },
                    ],
                },
            ],
        }),
    };
};
