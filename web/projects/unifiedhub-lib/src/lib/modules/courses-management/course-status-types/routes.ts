import { Routes } from '@angular/router';
import { CourseStatusTypesComponent } from './course-status-types.component';
import { CourseStatusTypeListPageComponent } from './pages/course-status-type-list-page/course-status-type-list-page.component';
import { CourseStatusTypeNewPageComponent } from './pages/course-status-type-new-page/course-status-type-new-page.component';
import { CourseStatusTypeDetailComponent } from './pages/course-status-type-detail-page/course-status-type-detail.component';

export const routes = [
    {
        path: '',
        component: CourseStatusTypesComponent,
        children: [
            {
                path: '',
                component: CourseStatusTypeListPageComponent,
                data: {
                    title: 'translate_course_status_types',
                },
            },

            {
                path: 'new',
                component: CourseStatusTypeNewPageComponent,
                data: {
                    title: 'translate_add_new_course_status_type',
                },
            },

            {
                path: 'edit/:id',
                component: CourseStatusTypeNewPageComponent,
                data: {
                    title: 'translate_update_course_status_type_details',
                },
            },

            {
                path: ':id',
                component: CourseStatusTypeDetailComponent,
                data: {
                    title: 'translate_course_status_type_details',
                },
            },
        ],
    },
] satisfies Routes;
