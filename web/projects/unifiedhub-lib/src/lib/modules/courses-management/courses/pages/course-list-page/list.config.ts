import { CoursesService } from '../../courses.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { Course } from '../../../core';

export const config = (
    coursesService: CoursesService,
): DynamicListPageConfig<Course, { keyword?: string }> => {
    return {
        title: 'translate_courses',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },

                {
                    label: 'translate_start_date',
                },

                {
                    label: 'translate_end_date',
                },

                {
                    label: 'translate_employee_count',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'courses', item.id],
                    },
                },

                {
                    value: item.startDate,
                    type: 'date',
                },

                {
                    value: item.endDate,
                    type: 'date',
                },

                {
                    value: item.employeeCount,
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.courses.write,
                },
                deleteButtonConfig: {
                    permissionId: p.courses.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: coursesService.list.bind(coursesService),
            deleter: coursesService.delete.bind(coursesService),
        },

        newButtonConfig: {
            permissionId: p.courses.write,
        },
    };
};
