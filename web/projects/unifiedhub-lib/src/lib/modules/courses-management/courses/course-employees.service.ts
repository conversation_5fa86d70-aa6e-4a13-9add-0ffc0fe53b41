import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import { CourseEmployee, CourseEmployeeFilterAttrs } from '../core';
import { Observable } from 'rxjs';
import { Employee } from '../../employment/core';

@Injectable()
export class CourseEmployeesService extends CrudService<
    CourseEmployee,
    CourseEmployeeFilterAttrs
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public getEmployeeDetails(id: string): Observable<Employee> {
        return this.httpClient.get<Employee>(
            `${this.getBaseEndpoint()}/details/${id}`,
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/courses/employees`;
    }
}
