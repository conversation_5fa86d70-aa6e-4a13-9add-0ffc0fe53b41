import {
    Course,
    CourseEmployee,
    CourseEmployeeFilterAttrs,
} from '../../../../core';
import { SectionConfig } from '../../../../../../features';
import { p } from '../../../../../../permissions';
import { Injector } from '@angular/core';
import { map } from 'rxjs';
import { JobTitlesService } from '../../../../../employment/job-titles/job-titles.service';
import { CourseResultsService } from '../../../../course-results/course-results.service';
import { EmployeesService } from '../../../../../employment/employees/employees.service';
import { DepartmentsService } from '../../../../../departments/departments.service';
import { JobLevelsService } from '../../../../../employment/job-levels/job-levels.service';
import { CourseEmployeesService } from '../../../course-employees.service';
import { Employee, formatEmployeeName } from '../../../../../employment/core';

export const employeeList = (
    course: Course | null,
    injector: Injector,
    courseEmployeesService: CourseEmployeesService,
): SectionConfig<Course> => {
    return {
        type: 'list_with_create',
        config: {
            list: {
                title: 'translate_employees_in_course',
                type: 'full',
                columnConfigFn: () => [
                    { label: 'translate_employee' },
                    { label: 'translate_department' },
                    { label: 'translate_job_title' },
                    { label: 'translate_job_level' },
                    { label: 'translate_course_result' },
                    { label: 'translate_notes' },
                ],
                valueConfigFn: tmp => {
                    const item = tmp as CourseEmployee;

                    return [
                        {
                            value: formatEmployeeName(item.employee),
                            linkConfig: {
                                value: ['', 'employees', item.employee.id],
                                permissionId: p.employees.read,
                            },
                        },

                        {
                            value: item.department?.name,
                        },

                        {
                            value: item.jobTitle?.name,
                        },

                        {
                            value: item.jobLevel?.name,
                        },

                        {
                            value: item.result?.name,
                        },

                        {
                            value: item.notes,
                        },
                    ];
                },

                rowConfigFn: () => {
                    return {
                        editButtonConfig: {
                            permissionId: p.courses.employees.write,
                        },
                        deleteButtonConfig: {
                            permissionId: p.courses.employees.delete,
                        },
                    };
                },

                filter: [
                    {
                        id: 'keyword',
                        type: 'text',
                        label: 'translate_search_by_name',
                    },

                    {
                        id: 'employeeIds',
                        type: 'select',
                        loaderFetcher: keyword =>
                            injector
                                .get(EmployeesService)
                                .simpleList({
                                    attrs: { keyword },
                                    pageSize: 20,
                                })
                                .pipe(map(x => x.items)),
                        bindLabel: 'name',
                        bindValue: 'id',
                        label: 'translate_employee',
                        isMulti: true,
                    },
                ],
                fetcher: courseEmployeesService.list.bind(
                    courseEmployeesService,
                ),
                deleter: courseEmployeesService.delete.bind(
                    courseEmployeesService,
                ),
                defaultFilter: {
                    courseIds: course ? [course.id] : [],
                    filterCountExclusions: ['course_ids'],
                } satisfies CourseEmployeeFilterAttrs,
                newButtonConfig: {
                    permissionId: p.courses.employees.write,
                },
            },

            form: {
                newTitle: 'translate_add_employee_to_course',
                updateTitle: 'translate_update_employee_course',
                configFn: mode => [
                    {
                        id: 'course',
                        type: 'hidden',
                        defaultValue: course,
                        required: true,
                    },

                    {
                        type: 'group',
                        config: [
                            {
                                id: 'employee',
                                type: 'select',
                                label: 'translate_employee',
                                required: true,
                                props: {
                                    bindLabel: 'name',
                                    compareWith: (a, b) => a?.id === b?.id,
                                    loaderFetcher: keyword =>
                                        injector
                                            .get(EmployeesService)
                                            .simpleList({
                                                attrs: {
                                                    keyword,
                                                },
                                                pageSize: 20,
                                            })
                                            .pipe(map(x => x.items)),
                                },
                                onValueChange: (value, group) => {
                                    if (!value || mode === 'edit') return;

                                    const fields = [
                                        'department',
                                        'jobLevel',
                                        'jobTitle',
                                    ];

                                    courseEmployeesService
                                        .getEmployeeDetails(
                                            (value as Employee).id,
                                        )
                                        .subscribe(employee => {
                                            fields.forEach(x => {
                                                group.controls[x].setValue(
                                                    (
                                                        employee as Record<
                                                            string,
                                                            unknown
                                                        >
                                                    )[x],
                                                );
                                            });
                                        });
                                },
                            },

                            {
                                id: 'department',
                                label: 'translate_department',
                                type: 'select',
                                props: {
                                    bindLabel: 'name',
                                    compareWith: (a, b) => a?.id === b?.id,
                                    items$: injector
                                        .get(DepartmentsService)
                                        .listAll(),
                                },
                            },
                        ],
                    },

                    {
                        type: 'group',
                        config: [
                            {
                                id: 'jobLevel',
                                type: 'select',
                                label: 'translate_job_level',
                                props: {
                                    bindLabel: 'name',
                                    compareWith: (a, b) => a?.id === b?.id,
                                    items$: injector
                                        .get(JobLevelsService)
                                        .listAll(),
                                },
                            },

                            {
                                id: 'jobTitle',
                                type: 'select',
                                label: 'translate_job_title',
                                props: {
                                    bindLabel: 'name',
                                    compareWith: (a, b) => a?.id === b?.id,
                                    items$: injector
                                        .get(JobTitlesService)
                                        .listAll(),
                                },
                            },
                        ],
                    },

                    {
                        type: 'group',
                        config: [
                            {
                                id: 'result',
                                type: 'select',
                                label: 'translate_course_result',
                                props: {
                                    bindLabel: 'name',
                                    compareWith: (a, b) => a?.id === b?.id,
                                    items$: injector
                                        .get(CourseResultsService)
                                        .listAll(),
                                },
                            },

                            {
                                id: 'notes',
                                type: 'textarea',
                                label: 'translate_notes',
                            },
                        ],
                    },
                ],
                fetcher: id => courseEmployeesService.get(id),
                creator: item =>
                    courseEmployeesService.create(item as CourseEmployee),
                updater: (id, item) =>
                    courseEmployeesService.update(id, item as CourseEmployee),
            },

            detail: item => {
                return {
                    title: 'translate_course_details',
                    loader: () => courseEmployeesService.get(item.id),
                    sectionConfigs: item => {
                        const courseEmployee = item as CourseEmployee;

                        return [
                            {
                                type: 'detail',
                                noContainer: true,
                                fieldConfigFn: () => [
                                    { label: 'translate_employee' },
                                    { label: 'translate_department' },
                                    { label: 'translate_job_level' },
                                    { label: 'translate_job_title' },
                                    { label: 'translate_course_result' },
                                    { label: 'translate_notes' },
                                ],

                                valueConfigFn: () => [
                                    {
                                        value: formatEmployeeName(
                                            courseEmployee.employee,
                                        ),
                                        linkConfig: {
                                            value: [
                                                '',
                                                'employees',
                                                courseEmployee.employee.id,
                                            ],
                                            permissionId: p.employees.read,
                                        },
                                    },

                                    {
                                        value: courseEmployee.department?.name,
                                    },

                                    {
                                        value: courseEmployee.jobLevel?.name,
                                    },

                                    {
                                        value: courseEmployee.jobTitle?.name,
                                    },

                                    {
                                        value: courseEmployee.result?.name,
                                    },

                                    {
                                        value: courseEmployee.notes,
                                    },
                                ],
                            },
                        ];
                    },
                };
            },
        },
    };
};
