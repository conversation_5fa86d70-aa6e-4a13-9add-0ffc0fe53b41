import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CourseTypesService } from './course-types.service';

@Component({
    selector: 'lib-course-types',
    template: '<router-outlet/>',
    standalone: true,
    providers: [CourseTypesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CourseTypesComponent {}
