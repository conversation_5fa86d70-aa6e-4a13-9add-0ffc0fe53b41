import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CourseMajor } from '../core';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';

@Injectable()
export class CourseMajorsService extends CrudService<
    CourseMajor,
    { keyword?: string }
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/courses/majors`;
    }
}
