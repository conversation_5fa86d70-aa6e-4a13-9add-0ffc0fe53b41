import { CourseMajorsService } from '../../course-majors.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { CourseMajor } from '../../../core';

export const config = (
    courseMajorsService: CourseMajorsService,
): DetailPageConfig<CourseMajor> => {
    return {
        loader: courseMajorsService.get.bind(courseMajorsService),
        config: item => ({
            title: 'translate_course_majors',
            subtitle: 'translate_course_major_details',
            editButtonConfig: {
                permissionId: p.courses.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_course_major_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },
                    ],
                },
            ],
        }),
    };
};
