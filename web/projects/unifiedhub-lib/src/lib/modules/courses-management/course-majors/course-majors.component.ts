import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CourseMajorsService } from './course-majors.service';

@Component({
    selector: 'lib-course-majors',
    template: '<router-outlet/>',
    standalone: true,
    providers: [CourseMajorsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CourseMajorsComponent {}
