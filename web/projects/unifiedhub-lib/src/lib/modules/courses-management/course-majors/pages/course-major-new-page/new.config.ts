import { CourseMajorsService } from '../../course-majors.service';
import { CourseMajor } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    courseMajorsService: CourseMajorsService,
): NewPageConfigFn<CourseMajor> => {
    return mode => ({
        title: 'translate_course_majors',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_course_major'
                : 'translate_update_course_major_details',
        newFormConfig: {
            loader: courseMajorsService.get.bind(courseMajorsService),
            creator: courseMajorsService.create.bind(courseMajorsService),
            editor: courseMajorsService.update.bind(courseMajorsService),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
