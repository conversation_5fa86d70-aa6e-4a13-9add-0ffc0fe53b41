import { CourseInstitutionsService } from '../../course-institutions.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { CourseInstitution } from '../../../core';

export const config = (
    courseInstitutionsService: CourseInstitutionsService,
): DetailPageConfig<CourseInstitution> => {
    return {
        loader: courseInstitutionsService.get.bind(courseInstitutionsService),
        config: item => ({
            title: 'translate_course_institutions',
            subtitle: 'translate_course_institution_details',
            editButtonConfig: {
                permissionId: p.courses.manageLists,
            },
            sectionConfigs: [
                {
                    title: 'translate_course_institution_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },
                    ],
                },
            ],
        }),
    };
};
