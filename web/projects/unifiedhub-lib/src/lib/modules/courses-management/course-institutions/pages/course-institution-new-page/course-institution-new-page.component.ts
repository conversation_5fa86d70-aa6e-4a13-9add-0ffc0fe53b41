import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { CourseInstitutionsService } from '../../course-institutions.service';

@Component({
    selector: 'lib-course-institution-new-page',
    templateUrl: './course-institution-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent],
})
export class CourseInstitutionNewPageComponent {
    protected configFn = configFn(inject(CourseInstitutionsService));
}
