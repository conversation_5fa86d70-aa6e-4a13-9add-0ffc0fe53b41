import { CourseInstitutionsService } from '../../course-institutions.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { CourseInstitution } from '../../../core';

export const config = (
    courseInstitutionsService: CourseInstitutionsService,
): DynamicListPageConfig<CourseInstitution, { keyword?: string }> => {
    return {
        title: 'translate_course_institutions',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'courses', 'institutions', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
                deleteButtonConfig: {
                    permissionId: p.courses.manageLists,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: courseInstitutionsService.list.bind(
                courseInstitutionsService,
            ),
            deleter: courseInstitutionsService.delete.bind(
                courseInstitutionsService,
            ),
        },

        newButtonConfig: {
            permissionId: p.courses.manageLists,
        },
    };
};
