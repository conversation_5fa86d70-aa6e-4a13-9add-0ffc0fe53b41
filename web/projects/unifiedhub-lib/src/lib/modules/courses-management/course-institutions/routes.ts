import { Routes } from '@angular/router';
import { CourseInstitutionsComponent } from './course-institutions.component';
import { CourseInstitutionListPageComponent } from './pages/course-institution-list-page/course-institution-list-page.component';
import { CourseInstitutionNewPageComponent } from './pages/course-institution-new-page/course-institution-new-page.component';
import { CourseInstitutionDetailComponent } from './pages/course-institution-detail-page/course-institution-detail.component';

export const routes = [
    {
        path: '',
        component: CourseInstitutionsComponent,
        children: [
            {
                path: '',
                component: CourseInstitutionListPageComponent,
                data: {
                    title: 'translate_course_institutions',
                },
            },

            {
                path: 'new',
                component: CourseInstitutionNewPageComponent,
                data: {
                    title: 'translate_add_new_course_institution',
                },
            },

            {
                path: 'edit/:id',
                component: CourseInstitutionNewPageComponent,
                data: {
                    title: 'translate_update_course_institution_details',
                },
            },

            {
                path: ':id',
                component: CourseInstitutionDetailComponent,
                data: {
                    title: 'translate_course_institution_details',
                },
            },
        ],
    },
] satisfies Routes;
