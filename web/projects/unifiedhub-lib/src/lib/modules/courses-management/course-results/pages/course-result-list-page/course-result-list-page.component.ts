import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { config } from './list.config';
import { DynamicListPageComponent } from '../../../../../features';
import { CourseResultsService } from '../../course-results.service';

@Component({
    selector: 'lib-course-result-list-page',
    templateUrl: './course-result-list-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListPageComponent],
})
export class CourseResultListPageComponent {
    protected config = config(inject(CourseResultsService));
}
