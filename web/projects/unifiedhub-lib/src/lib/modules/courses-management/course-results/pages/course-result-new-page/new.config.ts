import { CourseResultsService } from '../../course-results.service';
import { CourseResult } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    courseResultsService: CourseResultsService,
): NewPageConfigFn<CourseResult> => {
    return mode => ({
        title: 'translate_course_results',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_course_result'
                : 'translate_update_course_result_details',
        newFormConfig: {
            loader: courseResultsService.get.bind(courseResultsService),
            creator: courseResultsService.create.bind(courseResultsService),
            editor: courseResultsService.update.bind(courseResultsService),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
