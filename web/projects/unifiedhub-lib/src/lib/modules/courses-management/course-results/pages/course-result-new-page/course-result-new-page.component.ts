import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { CourseResultsService } from '../../course-results.service';

@Component({
    selector: 'lib-course-result-new-page',
    templateUrl: './course-result-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewPageComponent],
})
export class CourseResultNewPageComponent {
    protected configFn = configFn(inject(CourseResultsService));
}
