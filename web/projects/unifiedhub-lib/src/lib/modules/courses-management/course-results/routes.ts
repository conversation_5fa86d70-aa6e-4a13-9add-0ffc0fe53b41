import { Routes } from '@angular/router';
import { CourseResultsComponent } from './course-results.component';
import { CourseResultListPageComponent } from './pages/course-result-list-page/course-result-list-page.component';
import { CourseResultNewPageComponent } from './pages/course-result-new-page/course-result-new-page.component';
import { CourseResultDetailComponent } from './pages/course-result-detail-page/course-result-detail.component';

export const routes = [
    {
        path: '',
        component: CourseResultsComponent,
        children: [
            {
                path: '',
                component: CourseResultListPageComponent,
                data: {
                    title: 'translate_course_results',
                },
            },

            {
                path: 'new',
                component: CourseResultNewPageComponent,
                data: {
                    title: 'translate_add_new_course_result',
                },
            },

            {
                path: 'edit/:id',
                component: CourseResultNewPageComponent,
                data: {
                    title: 'translate_update_course_result_details',
                },
            },

            {
                path: ':id',
                component: CourseResultDetailComponent,
                data: {
                    title: 'translate_course_result_details',
                },
            },
        ],
    },
] satisfies Routes;
