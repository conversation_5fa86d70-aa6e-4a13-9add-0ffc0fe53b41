import { DynamicFormConfig } from '../../../../../features';
import { Injector } from '@angular/core';
import { MiscService } from '../../misc.service';
import { map } from 'rxjs';
import { EmployeesService } from '../../../../employment/employees/employees.service';
import { MessagingGroupsService } from '../../../messaging-groups';
import { Message } from '../../../core';

export const config = (
    message: Message,
    injector: Injector,
): DynamicFormConfig[] => {
    return [
        {
            id: 'subject',
            type: 'input',
            label: 'translate_subject',
        },

        {
            id: 'recipientGroups',
            type: 'select',
            label: 'translate_recipient_groups',
            props: {
                bindLabel: 'name',
                isMulti: true,
                loaderFetcher: keyword =>
                    injector
                        .get(MessagingGroupsService)
                        .list({ attrs: { keyword } })
                        .pipe(map(x => x.items)),
            },
        },

        {
            id: 'carbonCopyGroups',
            type: 'select',
            label: 'translate_carbon_copy_groups',
            props: {
                bindLabel: 'name',
                isMulti: true,
                loaderFetcher: keyword =>
                    injector
                        .get(MessagingGroupsService)
                        .list({ attrs: { keyword } })
                        .pipe(map(x => x.items)),
            },
        },

        {
            type: 'group',
            config: [
                {
                    id: 'confidentialityLevel',
                    type: 'select',
                    label: 'translate_confidentiality_level',
                    defaultValue: 'general',
                    props: {
                        items$: injector
                            .get(MiscService)
                            .confidentialityLevels(),
                        bindLabel: 'name',
                        bindValue: 'id',
                        isNotClearable: true,
                        isNotSearchable: true,
                    },
                },

                {
                    id: 'importanceLevel',
                    type: 'select',
                    label: 'translate_importance_level',
                    defaultValue: 'normal',
                    props: {
                        items$: injector.get(MiscService).importanceLevels(),
                        bindLabel: 'name',
                        bindValue: 'id',
                        isNotClearable: true,
                        isNotSearchable: true,
                    },
                },
            ],
        },

        {
            type: 'group',
            config: [
                {
                    id: 'daysToReply',
                    type: 'input',
                    label: 'translate_days_to_reply',
                    props: {
                        type: 'number',
                    },
                },
            ],
        },

        {
            id: 'relevantEmployees',
            type: 'select',
            label: 'translate_relevant_employees',
            props: {
                loaderFetcher: keyword =>
                    injector
                        .get(EmployeesService)
                        .simpleList({ attrs: { keyword } })
                        .pipe(map(data => data.items)),
                bindLabel: 'name',
                compareWith: (a, b) => a.id === b.id,
                isMulti: true,
            },
        },

        {
            id: 'isStampShown',
            type: 'input',
            hide: message.type === 'message',
            label: 'translate_show_stamp',
            props: {
                type: 'checkbox',
            },
        },
    ];
};
