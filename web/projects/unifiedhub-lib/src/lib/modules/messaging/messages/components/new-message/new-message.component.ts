import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    Injector,
    input,
    OnInit,
    OutputRefSubscription,
    signal,
    viewChild,
} from '@angular/core';
import { config } from './form.config';
import {
    DynamicFormComponent,
    ModalService,
    SmartAlertService,
} from '../../../../../features';
import { WysiwygComponent } from '../../../components';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { firstValueFrom, switchMap } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { MessagesService } from '../../messages.service';
import { Message, MessageApprovalTransaction } from '../../../core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
    ToastService,
    TranslateMultilingualStringPipe,
} from '../../../../../core';
import { MessageBroadcastingService } from '../../message-broadcasting.service';
import { MessageAttachmentListComponent } from './components/message-attachment-list/message-attachment-list.component';
import { MessageTemplateSelectorComponent } from './components/message-template-selector/message-template-selector.component';
import { SaveCurrentBodyAsMessageTemplateDialogComponent } from './components/new-message-template-dialog/save-current-body-as-message-template-dialog.component';
import {
    AlertComponent,
    TooltipDirective,
    WaitUntilLoadedDirective,
} from '../../../../../ui';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { p } from '../../../../../permissions';
import { SendForApprovalDialogComponent } from './components/send-for-approval-dialog/send-for-approval-dialog.component';
import { MessageApprovalTransactionListComponent } from '../message-approval-transaction-list/message-approval-transaction-list.component';
import { MessagePdfPreviewComponent } from '../message-pdf-preview/message-pdf-preview.component';
import { NgClass } from '@angular/common';
import { FormatEmployeeNamePipe } from '../../../../employment/core';
import { MessageEmployeeListInputComponent } from './components/message-employee-list-input/message-employee-list-input.component';
import { getSingularForm } from '../../../../../common';

@Component({
    selector: 'lib-new-message',
    templateUrl: 'new-message.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicFormComponent,
        WysiwygComponent,
        ReactiveFormsModule,
        TranslateModule,
        MessageAttachmentListComponent,
        MessageTemplateSelectorComponent,
        TooltipDirective,
        MessagePdfPreviewComponent,
        NgClass,
        WaitUntilLoadedDirective,
        AlertComponent,
        FormatEmployeeNamePipe,
        TranslateMultilingualStringPipe,
        MessageEmployeeListInputComponent,
    ],
})
export class NewMessageComponent implements OnInit {
    public readonly messageId = input.required<string>();

    protected config = computed(() =>
        this.message() ? config(this.message()!, this.injector) : undefined,
    );
    protected formGroup = signal<FormGroup | undefined>(undefined);
    protected approvalTransactions = signal<MessageApprovalTransaction[]>([]);
    protected isPdfPreviewShown = signal<boolean>(false);
    protected readonly message = signal<Message | undefined>(undefined);

    protected readonly p = p;

    private readonly messagePdfPreviewComponent =
        viewChild<MessagePdfPreviewComponent>('messagePdfPreviewComponent');

    public constructor(
        private readonly injector: Injector,
        private readonly messagesService: MessagesService,
        private readonly smartAlertService: SmartAlertService,
        private readonly translateService: TranslateService,
        private readonly modalService: ModalService,
        private readonly toastService: ToastService,
        private readonly messagesBroadcastingService: MessageBroadcastingService,
        private readonly destroyRef: DestroyRef,
    ) {}

    public ngOnInit(): void {
        this.load();
    }

    protected async sendForApproval(): Promise<void> {
        let subscription: OutputRefSubscription;

        const component = await this.modalService.show(
            SendForApprovalDialogComponent,
            {
                title: await firstValueFrom(
                    this.translateService.get('translate_send_for_approval'),
                ),
                inputs: {
                    messageId: this.messageId(),
                },
                onDismiss: () => {
                    subscription?.unsubscribe();
                },
                injector: this.injector,
            },
        );

        subscription = component.sendForApproval.subscribe(() => {
            this.messagesBroadcastingService.broadcast({
                type: 'sentForApproval',
                messageIds: [this.messageId()],
            });
            this.modalService.dismiss(component);
            this.modalService.dismiss(this);
        });
    }

    protected async togglePdfPreview(): Promise<void> {
        this.isPdfPreviewShown.update(x => !x);
    }

    protected discard(): void {
        this.translateService
            .get('translate_discard_this_message')
            .pipe(switchMap(str => this.smartAlertService.confirm(str)))
            .subscribe(isConfirmed => {
                if (!isConfirmed) return;

                this.messagesService
                    .discard([this.messageId()])
                    .subscribe(async () => {
                        await this.modalService.dismiss(this);
                        this.messagesBroadcastingService.broadcast({
                            type: 'delete',
                            messageIds: [this.messageId()],
                        });
                        this.translateService
                            .get('translate_operation_successful')
                            .subscribe(str => this.toastService.success(str));
                    });
            });
    }

    protected updateFormGroup(formGroup: FormGroup): void {
        formGroup.addControl('body', new FormControl());
        this.formGroup.set(formGroup);

        this.monitorChanges();
        this.fillForm();
    }

    protected async showTransactionList(): Promise<void> {
        await this.modalService.show(MessageApprovalTransactionListComponent, {
            title: await firstValueFrom(
                this.translateService.get('translate_transaction_history'),
            ),

            inputs: {
                transactions: this.approvalTransactions(),
            },

            injector: this.injector,
        });
    }

    protected async saveAsTemplate(): Promise<void> {
        await this.modalService.show(
            SaveCurrentBodyAsMessageTemplateDialogComponent,
            {
                title: await firstValueFrom(
                    this.translateService.get(
                        'translate_save_current_as_template',
                    ),
                ),
                injector: this.injector,
                inputs: {
                    body: this.formGroup()?.controls['body'].value,
                },
            },
        );
    }

    private monitorChanges(): void {
        this.formGroup()!
            .valueChanges.pipe(
                debounceTime(1000),
                takeUntilDestroyed(this.destroyRef),
            )
            .subscribe(values => {
                values['body'] = values['body']
                    ? JSON.stringify(values['body'])
                    : null;

                const objectToIdsFields = [
                    'relevantEmployees',
                    'recipientGroups',
                    'carbonCopyGroups',
                ];
                objectToIdsFields.forEach(key => {
                    values[getSingularForm(key) + 'Ids'] = values[key].map(
                        (x: { id: string }) => x.id,
                    );
                    delete values[key];
                });

                this.messagesService
                    .update(this.messageId(), values)
                    .subscribe(() => {
                        this.messagePdfPreviewComponent()?.reload();
                    });
            });
    }

    private load(): void {
        this.messagesService.get(this.messageId()).subscribe(message =>
            this.message.set({
                ...message,
                body: JSON.parse(message.body),
            }),
        );

        this.messagesService
            .listApprovalTransactions(this.messageId())
            .subscribe(items => this.approvalTransactions.set(items));
    }

    private fillForm(): void {
        const message = this.message()!;

        Object.keys(this.formGroup()!.controls).forEach(x => {
            this.formGroup()?.controls[x].setValue(
                (message as Record<string, unknown>)[x],
            );
        });
    }
}
