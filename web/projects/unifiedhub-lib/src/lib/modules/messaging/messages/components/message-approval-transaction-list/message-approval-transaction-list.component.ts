import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
} from '@angular/core';
import { MessageApprovalTransaction } from '../../../core';
import { DynamicListComponent } from '../../../../../features';
import { config } from './list.config';
import { MessagesService } from '../../messages.service';

@Component({
    selector: 'lib-message-approval-transaction-list',
    templateUrl: 'message-approval-transaction-list.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicListComponent],
})
export class MessageApprovalTransactionListComponent {
    public readonly transactions =
        input.required<MessageApprovalTransaction[]>();

    protected readonly config = computed(() =>
        config(this.transactions(), this.messagesService),
    );

    public constructor(private readonly messagesService: MessagesService) {}
}
