<!-- Attachment List Container -->
<div class="flex flex-col gap-4">
    <!-- Attachments list -->
    @if (attachments().length) {
        <div class="mt-4 space-y-2">
            @for (item of attachments(); track item) {
                <div
                    class="flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3"
                >
                    <div class="flex grow items-center gap-2">
                        <!-- Icon -->
                        <i class="fal fa-file text-xl text-gray-400"></i>

                        <div class="flex flex-col gap-2">
                            <!-- File name & size -->
                            <div class="flex flex-row items-center gap-2">
                                <!-- File name -->
                                <span
                                    class="text-sm text-gray-700"
                                    [ngClass]="{
                                        'cursor-pointer hover:text-primary hover:underline':
                                            item.progress === 1
                                    }"
                                    (click)="
                                        !currentlyProcessing().has(item) &&
                                            item.progress &&
                                            view(item)
                                    "
                                >
                                    {{
                                        item.file.name
                                            | translateMultilingualString
                                    }}
                                </span>

                                <!-- Size -->
                                <span class="text-xs text-gray-500">
                                    ({{
                                        item.file.sizeInBytes ?? 0
                                            | formatBytes
                                    }})
                                </span>
                            </div>

                            <!-- Progress -->
                            @if (item.progress !== 1) {
                                <div
                                    class="relative h-1 w-32 overflow-hidden rounded-2xl bg-gray-300"
                                >
                                    <div
                                        class="absolute top-0 h-full bg-primary-500 transition-all ltr:left-0 rtl:right-0"
                                        [ngStyle]="{
                                            'width': item.progress * 100 + '%'
                                        }"
                                    ></div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Remove button -->
                    <button
                        (click)="delete(item)"
                        [disabled]="currentlyProcessing().has(item)"
                        class="text-gray-400 hover:text-red-500"
                    >
                        <i class="fal fa-times text-xl"></i>
                    </button>
                </div>
            }
        </div>
    }

    <input
        multiple
        (change)="add($event)"
        #fileInput
        type="file"
        class="hidden"
    />
</div>
