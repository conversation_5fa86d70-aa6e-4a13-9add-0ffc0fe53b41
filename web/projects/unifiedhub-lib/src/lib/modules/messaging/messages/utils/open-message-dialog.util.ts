import { NewMessageComponent } from '../components/new-message/new-message.component';
import { ModalService } from '../../../../features';
import { Injector } from '@angular/core';
import { MessageBroadcastingService } from '../message-broadcasting.service';
import { Message } from '../../core';

export const openMessageDialog = async (
    type: Message['type'],
    messageId: string,
    modalService: ModalService,
    messagesBroadcastingService: MessageBroadcastingService,
    injector: Injector,
): Promise<void> => {
    await modalService.show(NewMessageComponent, {
        title:
            type === 'circular'
                ? 'translate_new_circular'
                : type === 'directive'
                  ? 'translate_new_directive'
                  : type === 'order'
                    ? 'translate_new_order'
                    : 'translate_new_message',
        size: {
            width: '98%',
            height: '95%',
        },
        onDismiss: () => {
            messagesBroadcastingService.broadcast({
                type: 'update',
                messageIds: [messageId],
            });
        },
        injector: injector,
        inputs: {
            messageId,
        },
    });
};
