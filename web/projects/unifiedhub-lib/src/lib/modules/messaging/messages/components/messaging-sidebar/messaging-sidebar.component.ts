import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    output,
    signal,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MessageCount, MessageFolder } from '../../../core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { p } from '../../../../../permissions';
import { MessagesService } from '../../messages.service';
import { MessageBroadcastingService } from '../../message-broadcasting.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RbacDirective } from '../../../../../core';
import { items } from './items';

@Component({
    selector: 'lib-messaging-sidebar',
    templateUrl: './messaging-sidebar.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, NgClass, RbacDirective, NgTemplateOutlet],
})
export class MessagingSidebarComponent {
    public readonly selectionChange = output<MessageFolder>();

    protected readonly selectedFolder = signal<MessageFolder>('inbox');
    protected readonly p = p;
    protected readonly items = computed(() => items(this.count()));

    private readonly count = signal<MessageCount | undefined>(undefined);

    public constructor(
        private readonly messagesService: MessagesService,
        private readonly messageBroadcastService: MessageBroadcastingService,
        private readonly destroyRef: DestroyRef,
    ) {
        this.reloadCount();
        this.handleMessagesBroadcasts();
    }

    protected changeSelection(selection: MessageFolder): void {
        this.selectedFolder.set(selection);
        this.selectionChange.emit(selection);
    }

    private reloadCount(): void {
        this.messagesService.counts().subscribe(data => this.count.set(data));
    }

    private handleMessagesBroadcasts(): void {
        this.messageBroadcastService
            .listen()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(data => {
                switch (data.type) {
                    case 'pinned':
                    case 'unpinned':
                    case 'read':
                    case 'sent':
                    case 'sentForApproval':
                    case 'returnedFromApproval':
                        this.reloadCount();
                        break;
                }
            });
    }
}
