import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    input,
    signal,
} from '@angular/core';
import {
    DynamicNewFormComponent,
    DynamicNewFormConfig,
    ModalService,
} from '../../../../../../../features';
import { newFormConfigFn } from '../../../../../message-templates/pages/message-template-new-page/new.config';
import { MessageTemplatesService } from '../../../../../message-templates/message-templates.service';
import { WysiwygComponent } from '../../../../../components';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MessageTemplate } from '../../../../../core';
import { MessageBroadcastingService } from '../../../../message-broadcasting.service';

@Component({
    selector: 'lib-save-current-body-as-message-template-dialog',
    templateUrl: 'save-current-body-as-message-template-dialog.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewFormComponent, WysiwygComponent, ReactiveFormsModule],
})
export class SaveCurrentBodyAsMessageTemplateDialogComponent {
    public body = input.required<unknown>();

    protected readonly config = computed<DynamicNewFormConfig<MessageTemplate>>(
        () => {
            return newFormConfigFn(this.messageTemplatesService);
        },
    );
    protected readonly form = signal<FormGroup | undefined>(undefined);

    public constructor(
        protected readonly messagesBroadcastingService: MessageBroadcastingService,
        private readonly messageTemplatesService: MessageTemplatesService,
        private readonly modalService: ModalService,
    ) {
        effect(() => {
            if (!this.form()?.controls['body']) return;
            this.form()!.controls['body'].setValue(this.body());
        });
    }

    protected async handleCreation(): Promise<void> {
        this.messagesBroadcastingService.broadcast({
            type: 'template_created',
        });

        await this.modalService.dismiss(this);
    }
}
