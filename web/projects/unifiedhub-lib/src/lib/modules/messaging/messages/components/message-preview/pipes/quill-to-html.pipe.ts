import { Pipe, PipeTransform } from '@angular/core';
import { QuillDeltaToHtmlConverter } from 'quill-delta-to-html';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
    name: 'quillToHtml',
    standalone: true,
})
export class QuillToHtmlPipe implements PipeTransform {
    public constructor(private readonly sanitizer: DomSanitizer) {}

    public transform(value: string): SafeHtml {
        const deltaOps = JSON.parse(value)['ops'];

        if (!deltaOps || !Array.isArray(deltaOps)) {
            return '';
        }

        const converter = new QuillDeltaToHtmlConverter(deltaOps, {
            inlineStyles: true,
            paragraphTag: 'p',
            multiLineBlockquote: true,
            multiLineHeader: true,
            multiLineCodeblock: true,
        });

        const html = converter.convert();

        return this.sanitizer.bypassSecurityTrustHtml(html);
    }
}
