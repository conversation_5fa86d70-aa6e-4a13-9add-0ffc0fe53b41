import {
    LinkerConfig,
    ListFilterConfig,
    SelectFilterConfig,
} from '../../../../../../../features';
import {
    Employee,
    formatEmployeeName,
} from '../../../../../../employment/core';
import { p } from '../../../../../../../permissions';
import { Filter } from '../../../../../../../common';
import { DepartmentsService } from '../../../../../../departments/departments.service';
import { map } from 'rxjs';
import { JobTitlesService } from '../../../../../../employment/job-titles/job-titles.service';
import { JobClassificationsService } from '../../../../../../employment/job-classifications/job-classifications.service';
import { JobLevelsService } from '../../../../../../employment/job-levels/job-levels.service';
import { Injector } from '@angular/core';
import { MessagesService } from '../../../../messages.service';
import { FoundationService } from '../../../../../../foundation/foundation.service';

export const config = (
    messageId: string,
    type: 'recipient' | 'carbon_copy',
    injector: Injector,
): LinkerConfig<Employee, Employee> => {
    const messagesService = injector.get(MessagesService);

    const filters = [
        {
            id: 'keyword',
            type: 'text',
            label: 'translate_search_by_name',
        },

        {
            id: 'number',
            type: 'text',
            label: 'translate_employee_number',
        },

        {
            id: 'departmentIds',
            type: 'select',
            label: 'translate_department',
            items$: injector
                .get(DepartmentsService)
                .list({ pageNumber: 0, pageSize: -1 })
                .pipe(map(data => data.items)),
            bindValue: 'id',
            bindLabel: 'name',
            bindLabelSecond: 'parent.name',
            bindLabelThird: 'parentSecond.name',
            isMulti: true,
        },

        {
            id: 'jobTitleIds',
            type: 'select',
            label: 'translate_job_title',
            items$: injector.get(JobTitlesService).listAll(),
            bindValue: 'id',
            bindLabel: 'name',
            isMulti: true,
        },

        {
            id: 'jobClassificationIds',
            type: 'select',
            label: 'translate_job_classification',
            items$: injector.get(JobClassificationsService).listAll(),
            bindValue: 'id',
            bindLabel: 'name',
            isMulti: true,
            onChange: (value, configs) => {
                const classificationIds = value as string[];
                const jobLevelIdsConfig = configs.find(
                    x => x.id == 'jobLevelIds',
                ) as SelectFilterConfig;
                if (classificationIds?.length) {
                    jobLevelIdsConfig.items$ = injector
                        .get(JobLevelsService)
                        .listAll({
                            classificationIds,
                        });
                } else {
                    jobLevelIdsConfig.items$ = undefined;
                }
            },
        },

        {
            id: 'jobLevelIds',
            type: 'select',
            label: 'translate_job_level',
            bindValue: 'id',
            bindLabel: 'name',
            isMulti: true,
        },

        {
            id: 'genders',
            type: 'select',
            label: 'translate_gender',
            items$: injector.get(FoundationService).genders(),
            bindValue: 'id',
            bindLabel: 'name',
            isMulti: true,
        },
    ] satisfies ListFilterConfig<Employee>[];

    return {
        linked: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],
            valueConfigFn: employee => [
                {
                    value: formatEmployeeName(employee),
                    linkConfig: {
                        permissionId: p.employees.read,
                        value: ['', 'employees', employee.id],
                    },
                },
            ],
            filter: filters,
            fetcher: (filter: Filter<{ keyword?: string }>) =>
                messagesService.getEmployeeListInMessage(
                    messageId,
                    type,
                    filter,
                ),
            transfer: (employees, filter) =>
                messagesService.removeEmployeesFromMessage(
                    messageId,
                    employees.map(x => x.id),
                    type,
                    filter,
                ),
            enableTotalSelection: true,
        },

        unlinked: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],
            valueConfigFn: employee => [
                {
                    value: formatEmployeeName(employee),
                    linkConfig: {
                        permissionId: p.employees.read,
                        value: ['', 'employees', employee.id],
                        shouldOpenInNewTab: true,
                    },
                },
            ],

            filter: filters,

            fetcher: (filter: Filter<{ keyword?: string }>) =>
                messagesService.getEmployeeListNotInMessage(
                    messageId,
                    type,
                    filter,
                ),

            transfer: (employees, filter) => {
                return messagesService.addEmployeesToMessage(
                    messageId,
                    employees.map(x => x.id),
                    type,
                    filter,
                );
            },
            enableTotalSelection: true,
        },
    };
};
