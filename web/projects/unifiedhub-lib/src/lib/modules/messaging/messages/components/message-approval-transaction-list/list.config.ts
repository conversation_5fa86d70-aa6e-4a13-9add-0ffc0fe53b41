import { DynamicListConfig } from '../../../../../features';
import { MessageApprovalTransaction } from '../../../core';
import { formatEmployeeName } from '../../../../employment/core';
import { MessagesService } from '../../messages.service';

export const config = (
    items: MessageApprovalTransaction[],
    messagesService: MessagesService,
): DynamicListConfig<MessageApprovalTransaction> => {
    return {
        columnConfigFn: () => [
            {
                label: 'translate_time',
            },

            {
                label: 'translate_by',
            },

            {
                label: 'translate_from_state',
            },

            {
                label: 'translate_to_state',
            },

            {
                label: 'translate_notes',
            },
        ],

        valueConfigFn: item => [
            {
                value: item.createdTime,
                type: 'datetime',
            },

            {
                value: formatEmployeeName(item.employee),
            },

            {
                value: item.fromState,
                type: 'id_to_name',
                idToName$: messagesService.states(),
            },

            {
                value: item.toState,
                type: 'id_to_name',
                idToName$: messagesService.states(),
            },

            {
                value: item.notes,
            },
        ],
        items: items,
    };
};
