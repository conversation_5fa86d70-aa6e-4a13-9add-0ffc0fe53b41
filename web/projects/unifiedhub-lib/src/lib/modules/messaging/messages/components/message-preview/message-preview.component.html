@if (message()) {
    <div class="flex h-full flex-1 flex-col bg-white">
        <!-- Message Header -->
        <div class="border-b border-gray-200 bg-white px-6 py-4">
            <!-- Top Bar with Reference and Metadata -->
            <div class="mb-4 flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <!-- Reference number -->
                    @if (message()!.referenceNumber) {
                        <span class="text-sm font-medium text-gray-500">
                            <span>{{
                                'translate_reference_number' | translate
                            }}</span>
                            <span class="text-gray-900">
                                {{ message()!.referenceNumber }}
                            </span>
                        </span>
                    }

                    <lib-message-badge-list [message]="message()!" />
                </div>
                <div class="flex items-center gap-2">
                    <!-- View -->
                    @if (message()) {
                        <button
                            (click)="download()"
                            [libTooltip]="'translate_view' | translate"
                            class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    }

                    <!-- Controls that appear once the message is sent -->
                    @if (message()!.sentTime) {
                        <!-- Reply -->
                        @if (
                            message()!.type !== 'circular' &&
                            message()!.type !== 'directive' &&
                            message()!.type !== 'order'
                        ) {
                            <button
                                (click)="reply()"
                                [libTooltip]="'translate_reply' | translate"
                                class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                            >
                                <i class="fas fa-reply"></i>
                            </button>
                        }

                        <!-- Forward -->
                        <button
                            (click)="forward()"
                            [libTooltip]="'translate_forward' | translate"
                            class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                        >
                            <i class="fas fa-share"></i>
                        </button>

                        <!-- Trash -->
                        <button
                            (click)="trash()"
                            [libTooltip]="'translate_delete' | translate"
                            class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                        >
                            <i class="fas fa-trash"></i>
                        </button>
                    } @else if (message()!.state === 'submitted') {
                        <!-- Transaction history -->
                        @if (approvalTransactions().length) {
                            <button
                                (click)="showTransactionList()"
                                [libTooltip]="
                                    'translate_transaction_history' | translate
                                "
                                class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                            >
                                <i class="fa fa-clock-rotate-left"></i>
                            </button>
                        }

                        <!-- Withdraw from approval -->
                        @if (
                            message()!.sendingEmployee.user?.id &&
                            message()!.sendingEmployee!.user!.id ===
                                (authService.identity$ | async)?.user?.id
                        ) {
                            <button
                                (click)="withdrawFromApproval()"
                                [libTooltip]="
                                    'translate_withdraw_from_approval'
                                        | translate
                                "
                                class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                            >
                                <i class="fa fa-arrow-up-from-bracket"></i>
                            </button>
                        }

                        @if (
                            message()!.approvingEmployee?.user?.id &&
                            message()!.approvingEmployee!.user!.id ===
                                (authService.identity$ | async)?.user?.id
                        ) {
                            <!-- Approve -->
                            <button
                                (click)="approveAndSend()"
                                [libTooltip]="
                                    'translate_approve_and_send_message'
                                        | translate
                                "
                                class="rounded p-2 text-green-400 hover:bg-green-100 hover:text-green-600"
                            >
                                <i class="fas fa-circle-check"></i>
                            </button>

                            <!-- Return -->
                            <button
                                (click)="return()"
                                [libTooltip]="
                                    'translate_return_message' | translate
                                "
                                class="rounded p-2 text-red-400 hover:bg-red-100 hover:text-red-600"
                            >
                                <i class="fas fa-circle-xmark"></i>
                            </button>
                        }
                    }
                </div>
            </div>

            <!-- Subject Line -->
            <h2 class="mb-4 text-xl font-semibold text-gray-900">
                {{ message()!.subject ?? ('translate_untitled' | translate) }}
            </h2>

            <!-- Sender Information -->
            <div class="mb-4 flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-primary-100"
                    >
                        <span class="font-medium text-primary-700">
                            {{
                                message()!.sendingEmployee.name
                                    | translateMultilingualString
                                    | initials
                            }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-900">
                            {{
                                message()!.sendingEmployee
                                    | formatEmployeeName
                                    | translateMultilingualString
                            }}
                        </span>

                        <!-- Department -->
                        @if (message()!.sendingEmployee.department) {
                            <div class="text-sm text-gray-500">
                                {{
                                    message()!.sendingDepartment ??
                                        message()!.sendingEmployee!.department!
                                        | formatDepartmentName: 1
                                        | translateMultilingualString
                                }}
                            </div>
                        }
                    </div>
                </div>
                <div class="text-right">
                    <!-- Sending time -->
                    <div class="text-sm text-gray-500">
                        {{ message()!.sentTime | date: 'yyyy-MM-dd hh:mm a' }}
                    </div>

                    <!-- Reply in days -->
                    @if (message()!.daysToReply) {
                        <div class="text-sm text-orange-600">
                            {{
                                'translate_should_reply_within_days_days'
                                    | translate
                                        : { days: message()!.daysToReply }
                            }}
                        </div>
                    }
                </div>
            </div>

            <!-- Recipients -->
            <div class="flex flex-col gap-2">
                <!-- Individual Recipients -->
                @if (message()!.sampleRecipientEmployees.length) {
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-500">
                            {{ 'translate_to' | translate }}
                        </span>
                        <div class="flex flex-wrap items-center gap-1 text-xs">
                            @for (
                                item of message()!.sampleRecipientEmployees
                                    | take: 7;
                                track item.id
                            ) {
                                <span
                                    class="rounded-full bg-gray-100 px-2 py-1 text-gray-600"
                                >
                                    {{
                                        item.name | translateMultilingualString
                                    }}
                                </span>
                            }
                            @if (
                                message()!.sampleRecipientEmployees.length !==
                                message()!.recipientEmployeesCount
                            ) {
                                <span class="text-gray-400">
                                    {{
                                        'translate_and_count_others'
                                            | translate
                                                : {
                                                      count:
                                                          message()!
                                                              .recipientEmployeesCount -
                                                          message()!
                                                              .sampleCarbonCopyEmployees
                                                              .length
                                                  }
                                    }}
                                </span>
                            }
                        </div>
                    </div>
                }

                <!-- Group Recipients -->
                @if (message()!.recipientGroups.length) {
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-500">
                            {{ 'translate_groups' | translate }}
                        </span>
                        <div class="flex flex-wrap items-center gap-1">
                            @for (
                                item of message()!.recipientGroups;
                                track item.id
                            ) {
                                <span
                                    class="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
                                >
                                    {{
                                        item.name | translateMultilingualString
                                    }}
                                </span>
                            }
                        </div>
                    </div>
                }

                <!-- Relevant Employees -->
                @if (message()!.relevantEmployees.length) {
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-500">
                            {{ 'translate_relevant_employees' | translate }}
                        </span>
                        <div class="flex flex-wrap items-center gap-1">
                            @for (
                                item of message()!.relevantEmployees;
                                track item.id
                            ) {
                                <span
                                    class="rounded-full bg-primary-50 px-2 py-1 text-xs text-primary-700"
                                >
                                    {{
                                        item.name | translateMultilingualString
                                    }}
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Circular/order/director ribbon -->
        @if (message()!.type !== 'message') {
            <div class="border-b bg-amber-100 p-2 text-xs font-bold">
                <span>
                    {{
                        'translate_this_message_is_of_type_type_and_was_approved_by_approver_to_view_the_full_message_click'
                            | translate
                                : {
                                      type:
                                          message()!.type
                                          | idToName: messagesService.types()
                                          | async,
                                      approver:
                                          approver()!.name
                                          | translateMultilingualString
                                  }
                    }}
                </span>
                <button
                    (click)="download()"
                    class="text-blue-600 outline-none hover:underline"
                >
                    {{ 'translate_lower_here' | translate }}
                </button>
                <span>.</span>
            </div>
        }

        <!-- Pending approval ribbon -->
        @if (message()!.state === 'submitted' && message()!.approvingEmployee) {
            <div
                class="flex flex-row items-center gap-2 border-b bg-amber-100 p-2 text-xs"
            >
                <i class="fa fa-gavel"></i>
                <span class="grow font-bold">
                    {{
                        'translate_waiting_for_employee_to_approve'
                            | translate
                                : {
                                      employee:
                                          message()!.approvingEmployee!
                                          | formatEmployeeName
                                          | translateMultilingualString
                                  }
                    }}
                </span>

                @if (
                    message()!.approvingEmployee?.user?.id &&
                    message()!.approvingEmployee!.user!.id ===
                        (authService.identity$ | async)?.user?.id
                ) {
                    <div class="flex shrink-0 flex-row items-center gap-2">
                        <button
                            (click)="approveAndSend()"
                            class="btn btn-sm btn-success"
                        >
                            {{
                                'translate_approve_and_send_message' | translate
                            }}
                        </button>

                        <button
                            (click)="return()"
                            class="btn btn-sm btn-danger"
                        >
                            {{ 'translate_return_message' | translate }}
                        </button>
                    </div>
                }
            </div>
        }

        <!-- Message Content -->
        <div class="flex-1 overflow-auto p-6">
            <div class="flex flex-col">
                <!-- Body -->
                <div [innerHTML]="message()!.body | quillToHtml"></div>

                <!-- Sender name & title -->
                @if (message()!.senderName) {
                    <div
                        class="mt-8 flex flex-col items-center gap-2 self-end text-gray-500"
                    >
                        @if (message()!.senderTitle) {
                            <div class="text-sm">
                                {{
                                    message()!.senderTitle
                                        | translateMultilingualString
                                }}
                            </div>
                        }

                        <div>
                            {{
                                message()!.senderName
                                    | translateMultilingualString
                            }}
                        </div>
                    </div>
                }

                <!-- Stamp -->
                @if (message()!.type !== 'message' && message()!.isStampShown) {
                    <lib-image
                        class="mt-8 h-40 w-40 self-center"
                        [imageObservable]="stampBlob$"
                    />
                }

                <!-- Attachment -->
                <div class="mt-4 flex flex-col gap-2">
                    @for (item of message()!.attachments; track item.id) {
                        <div class="rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center gap-3">
                                <div class="rounded-lg bg-gray-100 p-2">
                                    <i
                                        class="fas fa-file text-2xl text-gray-600"
                                    ></i>
                                </div>
                                <div class="flex-1">
                                    <div
                                        class="text-sm font-medium text-gray-900"
                                    >
                                        {{
                                            item.name
                                                | translateMultilingualString
                                        }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{
                                            item.sizeInBytes ?? 0 | formatBytes
                                        }}
                                    </div>
                                </div>
                                <button
                                    (click)="downloadAttachment(item)"
                                    class="rounded p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                                >
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
} @else {
    <div
        class="flex h-full w-full flex-col items-center justify-center gap-4 text-gray-400"
    >
        <i class="fal fa-envelope-open text-7xl"></i>

        <span class="text-sm">
            {{ 'translate_no_message_is_selected' | translate }}
        </span>
    </div>
}
