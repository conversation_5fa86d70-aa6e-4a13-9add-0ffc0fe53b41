import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    effect,
    input,
    OnDestroy,
    signal,
} from '@angular/core';
import { MessagesService } from '../../messages.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { WaitUntilLoadedDirective } from '../../../../../ui';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject, switchMap } from 'rxjs';

@Component({
    selector: 'lib-message-pdf-preview',
    templateUrl: 'message-pdf-preview.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [WaitUntilLoadedDirective],
})
export class MessagePdfPreviewComponent implements OnDestroy {
    public readonly messageId = input.required<string>();
    protected readonly safePdfUrl = signal<SafeResourceUrl | undefined>(
        undefined,
    );
    private pdfUrl: string | undefined = undefined;
    private reloadSubject$ = new Subject<void>();

    public constructor(
        private readonly messagesService: MessagesService,
        private readonly sanitizer: DomSanitizer,
        private readonly destroyRef: DestroyRef,
    ) {
        this.setupReloadSubject();
        effect(() => {
            this.reload();
        });
    }

    public ngOnDestroy(): void {
        if (this.pdfUrl) {
            URL.revokeObjectURL(this.pdfUrl);
        }
    }

    public reload(): void {
        this.reloadSubject$.next();
    }

    private setupReloadSubject(): void {
        this.reloadSubject$
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                switchMap(() =>
                    this.messagesService.download(this.messageId()),
                ),
            )
            .subscribe(blob => {
                this.pdfUrl = URL.createObjectURL(blob);
                this.safePdfUrl.set(
                    this.sanitizer.bypassSecurityTrustResourceUrl(
                        this.pdfUrl + '#toolbar=0&navpanes=0&scrollbar=0', // cspell: disable-line
                    ),
                );
            });
    }
}
