<div
    class="h-full w-64 overflow-y-auto border-gray-200 bg-white ltr:border-r rtl:border-l"
>
    <nav>
        <ng-container
            [ngTemplateOutlet]="itemListTemplate"
            [ngTemplateOutletContext]="{ items: items() }"
        />
    </nav>
</div>

<ng-template #itemListTemplate let-items="items">
    @for (item of items; track item.id) {
        @switch (item.type) {
            @case ('folder') {
                <ng-container
                    [ngTemplateOutlet]="folderTemplate"
                    [ngTemplateOutletContext]="{ folder: item }"
                />
            }
            @case ('group') {
                <ng-container
                    [ngTemplateOutlet]="groupTemplate"
                    [ngTemplateOutletContext]="{ group: item }"
                />
            }
        }
    }
</ng-template>

<ng-template #folderTemplate let-folder="folder">
    <button
        *libRbac="folder.permissionIds"
        class="flex w-full flex-row items-center gap-2 px-4 py-2 text-sm"
        [ngClass]="{
            'bg-primary-400 text-white': selectedFolder() === folder.id
        }"
        (click)="changeSelection($any(folder.id))"
    >
        <i class="fas {{ folder.icon }} shrink-0"></i>
        <span class="grow ltr:text-left rtl:text-right">
            {{ 'translate_' + folder.id | translate }}
        </span>

        @if (folder.count) {
            <span
                class="shrink-0 rounded-full bg-primary-100 px-2 py-1 text-xs text-primary-600"
                [ngClass]="{
                    'bg-white text-primary-500': selectedFolder() === folder.id
                }"
            >
                {{ folder.count }}
            </span>
        }
    </button>
</ng-template>

<ng-template #groupTemplate let-group="group">
    <ng-container *libRbac="group.permissionIds">
        <div
            class="flex flex-row items-center justify-center gap-2 bg-gray-100 p-2 text-center text-xs font-bold text-gray-400"
        >
            <i class="fas {{ group.icon }}"></i>
            <span>
                {{ group.label | translate }}
            </span>
        </div>

        <div class="mb-2">
            <ng-container
                [ngTemplateOutlet]="itemListTemplate"
                [ngTemplateOutletContext]="{ items: group.items }"
            />
        </div>
    </ng-container>
</ng-template>
