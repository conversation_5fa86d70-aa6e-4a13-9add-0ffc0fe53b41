import { Inject, Injectable } from '@angular/core';
import {
    HttpClient,
    HttpContext,
    HttpEvent,
    HttpParams,
} from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../../config';
import { Observable } from 'rxjs';
import {
    Message,
    MessageApprovalTransaction,
    MessageCount,
    MessageFolder,
} from '../core';
import {
    File,
    Filter,
    Item,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';
import { CACHE_INTERCEPTOR } from '../../../core';
import { Employee } from '../../employment/core';

@Injectable()
export class MessagesService {
    public constructor(
        private readonly httpClient: HttpClient,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {}

    /** CRUD **/

    public list(
        folder: MessageFolder,
        keyword?: string,
        before?: Date,
        since?: Date,
        pageSize?: number,
    ): Observable<Message[]> {
        let params = new HttpParams();
        params = params.append('folder', folder);
        if (keyword) params = params.append('keyword', keyword);
        if (before) params = params.append('before', before.toISOString());
        if (since) params = params.append('since', since.toISOString());
        if (pageSize) params = params.append('pageSize', pageSize);

        return this.httpClient.get<Message[]>(
            `${this.config.apiUrl}/messaging/messages`,
            {
                params,
            },
        );
    }

    public get(id: string): Observable<Message> {
        return this.httpClient.get<Message>(
            `${this.config.apiUrl}/messaging/messages/${id}`,
        );
    }

    public create(
        replyToMessageId?: string,
        forwardMessageId?: string,
    ): Observable<Message> {
        return this.httpClient.post<Message>(
            `${this.config.apiUrl}/messaging/messages`,
            { replyToMessageId, forwardMessageId },
        );
    }

    public createCircular(): Observable<Message> {
        return this.httpClient.post<Message>(
            `${this.config.apiUrl}/messaging/messages/circulars`,
            undefined,
        );
    }

    public createDirective(): Observable<Message> {
        return this.httpClient.post<Message>(
            `${this.config.apiUrl}/messaging/messages/directives`,
            undefined,
        );
    }

    public createOrder(): Observable<Message> {
        return this.httpClient.post<Message>(
            `${this.config.apiUrl}/messaging/messages/orders`,
            undefined,
        );
    }

    public update(id: string, message: Message): Observable<Message> {
        return this.httpClient.put<Message>(
            `${this.config.apiUrl}/messaging/messages/${id}`,
            message,
        );
    }

    public delete(ids: string[]): Observable<void> {
        let params = new HttpParams();
        ids.forEach(id => (params = params.append('ids', id)));
        return this.httpClient.delete<void>(
            `${this.config.apiUrl}/messaging/messages`,
            {
                params,
            },
        );
    }

    public download(id: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.config.apiUrl}/messaging/messages/${id}/download`,
            {
                responseType: 'blob',
            },
        );
    }

    /** MESSAGE OPERATIONS **/

    public discard(ids: string[]): Observable<void> {
        let params = new HttpParams();
        ids.forEach(id => (params = params.append('ids', id)));
        return this.httpClient.delete<void>(
            `${this.config.apiUrl}/messaging/messages/discard`,
            {
                params,
            },
        );
    }

    public trash(ids: string[]): Observable<void> {
        let params = new HttpParams();
        ids.forEach(id => (params = params.append('ids', id)));
        return this.httpClient.delete<void>(
            `${this.config.apiUrl}/messaging/messages/trash`,
            {
                params,
            },
        );
    }

    public send(id: string): Observable<void> {
        return this.httpClient.post<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/send`,
            {},
        );
    }

    public togglePin(id: string, isPinned: boolean): Observable<void> {
        return this.httpClient.put<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/toggle-pin?isPinned=${isPinned}`,
            null,
        );
    }

    public toggleArchive(id: string, isArchived: boolean): Observable<void> {
        return this.httpClient.put<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/toggle-archive?isArchived=${isArchived}`,
            null,
        );
    }

    public setAsRead(id: string): Observable<void> {
        return this.httpClient.put<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/set-as-read`,
            null,
        );
    }

    /** ATTACHMENTS **/

    public listAttachments(id: string): Observable<File[]> {
        return this.httpClient.get<File[]>(
            `${this.config.apiUrl}/messaging/messages/${id}/attachments`,
        );
    }

    public downloadAttachment(id: string, fileId: string): Observable<Blob> {
        return this.httpClient.get(
            `${this.config.apiUrl}/messaging/messages/${id}/attachments/${fileId}`,
            {
                responseType: 'blob',
            },
        );
    }

    public addAttachment(id: string, file: File): Observable<HttpEvent<File>> {
        return this.httpClient.post<File>(
            `${this.config.apiUrl}/messaging/messages/${id}/attachments`,
            file,
            {
                reportProgress: true,
                observe: 'events',
            },
        );
    }

    public deleteAttachment(id: string, fileId: string): Observable<void> {
        return this.httpClient.delete<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/attachments/${fileId}`,
        );
    }

    /** APPROVAL **/
    public listApprovalTransactions(
        id: string,
    ): Observable<MessageApprovalTransaction[]> {
        return this.httpClient.get<MessageApprovalTransaction[]>(
            `${this.config.apiUrl}/messaging/messages/${id}/approval/transactions`,
        );
    }

    public sendForApproval(
        id: string,
        data: {
            approvingEmployeeId: string;
            approvingEmployeeTitle?: string;
            notes?: string;
        },
    ): Observable<void> {
        return this.httpClient.post<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/approval/send`,
            data,
        );
    }

    public returnFromApproval(id: string, notes: string): Observable<void> {
        return this.httpClient.post<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/approval/return`,
            {
                notes,
            },
        );
    }

    public withdrawFromApproval(id: string): Observable<void> {
        return this.httpClient.post<void>(
            `${this.config.apiUrl}/messaging/messages/${id}/approval/withdraw`,
            undefined,
        );
    }

    /** EMPLOYEE LINKING **/
    public getEmployeeListInMessage(
        id: string,
        type: 'recipient' | 'carbon_copy',
        filter: Filter<unknown>,
    ): Observable<PaginatedResult<Employee>> {
        let params = mapFilterToQueryParams(filter);
        params = params.append('type', type);

        return this.httpClient.get<PaginatedResult<Employee>>(
            `${this.config.apiUrl}/messaging/messages/${id}/employees`,
            { params },
        );
    }

    public getEmployeeListNotInMessage(
        id: string,
        type: 'recipient' | 'carbon_copy',
        filter: Filter<unknown>,
    ): Observable<PaginatedResult<Employee>> {
        let params = mapFilterToQueryParams(filter);
        params = params.append('type', type);

        return this.httpClient.get<PaginatedResult<Employee>>(
            `${this.config.apiUrl}/messaging/messages/${id}/employees/others`,
            { params },
        );
    }

    public addEmployeesToMessage(
        id: string,
        employeeIds: string[],
        type: 'recipient' | 'carbon_copy',
        filter: unknown | undefined,
    ): Observable<any> {
        return this.httpClient.post(
            `${this.config.apiUrl}/messaging/messages/${id}/employees/add`,
            {
                employeeIds,
                type,
                byFilter: !!filter,
                ...(filter ? filter : {}),
            },
        );
    }

    public removeEmployeesFromMessage(
        id: string,
        employeeIds: string[],
        type: 'recipient' | 'carbon_copy',
        filter: unknown | undefined,
    ): Observable<any> {
        return this.httpClient.post(
            `${this.config.apiUrl}/messaging/messages/${id}/employees/remove`,
            {
                employeeIds,
                type,
                byFilter: !!filter,
                ...(filter ? filter : {}),
            },
        );
    }

    /** OTHER **/

    public counts(): Observable<MessageCount> {
        return this.httpClient.get<MessageCount>(
            `${this.config.apiUrl}/messaging/messages/counts`,
        );
    }

    public types(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.config.apiUrl}/messaging/messages/types`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }

    public states(): Observable<Item[]> {
        return this.httpClient.get<Item[]>(
            `${this.config.apiUrl}/messaging/messages/approval/states`,
            {
                context: new HttpContext().set(CACHE_INTERCEPTOR, true),
            },
        );
    }
}
