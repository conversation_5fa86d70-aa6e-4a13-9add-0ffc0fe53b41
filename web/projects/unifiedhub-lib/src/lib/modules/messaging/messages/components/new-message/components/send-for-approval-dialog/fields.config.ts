import {
    DynamicFormSelectConfig,
    DynamicNewFormConfig,
} from '../../../../../../../features';
import { EmployeesService } from '../../../../../../employment/employees/employees.service';
import { Injector } from '@angular/core';
import { MessagesService } from '../../../../messages.service';
import { map } from 'rxjs';
import { formatEmployeeName } from '../../../../../../employment/core';
import { MultilingualString } from '../../../../../../../common';

type FormModel = {
    approvingEmployeeId: string;
    approvingEmployeeTitleSelect?: MultilingualString;
    approvingEmployeeTitleText?: MultilingualString;
    notes?: string;
};

export const config = (
    messageId: string,
    injector: Injector,
    onSent: () => void,
): DynamicNewFormConfig<FormModel> => {
    const messagesService = injector.get(MessagesService);
    return {
        formConfig: [
            {
                id: 'approvingEmployee',
                type: 'select',
                label: 'translate_approving_employee',
                required: true,

                props: {
                    bindLabel: 'name',
                    items$: injector
                        .get(EmployeesService)
                        .getManagersForCurrentUser()
                        .pipe(
                            map(items => {
                                return items.map(x => ({
                                    id: x.employee.id,
                                    name: formatEmployeeName(x.employee),
                                    titles: x.titles,
                                }));
                            }),
                        ),
                },

                onValueChange: (value, group, fields) => {
                    const selectControl =
                        group.controls['approvingEmployeeTitleSelect'];
                    const selectField = fields['approvingEmployeeTitleSelect'];

                    const textControl =
                        group.controls['approvingEmployeeTitleText'];
                    const textField = fields['approvingEmployeeTitleText'];

                    selectControl?.setValue(undefined);
                    textControl?.setValue(undefined);

                    const employee = value as { titles: MultilingualString[] };
                    selectField.hide = !employee?.titles?.length;
                    textField.hide = !!employee?.titles?.length;

                    if (selectField.hide) return;

                    (selectField as DynamicFormSelectConfig).props = {
                        items: employee.titles,
                    };
                },
            },

            {
                id: 'approvingEmployeeTitleSelect',
                type: 'select',
                label: 'translate_sender_title',
                hide: true,
            },

            {
                id: 'approvingEmployeeTitleText',
                type: 'multilingualTextInput',
                label: 'translate_sender_title',
                hide: true,
            },

            {
                id: 'notes',
                type: 'textarea',
                label: 'translate_notes',
            },
        ],
        creator: item => messagesService.sendForApproval(messageId, item),
        onCreated: onSent,
        onSubmitting: item => {
            (item as unknown as Record<string, MultilingualString | undefined>)[
                'approvingEmployeeTitle'
            ] =
                item['approvingEmployeeTitleSelect'] ??
                item['approvingEmployeeTitleText'];
            delete item['approvingEmployeeTitleSelect'];
            delete item['approvingEmployeeTitleText'];
            return item;
        },
        submitButtonLabels: {
            createLabel: 'translate_send_for_approval',
        },
    };
};
