import { MessageCategoriesService } from '../../message-categories.service';
import { DetailPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { MessageCategory } from '../../../core';

export const config = (
    messageCategoriesService: MessageCategoriesService,
): DetailPageConfig<MessageCategory> => {
    return {
        loader: messageCategoriesService.get.bind(messageCategoriesService),
        config: item => ({
            title: 'translate_message_categories',
            subtitle: 'translate_message_category_details',
            editButtonConfig: {
                permissionId: p.messaging.categories.write,
            },
            sectionConfigs: [
                {
                    title: 'translate_message_category_details',
                    type: 'detail',
                    fieldConfigFn: () => [
                        {
                            label: 'translate_name',
                        },

                        {
                            label: 'translate_description',
                        },
                    ],
                    valueConfigFn: () => [
                        {
                            value: item?.name,
                        },

                        {
                            value: item?.description,
                        },
                    ],
                },
            ],
        }),
    };
};
