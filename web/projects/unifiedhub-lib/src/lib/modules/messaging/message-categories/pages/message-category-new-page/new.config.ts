import { MessageCategoriesService } from '../../message-categories.service';
import { MessageCategory } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    messageCategoriesService: MessageCategoriesService,
): NewPageConfigFn<MessageCategory> => {
    return mode => ({
        title: 'translate_message_categories',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_message_category'
                : 'translate_update_message_category_details',
        newFormConfig: {
            loader: messageCategoriesService.get.bind(messageCategoriesService),
            creator: messageCategoriesService.create.bind(
                messageCategoriesService,
            ),
            editor: messageCategoriesService.update.bind(
                messageCategoriesService,
            ),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                        },
                    ],
                },

                {
                    id: 'description',
                    type: 'multilingualTextInput',
                    label: 'translate_description',
                    props: {
                        type: 'long',
                    },
                },
            ],
        },
    });
};
