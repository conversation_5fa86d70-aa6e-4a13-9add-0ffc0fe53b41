import { MessageCategoriesService } from '../../message-categories.service';
import { DynamicListPageConfig } from '../../../../../features';
import { p } from '../../../../../permissions';
import { MessageCategory } from '../../../core';

export const config = (
    messageCategoriesService: MessageCategoriesService,
): DynamicListPageConfig<MessageCategory, { keyword?: string }> => {
    return {
        title: 'translate_message_categories',
        listConfig: {
            columnConfigFn: () => [
                {
                    label: 'translate_name',
                },
            ],

            valueConfigFn: item => [
                {
                    value: item.name,
                    linkConfig: {
                        value: ['', 'messaging', 'categories', item.id],
                    },
                },
            ],

            rowConfigFn: () => ({
                editButtonConfig: {
                    permissionId: p.messaging.categories.write,
                },
                deleteButtonConfig: {
                    permissionId: p.messaging.categories.delete,
                },
            }),

            filter: [
                {
                    id: 'keyword',
                    type: 'text',
                    label: 'translate_search_by_name',
                },
            ],
            fetcher: messageCategoriesService.list.bind(
                messageCategoriesService,
            ),
            deleter: messageCategoriesService.delete.bind(
                messageCategoriesService,
            ),
        },

        newButtonConfig: {
            permissionId: p.messaging.categories.write,
        },
    };
};
