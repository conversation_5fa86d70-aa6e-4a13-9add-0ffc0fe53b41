import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MessageCategoriesService } from './message-categories.service';

@Component({
    selector: 'lib-message-categories',
    template: '<router-outlet/>',
    standalone: true,
    providers: [MessageCategoriesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MessageCategoriesComponent {}
