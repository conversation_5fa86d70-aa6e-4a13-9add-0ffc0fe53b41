import { Employee } from '../../../employment/core';
import { MessageCategory } from './message-category.type';
import { File, MultilingualString } from '../../../../common';
import { Department } from '../../../departments/core';
import { MessagingGroup } from './messaging-group.type';

export type Message = {
    id: string;
    createdTime: Date;
    type: 'message' | 'circular' | 'directive' | 'order';
    sendingEmployee: Employee;
    approvingEmployee?: Employee;
    sendingDepartment?: Department;
    referenceNumber?: string;
    subject?: string;
    category?: MessageCategory;
    confidentialityLevel: string;
    importanceLevel: string;
    daysToReply?: number;
    sentTime?: Date;
    readTime?: Date;
    trashedTime?: Date;
    pinnedTime?: Date;
    archivedTime?: Date;
    attachmentCount: number;
    sampleRecipientEmployees: Employee[];
    recipientEmployeesCount: number;
    sampleCarbonCopyEmployees: Employee[];
    carbonCopyEmployeesCount: number;
    recipientGroups: MessagingGroup[];
    carbonCopyGroups: MessagingGroup[];
    relevantEmployees: Employee[];
    body: string;
    isStampShown: boolean;
    senderName?: MultilingualString;
    senderTitle?: MultilingualString;
    attachments: File[];
    state: 'draft' | 'returned' | 'submitted' | 'approved';
};
