import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MessageTemplatesService } from './message-templates.service';

@Component({
    selector: 'lib-message-templates',
    template: '<router-outlet/>',
    standalone: true,
    providers: [MessageTemplatesService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MessageTemplatesComponent {}
