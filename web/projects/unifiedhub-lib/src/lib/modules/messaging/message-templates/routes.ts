import { Routes } from '@angular/router';
import { MessageTemplatesComponent } from './message-templates.component';
import { MessageTemplateListPageComponent } from './pages/message-template-list-page/message-template-list-page.component';
import { MessageTemplateNewPageComponent } from './pages/message-template-new-page/message-template-new-page.component';
import { MessageTemplateDetailComponent } from './pages/message-template-detail-page/message-template-detail.component';

export const routes = [
    {
        path: '',
        component: MessageTemplatesComponent,
        children: [
            {
                path: '',
                component: MessageTemplateListPageComponent,
                data: {
                    title: 'translate_message_templates',
                },
            },

            {
                path: 'new',
                component: MessageTemplateNewPageComponent,
                data: {
                    title: 'translate_add_new_message_template',
                },
            },

            {
                path: 'edit/:id',
                component: MessageTemplateNewPageComponent,
                data: {
                    title: 'translate_update_message_template_details',
                },
            },

            {
                path: ':id',
                component: MessageTemplateDetailComponent,
                data: {
                    title: 'translate_message_template_details',
                },
            },
        ],
    },
] satisfies Routes;
