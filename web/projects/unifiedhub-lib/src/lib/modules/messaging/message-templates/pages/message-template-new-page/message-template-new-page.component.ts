import { ChangeDetectionStrategy, Component } from '@angular/core';
import { configFn } from './new.config';
import { DynamicNewPageComponent } from '../../../../../features';
import { MessageTemplatesService } from '../../message-templates.service';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
    selector: 'lib-message-template-new-page',
    templateUrl: './message-template-new-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DynamicNewPageComponent,
        DynamicNewPageComponent,
        ReactiveFormsModule,
    ],
})
export class MessageTemplateNewPageComponent {
    protected readonly configFn = configFn(this.messageTemplatesService);

    public constructor(
        private readonly messageTemplatesService: MessageTemplatesService,
    ) {}
}
