import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MessagingGroup } from '../core';
import { CrudService } from '../../../features';
import { APP_CONFIG, AppConfig } from '../../../config';
import {
    Filter,
    mapFilterToQueryParams,
    PaginatedResult,
} from '../../../common';
import { Observable } from 'rxjs';
import { Employee } from '../../employment/core';

@Injectable()
export class MessagingGroupsService extends CrudService<
    MessagingGroup,
    { keyword?: string; classificationIds?: string[] }
> {
    public constructor(
        @Inject(APP_CONFIG) private readonly config: AppConfig,
        httpClient: HttpClient,
    ) {
        super(httpClient);
    }

    public getEmployeeListAssignedToGroup(
        id: string,
        filter: Filter<unknown>,
    ): Observable<PaginatedResult<Employee>> {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<Employee>>(
            `${this.getBaseEndpoint()}/${id}/employees`,
            { params },
        );
    }

    public getEmployeeListNotAssignedToGroup(
        id: string,
        filter: Filter<unknown>,
    ): Observable<PaginatedResult<Employee>> {
        const params = mapFilterToQueryParams(filter);
        return this.httpClient.get<PaginatedResult<Employee>>(
            `${this.getBaseEndpoint()}/${id}/employees/others`,
            { params },
        );
    }

    public addEmployeesToGroup(
        id: string,
        employeeIds: string[],
        filter: unknown | undefined,
    ): Observable<any> {
        return this.httpClient.post(
            `${this.getBaseEndpoint()}/${id}/employees/add`,
            { employeeIds, byFilter: !!filter, ...(filter ? filter : {}) },
        );
    }

    public removeEmployeesFromGroup(
        id: string,
        employeeIds: string[],
        filter: unknown | undefined,
    ): Observable<any> {
        return this.httpClient.post(
            `${this.getBaseEndpoint()}/${id}/employees/remove`,
            { employeeIds, byFilter: !!filter, ...(filter ? filter : {}) },
        );
    }

    protected override getBaseEndpoint(): string {
        return `${this.config.apiUrl}/messaging/groups`;
    }
}
