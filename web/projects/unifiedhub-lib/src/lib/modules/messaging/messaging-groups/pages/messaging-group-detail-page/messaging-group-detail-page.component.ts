import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Injector,
} from '@angular/core';
import { config } from './detail.config';
import { DynamicDetailPageComponent } from '../../../../../features';
import { DepartmentsService } from '../../../../departments/departments.service';
import { JobTitlesService } from '../../../../employment/job-titles/job-titles.service';
import { JobClassificationsService } from '../../../../employment/job-classifications/job-classifications.service';
import { MiscService } from '../../../../employment/employees/misc.service';
import { JobLevelsService } from '../../../../employment/job-levels/job-levels.service';

@Component({
    selector: 'lib-messaging-group-detail-page',
    templateUrl: './messaging-group-detail-page.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        DepartmentsService,
        JobTitlesService,
        JobClassificationsService,
        JobLevelsService,
        MiscService,
    ],
    imports: [DynamicDetailPageComponent],
})
export class MessagingGroupDetailPageComponent {
    protected readonly config = config(inject(Injector));
}
