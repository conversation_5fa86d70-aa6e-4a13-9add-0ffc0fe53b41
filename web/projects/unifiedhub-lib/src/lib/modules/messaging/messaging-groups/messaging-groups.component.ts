import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MessagingGroupsService } from './messaging-groups.service';

@Component({
    selector: 'lib-messaging-groups',
    template: '<router-outlet/>',
    standalone: true,
    providers: [MessagingGroupsService],
    imports: [RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MessagingGroupsComponent {}
