import { MessagingGroupsService } from '../../messaging-groups.service';
import { MessagingGroup } from '../../../core';
import { NewPageConfigFn } from '../../../../../features';

export const configFn = (
    messagingGroupsService: MessagingGroupsService,
): NewPageConfigFn<MessagingGroup> => {
    return mode => ({
        title: 'translate_messaging_groups',
        subtitle:
            mode === 'new'
                ? 'translate_add_new_messaging_group'
                : 'translate_update_messaging_group_details',
        newFormConfig: {
            loader: messagingGroupsService.get.bind(messagingGroupsService),
            creator: messagingGroupsService.create.bind(messagingGroupsService),
            editor: messagingGroupsService.update.bind(messagingGroupsService),
            formConfig: [
                {
                    type: 'group',
                    config: [
                        {
                            id: 'name',
                            type: 'multilingualTextInput',
                            label: 'translate_name',
                            required: true,
                        },
                    ],
                },

                {
                    type: 'group',
                    config: [
                        {
                            id: 'description',
                            type: 'multilingualTextInput',
                            label: 'translate_description',
                            props: {
                                type: 'long',
                            },
                        },
                    ],
                },
            ],
        },
    });
};
