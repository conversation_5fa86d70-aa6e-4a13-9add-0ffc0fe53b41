import { inject } from '@angular/core';
import {
    HttpContextToken,
    HttpErrorResponse,
    HttpEvent,
    HttpHandlerFn,
    HttpInterceptorFn,
    HttpRequest,
} from '@angular/common/http';
import { catchError, from, Observable, switchMap, throwError } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { parseErrorFromResponse } from '../../common';
import { AuthService, ToastService } from '../services';
import { Router } from '@angular/router';

// eslint-disable-next-line @typescript-eslint/naming-convention
export const ERROR_INTERCEPTOR_STEALTH = new HttpContextToken(() => false);

export const errorInterceptor: HttpInterceptorFn = (
    req: HttpRequest<unknown>,
    next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
    if (req.context.get(ERROR_INTERCEPTOR_STEALTH)) {
        return next(req);
    }

    const toastService = inject(ToastService);
    const translateService = inject(TranslateService);
    const router = inject(Router);
    const authService = inject(AuthService);

    return next(req).pipe(
        catchError((e: HttpErrorResponse) => {
            return from(parseErrorFromResponse(e)).pipe(
                switchMap(error => {
                    if (!error) {
                        translateService
                            .get('translate_an_unknown_error_has_occurred')
                            .subscribe(str => {
                                toastService.danger(str);
                            });
                        return throwError(() => e);
                    }

                    if (error.type?.endsWith('required-password-change')) {
                        router.navigate(['', 'account']).then();
                    } else if (error.type?.endsWith('user-not-active')) {
                        authService.logout();
                        toastService.danger(error!.title, error!.detail);
                    } else {
                        toastService.danger(error!.title, error!.detail);
                    }

                    return throwError(() => e);
                }),
            );
        }),
    );
};
