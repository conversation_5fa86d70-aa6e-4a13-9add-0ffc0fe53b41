import {
    HttpContextToken,
    HttpEvent,
    HttpHandlerFn,
    HttpInterceptorFn,
    HttpRequest,
} from '@angular/common/http';
import { finalize, Observable, Subject, takeUntil } from 'rxjs';
import { inject } from '@angular/core';
import { NavigationStart, Router } from '@angular/router'; // eslint-disable-next-line @typescript-eslint/naming-convention

// eslint-disable-next-line @typescript-eslint/naming-convention
export const CANCEL_ON_NAVIGATION_SUSTAIN = new HttpContextToken(() => false);

// Used when we want to allow certain routes not to
// cancel ongoing requests.
// eslint-disable-next-line @typescript-eslint/naming-convention
export const CANCEL_ON_NAVIGATION_IGNORE = 'ignore';

export const cancelOnNavigationInterceptor: HttpInterceptorFn = (
    req: HttpRequest<unknown>,
    next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
    const router = inject(Router);
    const unsubscribeAll$ = new Subject<void>();
    const cancelRequestSubject$ = new Subject<void>();
    const isSustain = req.context.get(CANCEL_ON_NAVIGATION_SUSTAIN);

    router.events.pipe(takeUntil(unsubscribeAll$)).subscribe(event => {
        if (event instanceof NavigationStart) {
            const state = router.getCurrentNavigation()?.extras.state;

            // If the navigation is marked to allow current
            // requests to continue, then skip cancellation.
            if ((state && state[CANCEL_ON_NAVIGATION_IGNORE]) || isSustain) {
                return;
            } else {
                cancelRequestSubject$.next();
            }
        }
    });

    return next(req).pipe(
        finalize(() => {
            cancelRequestSubject$.complete();
            unsubscribeAll$.next();
            unsubscribeAll$.complete();
        }),
        takeUntil(cancelRequestSubject$),
    );
};
