import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, forkJoin, Observable } from 'rxjs';
import { MultilingualString } from '../../common';
import { MultilingualStringTranslatorService } from './multilingual-string-translator.service';

@Injectable()
export class TitleService {
    private readonly value = new BehaviorSubject<string | MultilingualString>(
        '',
    );

    public constructor(
        private readonly title: Title,
        private readonly translateService: TranslateService,
        private readonly multilingualStringTranslatorService: MultilingualStringTranslatorService,
    ) {}

    public get value$(): Observable<string | MultilingualString> {
        return this.value.asObservable();
    }

    public set(title: string | MultilingualString): void {
        this.value.next(title);

        title = this.multilingualStringTranslatorService.get(
            title as MultilingualString,
        )!;

        forkJoin([
            this.translateService.get('translate_app_name'),
            this.translateService.get(title),
        ]).subscribe(([appNameStr, titleStr]) => {
            this.title.setTitle(`${titleStr} - ${appNameStr}`);
        });
    }
}
