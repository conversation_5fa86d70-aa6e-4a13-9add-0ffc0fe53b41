import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MultilingualString } from '../../common';

@Injectable()
export class MultilingualStringTranslatorService {
    public constructor(private readonly translateService: TranslateService) {}

    public get(value?: MultilingualString): string {
        if (
            !value ||
            !this.translateService.langs.some(lang =>
                Object.keys(value).includes(lang),
            )
        ) {
            return value as any;
        }

        if (this.translateService.currentLang === 'ar') {
            return value?.ar;
        }

        return value?.en;
    }
}
