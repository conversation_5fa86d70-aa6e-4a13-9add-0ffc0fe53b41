import { Inject, Injectable } from '@angular/core';
import { AuthService } from './auth';
import { BehaviorSubject, filter, map, Observable } from 'rxjs';
import { HttpClient, HttpContext } from '@angular/common/http';
import { APP_CONFIG, AppConfig } from '../../config';
import { CANCEL_ON_NAVIGATION_SUSTAIN } from '../interceptors';

@Injectable()
export class RbacService {
    private permissions = new BehaviorSubject<string[] | undefined>(undefined);

    public constructor(
        private readonly authService: AuthService,
        private readonly httpClient: HttpClient,
        @Inject(APP_CONFIG) private readonly config: AppConfig,
    ) {
        this.monitorAuthStatus();
    }

    public get permissions$(): Observable<string[]> {
        return this.permissions.pipe(filter((x): x is string[] => !!x));
    }

    public evaluate(permissionId: string): Observable<boolean> {
        return this.permissions$.pipe(
            map(
                permissions =>
                    !permissionId ||
                    (permissions.includes(permissionId) ?? false),
            ),
        );
    }

    private monitorAuthStatus(): void {
        this.authService.identity$.subscribe(identity => {
            if (!identity) {
                this.permissions.next([]);
                return;
            }

            this.httpClient
                .get<string[]>(`${this.config.apiUrl}/permissions/current`, {
                    context: new HttpContext().set(
                        CANCEL_ON_NAVIGATION_SUSTAIN,
                        true,
                    ),
                })
                .subscribe(permissions => {
                    this.permissions.next(permissions);
                });
        });
    }
}
