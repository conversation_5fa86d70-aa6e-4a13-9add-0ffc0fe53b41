import {
    ActivatedRouteSnapshot,
    CanActivateChildFn,
    CanActivateFn,
    GuardResult,
    MaybeAsync,
    Router,
    RouterStateSnapshot,
} from '@angular/router';
import { inject } from '@angular/core';
import { RbacService } from '../services';
import { map, tap, zip } from 'rxjs';

const canActivate: CanActivateFn = (
    route: ActivatedRouteSnapshot,
): MaybeAsync<boolean> => {
    const { permissionIds } = route.data as { permissionIds?: string[] };
    if (!permissionIds || !permissionIds.length) return true;

    const rbacService = inject(RbacService);
    const router = inject(Router);

    return zip(permissionIds.map(x => rbacService.evaluate(x))).pipe(
        map(results => {
            return results.some(r => r);
        }),
        tap(async isAllowed => {
            if (!isAllowed) {
                await router.navigate(['']);
            }
        }),
    );
};

const canActivateChild: CanActivateChildFn = (
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
): MaybeAsync<GuardResult> => canActivate(childRoute, state);

export const rbacGuard = { canActivate, canActivateChild };
