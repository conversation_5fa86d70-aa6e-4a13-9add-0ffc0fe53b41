import { Pipe, PipeTransform } from '@angular/core';
import { MultilingualString } from '../../common';
import { MultilingualStringTranslatorService } from '../services';

@Pipe({
    name: 'translateMultilingualString',
    standalone: true,
})
export class TranslateMultilingualStringPipe implements PipeTransform {
    public constructor(
        private readonly multilingualStringTranslator: MultilingualStringTranslatorService,
    ) {}

    public transform(value?: MultilingualString): string {
        return this.multilingualStringTranslator.get(value);
    }
}
