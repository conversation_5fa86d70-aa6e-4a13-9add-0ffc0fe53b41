export const p = {
    foundation: {
        write: 'foundation:write',
    },

    attendance: {
        punches: {
            read: 'attendance:punches:read',
        },
        schedules: {
            read: 'attendance:schedules:read',
            write: 'attendance:schedules:write',
            delete: 'attendance:schedules:delete',
        },
        transactions: {
            read: 'attendance:transactions:read',
        },
        zones: {
            read: 'attendance:zones:read',
            write: 'attendance:zones:write',
            delete: 'attendance:zones:delete',
        },
        permissions: {
            read: 'attendance:permissions:read',
            write: 'attendance:permissions:write',
            delete: 'attendance:permissions:delete',
        },
    },

    employees: {
        read: 'employees:read',
        readSensitive: 'employees:read_sensitive',
        write: 'employees:write',
        delete: 'employees:delete',
        approve: 'employees:approve',
        manageLists: 'employees:manage_lists',
        export: 'employees:export',

        children: {
            write: 'employees:children:write',
            delete: 'employees:children:delete',
        },

        terminations: {
            read: 'employees:terminations:read',
            write: 'employees:terminations:write',
            delete: 'employees:terminations:delete',
        },

        jobLevelChanges: {
            read: 'employees:job_level_changes:read',
            write: 'employees:job_level_changes:write',
            delete: 'employees:job_level_changes:delete',
        },
    },

    payroll: {
        salaries: {
            read: 'payroll:salaries:read',
            write: 'payroll:salaries:write',
            delete: 'payroll:salaries:delete',
        },

        registers: {
            read: 'payroll:registers:read',
            delete: 'payroll:registers:delete',
            generate: 'payroll:registers:generate',
            approve: 'payroll:registers:approve',
            finalize: 'payroll:registers:finalize',
        },
    },

    departments: {
        read: 'departments:read',
        write: 'departments:write',
        delete: 'departments:delete',
    },

    users: {
        read: 'users:read',
        write: 'users:write',
        delete: 'users:delete',
    },

    roles: {
        read: 'roles:read',
        write: 'roles:write',
        delete: 'roles:delete',
    },

    servicing: {
        requests: {
            read: 'servicing:requests:read',
            write: 'servicing:requests:write',
        },
    },

    leaves: {
        read: 'leaves:read',
        write: 'leaves:write',
        delete: 'leaves:delete',
        finalApproval: 'leaves:final_approval',
        medicalApproval: 'leaves:medical_approval',
    },

    messaging: {
        messages: {
            sendCirculars: 'messaging:messages:send_circulars',
            sendDirectives: 'messaging:messages:send_directives',
            sendOrders: 'messaging:messages:send_orders',
            approve: 'messaging:messages:approve_test',
        },
        categories: {
            read: 'messaging:categories:read',
            write: 'messaging:categories:write',
            delete: 'messaging:categories:delete',
        },
    },

    courses: {
        read: 'courses:read',
        write: 'courses:write',
        delete: 'courses:delete',
        manageLists: 'courses:manage_lists',

        employees: {
            read: 'courses:employees:read',
            write: 'courses:employees:write',
            delete: 'courses:employees:delete',
        },
    },

    suggestions: {
        assess: 'suggestions:assess',
        departmentAssess: 'suggestions:department_assess',
        manageLists: 'suggestions:manage_lists',
    },

    surveys: {
        read: 'surveys:read',
        write: 'surveys:write',
        delete: 'surveys:delete',
    },

    recruitment: {
        applicants: {
            read: 'recruitment:applicants:read',
        },
        applications: {
            read: 'recruitment:applications:read',
        },
        vacancies: {
            read: 'recruitment:vacancies:read',
            write: 'recruitment:vacancies:write',
            delete: 'recruitment:vacancies:delete',
        },
    },

    logging: {
        logs: {
            read: 'logging:logs:read',
        },
    },
};
