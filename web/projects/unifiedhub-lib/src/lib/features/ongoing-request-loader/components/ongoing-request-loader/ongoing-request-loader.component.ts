import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { AsyncPipe, NgStyle } from '@angular/common';
import { OngoingRequestLoaderService } from '../../ongoing-request-loader.service';

@Component({
    selector: 'lib-ongoing-request-loader',
    templateUrl: 'ongoing-request-loader.component.html',
    styleUrl: './ongoing-request-loader.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgStyle, AsyncPipe],
})
export class OngoingRequestLoaderComponent {
    protected ongoingRequestLoaderService = inject(OngoingRequestLoaderService);
}
