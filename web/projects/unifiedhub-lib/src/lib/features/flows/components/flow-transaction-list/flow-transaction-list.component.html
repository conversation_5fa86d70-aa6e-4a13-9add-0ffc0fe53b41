<lib-content-container
    contentTitle="{{ 'translate_transactions_history' | translate }}"
>
    <ng-container tools>
        <button (click)="reload()" class="btn btn-sm btn-primary">
            <i class="fa fa-refresh"></i>
        </button>
    </ng-container>

    <ul
        *libWaitUntilListLoaded="transactions()"
        content
        class="flex max-h-96 flex-col gap-2 overflow-y-auto"
    >
        @for (transaction of transactions(); track transaction.id) {
            <li
                class="flex flex-row items-start gap-2 rounded bg-white p-2 text-sm text-primary-700 shadow"
            >
                <!-- Icon -->
                <i class="fa fa-arrow-right-arrow-left text-green-500"></i>

                <div class="flex grow flex-col gap-2 md:flex-row">
                    <div class="flex flex-col gap-2 md:grow">
                        <!-- State from to -->
                        <div class="flex flex-row items-center gap-2">
                            <span>
                                {{
                                    'translate_item_state_has_been_changed_to'
                                        | translate
                                }}
                            </span>

                            <span
                                class="text-nowrap rounded bg-green-100 px-2 py-0.5 text-xs font-bold text-green-900"
                            >
                                {{
                                    'translate_flow_state_' +
                                        transaction.toState | translate
                                }}
                            </span>

                            @if (transaction.fromState) {
                                <span>{{
                                    'translate_from_state' | translate
                                }}</span>

                                <span
                                    class="text-nowrap rounded bg-primary-100 px-1 py-0.5 text-xs font-bold text-primary-900"
                                >
                                    {{
                                        'translate_flow_state_' +
                                            transaction.fromState | translate
                                    }}
                                </span>
                            }

                            @if (transaction.action) {
                                <span>
                                    {{ 'translate_via_the_action' | translate }}
                                </span>

                                <span
                                    class="text-nowrap rounded bg-yellow-100 px-1 py-0.5 text-xs font-bold text-yellow-800"
                                >
                                    {{
                                        'translate_flow_action_' +
                                            transaction.action | translate
                                    }}
                                </span>
                            }
                        </div>

                        <!-- User and time -->
                        <div
                            class="flex flex-row gap-2 text-xs text-primary-500"
                        >
                            <span>
                                {{ 'translate_by' | translate }}
                            </span>

                            <span>
                                {{
                                    transaction.user.name
                                        | translateMultilingualString
                                }}
                            </span>

                            <span>
                                {{ 'translate_at' | translate }}
                            </span>

                            <span>
                                {{
                                    transaction.createdTime
                                        | date: 'yyyy-MM-dd hh:mm a'
                                }}
                            </span>
                        </div>

                        <!-- Notes -->
                        @if (transaction.notes) {
                            <div>
                                {{ transaction.notes }}
                            </div>
                        }
                    </div>
                </div>
            </li>
        }
    </ul>
</lib-content-container>
