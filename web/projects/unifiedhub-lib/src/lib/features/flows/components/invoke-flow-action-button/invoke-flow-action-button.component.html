@if (flowEntity().flowTransitions.length) {
    @switch (flowButtonStyle()) {
        @case ('custom') {
            <ng-container
                [ngTemplateOutlet]="flowButtonTemplate()!"
                [ngTemplateOutletContext]="{ invoke: invoke.bind(this) }"
            />
        }
        @case ('list') {
            <button (click)="invoke()" class="btn btn-sm btn-primary">
                <i class="fa fa-paper-plane"></i>
            </button>
        }
        @case ('detail') {
            <button
                (click)="invoke()"
                class="btn btn-lg btn-primary flex flex-row items-center gap-2"
            >
                <span>
                    {{ 'translate_update_state' | translate }}
                </span>
                <i class="fa fa-paper-plane"></i>
            </button>
        }
    }
}
