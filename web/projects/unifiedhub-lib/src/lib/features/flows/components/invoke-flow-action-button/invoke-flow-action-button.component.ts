import {
    ChangeDetectionStrategy,
    Component,
    HostBinding,
    Injector,
    input,
    output,
    OutputRefSubscription,
    TemplateRef,
} from '@angular/core';
import { ModalService } from '../../../modal';
import { InvokeFlowActionComponent } from '../invoke-flow-action/invoke-flow-action.component';
import { NgTemplateOutlet } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FlowEntity } from '../../types/flow-entity.type';

@Component({
    selector: 'lib-invoke-flow-action-button',
    templateUrl: 'invoke-flow-action-button.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgTemplateOutlet, TranslateModule],
})
export class InvokeFlowActionButtonComponent {
    public readonly flowEntityPath = input.required<string>();
    public readonly flowEntity = input.required<FlowEntity>();
    public readonly flowButtonStyle = input<'custom' | 'list' | 'detail'>(
        'custom',
    );
    public readonly flowButtonTemplate = input<TemplateRef<unknown>>();

    public readonly invoked = output<void>();

    public constructor(
        private readonly modalService: ModalService,
        private readonly injector: Injector,
    ) {}

    @HostBinding('class.hidden')
    protected get className(): boolean {
        return this.flowEntity().flowTransitions.length === 0;
    }

    protected async invoke(): Promise<void> {
        let subscription: OutputRefSubscription | undefined;

        const component = await this.modalService.show(
            InvokeFlowActionComponent,
            {
                title: 'translate_update_state',
                injector: this.injector,
                inputs: {
                    entityPath: this.flowEntityPath(),
                    entity: this.flowEntity(),
                },
                onDismiss: () => {
                    subscription?.unsubscribe();
                },
            },
        );

        subscription = component.invoked.subscribe(() => {
            this.modalService.dismiss(component);
            this.invoked.emit();
        });
    }
}
