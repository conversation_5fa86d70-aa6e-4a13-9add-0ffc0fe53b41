import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    output,
} from '@angular/core';
import { FlowEntity } from '../../types/flow-entity.type';
import { DynamicNewFormComponent } from '../../../dynamic';
import { config } from './new.config';
import { FlowsService } from '../../services/flows.service';

@Component({
    selector: 'lib-invoke-action',
    templateUrl: 'invoke-flow-action.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [DynamicNewFormComponent],
})
export class InvokeFlowActionComponent {
    public readonly entityPath = input.required<string>();
    public readonly entity = input.required<FlowEntity>();

    public readonly invoked = output<void>();

    protected readonly config = computed(() =>
        config(
            this.entity(),
            this.entityPath(),
            this.flowsService,
            this.invoked,
        ),
    );

    public constructor(private readonly flowsService: FlowsService) {}
}
