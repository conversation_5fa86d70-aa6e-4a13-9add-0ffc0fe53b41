import { finalize, Observable, Subject } from 'rxjs';
import { FileViewerComponent } from '../../ui';
import { File, saveBlobToFileUtil } from '../../common';
import { MultilingualStringTranslatorService } from '../../core';
import { ModalService } from '../modal';

export const downloadOrPreviewFile = (
    file: File,
    downloader: () => Observable<Blob>,
    modalService: ModalService,
    multilingualStringTranslatorService: MultilingualStringTranslatorService,
    onDownload?: () => void,
): void => {
    downloader()
        .pipe(
            finalize(() => {
                onDownload?.();
            }),
        )
        .subscribe(async blob => {
            const fileName =
                multilingualStringTranslatorService.get(file.name) ??
                'Untitled';

            if (
                file.contentType === 'application/pdf' ||
                file.contentType?.split('/')[0] === 'image'
            ) {
                const src = URL.createObjectURL(blob);
                let subject = new Subject();
                await modalService.show(FileViewerComponent, {
                    title: fileName,
                    onDismiss: () => {
                        window.URL.revokeObjectURL(src);
                        subject.next(undefined);
                        subject.complete();
                    },
                    inputs: {
                        fileSrc: src,
                    },
                    size: {
                        width: '100%',
                        height: '100%',
                    },
                });
            } else {
                saveBlobToFileUtil(blob, fileName);
            }
        });
};
