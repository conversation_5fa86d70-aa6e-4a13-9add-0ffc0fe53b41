import {
    ChangeDetectionStrategy,
    Component,
    input,
    output,
    signal,
} from '@angular/core';
import { Toast } from '../../../../core';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'lib-toast',
    templateUrl: './toast.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, TranslateModule],
})
export class ToastComponent {
    public toast = input.required<Toast>();
    public toastEnter = output();
    public toastLeave = output();

    protected hovering = signal<boolean>(false);
}
