<lib-select-input
    [items]="$any(field.props).items"
    [items$]="$any(field.props).items$"
    [loaderFetcher]="$any(field.props).loaderFetcher"
    [bindLabel]="$any(field.props).bindLabel"
    [bindLabelSecond]="$any(field.props).bindLabelSecond"
    [bindLabelThird]="$any(field.props).bindLabelThird"
    [bindValue]="
        $any(field.props).bindValue ? $any(field.props).bindValue : null
    "
    [compareWith]="$any(field.props).compareWith"
    [loadOnOpen]="true"
    [formControl]="$any(formControl)"
    [formlyAttributes]="field"
    [isMulti]="$any(field.props).isMulti"
    [isNotClearable]="$any(field.props).isNotClearable"
    [isNotSearchable]="$any(field.props).isNotSearchable"
    [direction]="$any(field.props).direction"
    [canAddItem]="$any(field.props).canAddItem"
    [isSelectedItemEditable]="$any(field.props).isSelectedItemEditable"
/>
