import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FieldType, FormlyModule } from '@ngx-formly/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { SelectInputComponent } from '../../../../ui';

@Component({
    selector: 'lib-select-field-type',
    templateUrl: 'select-field-type.component.html',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormlyModule,
        NgSelectModule,
        SelectInputComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectFieldTypeComponent extends FieldType {}
