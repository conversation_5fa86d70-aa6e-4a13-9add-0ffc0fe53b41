import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FieldType, FormlyModule } from '@ngx-formly/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormlyFieldConfig } from '@ngx-formly/core/lib/models';
import { Observable } from 'rxjs';
import { FileInputComponent } from '../../../complex-ui';
import { DynamicFormFileConfig } from '../../../dynamic';
import { File } from '../../../../common';

@Component({
    selector: 'lib-file-input-field-type',
    templateUrl: 'file-input-field-type.component.html',
    standalone: true,
    imports: [ReactiveFormsModule, FormlyModule, FileInputComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileInputFieldTypeComponent extends FieldType {
    public getDownloader(): ((file: File) => Observable<Blob>) | undefined {
        const fileDownloader = (
            this.field.props as DynamicFormFileConfig['props']
        )?.fileDownloader;

        if (!fileDownloader) return undefined;

        const getFieldPath = (
            field?: FormlyFieldConfig,
            accumulated: string = '',
        ): string => {
            const separator =
                accumulated && !accumulated.startsWith('[') ? '.' : '';

            if (!field) return `$${separator}${accumulated}`;

            if (field.key && isNaN(parseInt(field.key as string))) {
                accumulated = `${field.key}${separator}${accumulated}`;
            }

            if (field.key && !isNaN(parseInt(field.key as string))) {
                accumulated = `[${field.key}]${separator}${accumulated}`;
            }

            return getFieldPath(field.parent, accumulated);
        };

        const fieldPath = getFieldPath(this.field);

        return file => fileDownloader(file, fieldPath);
    }
}
