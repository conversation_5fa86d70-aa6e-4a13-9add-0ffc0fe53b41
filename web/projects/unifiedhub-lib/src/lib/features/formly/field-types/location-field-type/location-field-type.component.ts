import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FieldType, FormlyModule } from '@ngx-formly/core';
import { ReactiveFormsModule } from '@angular/forms';
import { Ng<PERSON>lass, NgStyle } from '@angular/common';
import { MultilingualTextInputComponent } from '../../../../ui';
import { LocationInputComponent } from '../../../complex-ui';

@Component({
    selector: 'lib-location-field-type',
    templateUrl: 'location-field-type.component.html',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormlyModule,
        NgClass,
        NgStyle,
        LocationInputComponent,
        MultilingualTextInputComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LocationFieldTypeComponent extends FieldType {}
