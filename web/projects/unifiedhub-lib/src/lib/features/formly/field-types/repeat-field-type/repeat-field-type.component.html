<div
    class="flex w-full flex-col gap-8 border border-primary-300 bg-primary-50 p-2 md:p-6"
    [ngClass]="{
        'border-red-500 bg-red-50': showError
    }"
>
    @for (f of field.fieldGroup; track f.key; let idx = $index) {
        <div
            class="flex flex-row items-start gap-4 border border-dashed border-primary-300 p-2 md:p-4"
            [ngClass]="{ 'border-red-500': showError }"
        >
            <formly-field class="grow" [field]="f"></formly-field>

            @if (!field.formControl.disabled && !field.form?.disabled) {
                <button
                    class="btn btn-sm btn-outline-danger shrink-0"
                    type="button"
                    (click)="remove(idx)"
                >
                    <i class="fa fa-trash"></i>
                </button>
            }
        </div>
    }

    <!-- For some reason, using `field.formControl.disabled` does not work when the list of items is empty. -->
    @if (
        !field.props.disabled &&
        !field.formControl.disabled &&
        !field.form?.disabled &&
        !$any(field.props).isAdditionDisabled
    ) {
        <button
            class="btn btn-outline-primary flex flex-row items-center gap-2 self-center"
            type="button"
            (click)="add()"
        >
            <i class="fa fa-plus"></i>
            <span>
                {{ 'translate_add' | translate }}
            </span>
        </button>
    }
</div>
