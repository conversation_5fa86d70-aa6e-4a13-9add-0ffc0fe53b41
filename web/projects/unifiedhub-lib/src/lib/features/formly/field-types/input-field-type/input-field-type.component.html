@if (field.props?.type === 'checkbox') {
    <input
        value=""
        type="checkbox"
        [formControl]="$any(formControl)"
        [formlyAttributes]="field"
    />
} @else if (field.props?.type === 'number') {
    <lib-number-input
        class="w-full"
        [formControl]="$any(formControl)"
        [formlyAttributes]="field"
    />
} @else {
    <input
        class="w-full"
        type="{{ field.props?.type ?? 'text' }}"
        [autocomplete]="field.props?.['isAutocompleteOff'] ? 'off' : 'on'"
        [formControl]="$any(formControl)"
        [formlyAttributes]="field"
        [ngStyle]="
            $any(
                (field.props?.type === 'email' ||
                    field.props?.type === 'password') && { 'direction': 'ltr' }
            )
        "
    />
}
