<div class="flex flex-col gap-2">
    <label>
        <span>{{ field.props?.label ?? '' | translate }}</span>
        @if (field.props?.required) {
            <span class="text-rose-500">*</span>
        }
    </label>

    <div class="flex flex-col gap-1">
        <ng-container #fieldComponent></ng-container>

        @if (field.props?.['note']) {
            <div class="text-sm text-gray-500">
                {{ field.props?.['note'] | translate }}
            </div>
        }

        @if (showError) {
            <div class="text-xs text-red-500">
                <formly-validation-message
                    [field]="field"
                ></formly-validation-message>
            </div>
        }
    </div>
</div>
