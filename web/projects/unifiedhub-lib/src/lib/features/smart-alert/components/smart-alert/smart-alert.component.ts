import {
    ChangeDetectionStrategy,
    Component,
    input,
    output,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { SmartAlertAction } from '../../types';

@Component({
    selector: 'lib-smart-alert',
    templateUrl: './smart-alert.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, NgClass],
})
export class SmartAlertComponent {
    public readonly description = input<string>();
    public readonly actions = input.required<SmartAlertAction[]>();

    public readonly action = output<string>();
}
