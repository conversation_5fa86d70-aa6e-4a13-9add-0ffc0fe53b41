<div class="flex flex-col gap-4">
    @if (description()) {
        <p class="text-gray-500">{{ description()! | translate }}</p>
    }

    <div class="flex flex-row items-center justify-center gap-2">
        @for (item of actions(); track item.id) {
            <button
                (click)="action.emit(item.id)"
                class="btn"
                [ngClass]="{
                    'btn-primary': !item.type || item.type === 'primary',
                    'btn-success': item.type === 'success',
                    'btn-info': item.type === 'info',
                    'btn-warning': item.type === 'warning',
                    'btn-danger': item.type === 'danger'
                }"
            >
                {{ item.label | translate }}
            </button>
        }
    </div>
</div>
