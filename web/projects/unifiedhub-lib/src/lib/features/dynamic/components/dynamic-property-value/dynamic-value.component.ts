import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ValueConfig } from '../../types';
import {
    AsyncPipe,
    DatePipe,
    DecimalPipe,
    NgClass,
    NgComponentOutlet,
    NgTemplateOutlet,
} from '@angular/common';
import { RouterLink } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
    RbacDirective,
    TranslateMultilingualStringPipe,
} from '../../../../core';
import {
    IdToNamePipe,
    Location,
    RoundPipe,
    UpdatePropertyPipe,
} from '../../../../common';
import { ModalService } from '../../../modal';
import { LocationViewerComponent } from '../../../../ui';
import { AsMultiLineValueConfigPipe } from './pipes';
import { FileDownloaderComponent } from '../../../complex-ui';

@Component({
    selector: 'lib-dynamic-value',
    templateUrl: './dynamic-value.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        AsyncPipe,
        DatePipe,
        NgTemplateOutlet,
        RouterLink,
        TranslateModule,
        NgClass,
        TranslateMultilingualStringPipe,
        RbacDirective,
        IdToNamePipe,
        RoundPipe,
        AsMultiLineValueConfigPipe,
        DecimalPipe,
        FileDownloaderComponent,
        NgComponentOutlet,
        UpdatePropertyPipe,
    ],
})
export class DynamicValueComponent<T> {
    public config = input.required<ValueConfig>();
    public item = input.required<T>();

    public constructor(
        private readonly modalService: ModalService,
        private readonly translateService: TranslateService,
    ) {}

    protected async showLocationOnMap(location: Location): Promise<void> {
        this.translateService
            .get('translate_location_on_map')
            .subscribe(async title => {
                await this.modalService.show(LocationViewerComponent, {
                    title,
                    inputs: {
                        location,
                        height: '400px',
                    },
                });
            });
    }
}
