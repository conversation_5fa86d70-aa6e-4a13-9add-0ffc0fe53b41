import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    Injector,
    input,
    model,
    OnDestroy,
    output,
    signal,
    viewChild,
} from '@angular/core';
import {
    DynamicFormComponentConfig,
    DynamicFormConfig,
    DynamicFormCustomConfig,
    DynamicFormFieldConfig,
    DynamicFormRepeatConfig,
    DynamicFormSelectConfig,
} from '../../types';
import { FormlyFieldConfig, FormlyForm, FormlyModule } from '@ngx-formly/core';
import {
    FormGroup,
    ReactiveFormsModule,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    cacheObservable,
    MultilingualString,
    requiredMultilingualTextValidator,
} from '../../../../common';
import { MultilingualStringTranslatorService } from '../../../../core';
import { debounceTime } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ComponentFormControlWrapperComponent } from './components/component-form-control-wrapper/component-form-control-wrapper.component';
import { Observable } from 'rxjs';

@Component({
    selector: 'lib-dynamic-form',
    templateUrl: './dynamic-form.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ReactiveFormsModule,
        FormlyModule,
        NgClass,
        NgTemplateOutlet,
        ComponentFormControlWrapperComponent,
    ],
})
export class DynamicFormComponent<T> implements AfterViewInit, OnDestroy {
    public readonly item = model<T>();
    public readonly fields = model<Record<string, DynamicFormFieldConfig>>();
    public readonly config = input.required<DynamicFormConfig[]>();
    public readonly onValuesChange =
        input<
            (
                group: FormGroup,
                fields: Record<string, DynamicFormFieldConfig>,
            ) => void
        >();
    public readonly formGroupChange = output<UntypedFormGroup>();

    protected readonly formGroup = signal<UntypedFormGroup | undefined>(
        undefined,
    );

    protected readonly formlyFields = signal<FormlyFieldConfig[]>([]);

    protected readonly templateFields = computed<DynamicFormCustomConfig[]>(
        () => {
            if (!this.fields()) return [];

            const flattenFields = Object.values(this.fields()!);
            return flattenFields.filter(
                x => x.type === 'custom',
            ) as DynamicFormCustomConfig[];
        },
    );

    protected readonly componentFields = computed<DynamicFormComponentConfig[]>(
        () => {
            if (!this.fields()) return [];

            const flattenFields = Object.values(this.fields()!);
            return flattenFields.filter(
                x => x.type === 'component',
            ) as DynamicFormComponentConfig[];
        },
    );

    protected emptyFormGroup = new UntypedFormGroup({});

    private readonly flattenedFormlyFields = computed<
        Record<string, FormlyFieldConfig>
    >(() => this.flattenFormlyFields(this.formlyFields()));

    private readonly formlyComponent = viewChild<FormlyForm>('formlyComponent');
    private isDestroyed = false;

    private readonly cachedLazyLoadedSelectItems = new Map<
        string,
        Observable<unknown[]>
    >();

    public constructor(
        private readonly multilingualServiceTranslatorService: MultilingualStringTranslatorService,
        private readonly injector: Injector,
        private readonly destroyRef: DestroyRef,
    ) {}

    public ngAfterViewInit(): void {
        // XXX: we periodically check for the form since formly
        // does not officially provide a way to notify us when the
        // form group controls are built and set.
        const interval = setInterval(() => {
            if (this.isDestroyed) {
                clearInterval(interval);
                return;
            }
            const form = this.formlyComponent()?.form as UntypedFormGroup;
            if (Object.keys(form.controls).length > 0) {
                this.onControlsPopulated(form);
                clearInterval(interval);
            }
        });

        effect(
            () => {
                const config = this.config();
                this.fields.set(this.flattenFields(config));
                this.formlyFields.set([
                    {
                        fieldGroupClassName: 'flex flex-col gap-8',
                        fieldGroup: this.createFormlyFieldConfig(this.config()),
                    },
                ]);
            },
            { injector: this.injector, allowSignalWrites: true },
        );
    }

    public ngOnDestroy(): void {
        this.isDestroyed = true;
    }

    private onControlsPopulated(form: FormGroup): void {
        this.formGroup.set(form);
        this.formGroupChange.emit(form);

        if (this.onValuesChange()) {
            form.valueChanges
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => this.onValuesChange()!(form, this.fields()!));
        }
    }

    private flattenFields(
        config: DynamicFormConfig[],
    ): Record<string, DynamicFormFieldConfig> {
        const handlerFn: (id: string) => ProxyHandler<any> = id => {
            return {
                set: (target, property, value) => {
                    (target as any)[property] = value;

                    const formlyFields = this.flattenedFormlyFields();
                    const fields = this.fields();

                    if (!fields) return true;

                    this.mapFormlyFieldFromField(formlyFields[id], fields[id]);

                    return true;
                },
            };
        };

        const result: Record<string, DynamicFormFieldConfig> = {};

        const flatten = (c: DynamicFormConfig[]): void => {
            c.forEach(x => {
                if ((x.type === 'group' || x.type === 'section') && x.config) {
                    flatten(x.config);
                } else {
                    const field = x as DynamicFormFieldConfig;

                    this.cacheLazyLoadedSelectItems(field);

                    const handler = handlerFn(field.id!);
                    field.props = field.props
                        ? new Proxy(field.props, handler)
                        : undefined;
                    field.validators = field.validators
                        ? new Proxy(field.validators, handler)
                        : undefined;
                    result[field.id!] = new Proxy<DynamicFormFieldConfig>(
                        field,
                        handler,
                    );
                }
            });
        };

        flatten(config);

        return result;
    }

    private flattenFormlyFields(
        fields: FormlyFieldConfig[],
    ): Record<string, FormlyFieldConfig> {
        const result: Record<string, FormlyFieldConfig> = {};

        const flatten = (f: FormlyFieldConfig[]): void => {
            f.forEach(x => {
                if (!x.key && x.fieldGroup && x.fieldGroup.length > 0) {
                    flatten(x.fieldGroup);
                } else if (x.key) {
                    result[x.key as string] = x;
                }
            });
        };

        flatten(fields);

        return result;
    }

    private createFormlyFieldConfig(
        fields: DynamicFormConfig[],
    ): FormlyFieldConfig[] {
        return fields.map(field =>
            this.createFormlyFieldFromDynamicField(field),
        );
    }

    private createFormlyFieldFromDynamicField(
        field: DynamicFormConfig,
    ): FormlyFieldConfig {
        switch (field.type) {
            case 'group':
                return {
                    fieldGroupClassName:
                        field.direction === 'col'
                            ? 'flex flex-col gap-8'
                            : field.isGrid
                              ? 'grid grid-cols-1 lg:grid-cols-[repeat(auto-fit,minmax(32%,1fr))] gap-2 w-full'
                              : 'flex flex-col gap-8 @[44rem]:flex-row @[44rem]:gap-2 w-full',
                    fieldGroup: field.config
                        ? this.createFormlyFieldConfig(field.config)
                        : [],
                };
            case 'section':
                return {
                    wrappers: ['section'],
                    fieldGroupClassName: 'flex flex-col gap-8',
                    fieldGroup: field.config
                        ? this.createFormlyFieldConfig(field.config)
                        : [],
                    props: {
                        title: field.title,
                        icon: field.icon,
                    },
                };
            default:
                const formlyField = {
                    key: field.id,
                    type:
                        field.type === 'custom' || field.type === 'component'
                            ? field.id
                            : field.type,
                    className: `field-size-${field.size ?? 1}${field.type === 'hidden' ? ' hidden' : ''}`,
                    wrappers:
                        field.props?.beforeTemplate ||
                        field.props?.afterTemplate
                            ? ['withTemplates', 'default']
                            : ['default'],
                    defaultValue: field.defaultValue,
                    fieldArray:
                        field.type !== 'repeat'
                            ? undefined
                            : field.props?.repeatedConfig
                              ? this.createFormlyFieldFromDynamicField(
                                    field.props?.repeatedConfig,
                                )
                              : undefined,
                    hooks: {
                        onInit: f => {
                            this.updateValidation(field, f);

                            if (!field.onValueChange) return;
                            f.formControl?.valueChanges
                                .pipe(
                                    takeUntilDestroyed(this.destroyRef),
                                    debounceTime(200),
                                )
                                .subscribe(value => {
                                    field.onValueChange!(
                                        value,
                                        this.formGroup()!,
                                        this.fields()!,
                                    );
                                });
                        },
                    },
                } satisfies FormlyFieldConfig;

                this.mapFormlyFieldFromField(formlyField, field);

                return formlyField;
        }
    }

    private mapFormlyFieldFromField(
        formlyField: FormlyFieldConfig,
        field: DynamicFormFieldConfig,
    ): void {
        this.updateValidation(field, formlyField);

        formlyField.expressions = {
            'hide': () => {
                return field.hide ?? false;
            },
        };
        formlyField.props = {
            label: this.multilingualServiceTranslatorService.get(
                field.label as MultilingualString,
            ),
            // This is only for the asterisk, has no functionality whatsoever!
            required: field.required,
            note: field.note,
            ...(field.props ?? {}),
        };

        // XXX: has to be this way, otherwise, weird shit is gonna happen.
        if (field.disabled) {
            formlyField.props.disabled = true;
        } else {
            delete formlyField.props!.disabled;
        }
    }

    // XXX: this has to be called twice, once in the onInit for
    // the formly field, and the other one during the remapping.
    // Our priority is to set the validation on the form control
    // since it allows for dynamic removal and addition of validators.
    // Formly on the other hand allows to set it once during the
    // creation of the form.
    // You might ask: "then why don't we attach the validators
    // on the control only?" and that's because we don't have access
    // to the control during the creation of the form, since we need
    // formly field configs to actually create the controls!
    private updateValidation(
        field: DynamicFormFieldConfig,
        formlyField: FormlyFieldConfig,
    ): void {
        const requiredValidator =
            field.type === 'multilingualTextInput'
                ? requiredMultilingualTextValidator
                : Validators.required;

        const validators = [
            ...(field.validators ?? []),
            ...(field.required ? [requiredValidator] : []),
        ];

        if (formlyField.formControl) {
            const control = formlyField.formControl;
            control.setValidators(validators);
            control.updateValueAndValidity({ emitEvent: false });
        } else {
            if (validators.length) {
                formlyField.validators = {
                    validation: validators,
                };
            }
        }
    }

    private cacheLazyLoadedSelectItems(
        field: DynamicFormFieldConfig,
        keyPrefix: string = '',
    ): void {
        if (
            !field.id ||
            (field.type !== 'select' && field.type !== 'repeat') ||
            (field.type === 'select' &&
                !(field as DynamicFormSelectConfig).props?.items$)
        )
            return;

        const cacheKey = `${keyPrefix}.${field.id}`;

        if (field.type === 'select') {
            (field as DynamicFormSelectConfig).props!.items$ = cacheObservable(
                (field as DynamicFormSelectConfig).props!.items$!,
                this.cachedLazyLoadedSelectItems,
                cacheKey,
            );
            return;
        }

        const repeatedConfig = (field as DynamicFormRepeatConfig).props
            ?.repeatedConfig;

        if (!repeatedConfig) return;

        const traverse = (x: DynamicFormConfig): void => {
            if ((x.type === 'group' || x.type === 'section') && x.config) {
                x.config.forEach(traverse);
            } else {
                const f = x as DynamicFormFieldConfig;
                this.cacheLazyLoadedSelectItems(f, field.id);
            }
        };

        traverse(repeatedConfig);
    }
}
