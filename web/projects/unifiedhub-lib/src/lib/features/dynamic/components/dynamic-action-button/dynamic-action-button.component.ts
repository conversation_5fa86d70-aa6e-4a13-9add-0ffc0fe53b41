import {
    ChangeDetectionStrategy,
    Component,
    computed,
    HostBinding,
    input,
} from '@angular/core';
import {
    ComponentDynamicActionButton,
    DynamicActionButtonMaterialized,
} from '../../types';
import { NgClass } from '@angular/common';
import { RbacDirective } from '../../../../core';
import { RouterLink } from '@angular/router';
import { ComponentLoaderComponent } from '../../../../ui';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'lib-dynamic-action-button',
    templateUrl: './dynamic-action-button.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        RbacDirective,
        RouterLink,
        ComponentLoaderComponent,
        TranslateModule,
    ],
})
export class DynamicActionButtonComponent {
    public readonly config = input.required<DynamicActionButtonMaterialized>();

    protected readonly componentLoaderConfig = computed(() => {
        if (this.config().type !== 'component') return undefined;

        const config = this.config() as ComponentDynamicActionButton;
        return () => ({
            component: config.component,
            inputs: config.inputs,
            outputs: config.outputs,
        });
    });

    @HostBinding('class.hidden')
    protected get className(): boolean {
        return this.config().isHidden as boolean;
    }
}
