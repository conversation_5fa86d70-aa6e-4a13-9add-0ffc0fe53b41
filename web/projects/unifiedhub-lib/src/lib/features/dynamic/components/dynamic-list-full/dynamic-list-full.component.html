<div
    class="flex h-full flex-col gap-4"
    [ngClass]="{
        'md:flex-row': config().isFilterOnSide
    }"
>
    <!-- Filter -->
    <lib-dynamic-list-full-filter
        [tableController]="tableController()"
        [config]="updatedConfig().filter"
        [isQueryParamTrackingEnabled]="
            updatedConfig().isQueryParamTrackingEnabled
        "
        [defaultFilter]="updatedConfig().defaultFilter"
        [persistentFilter]="updatedConfig().persistentFilter"
        [maxShownFilterCount]="updatedConfig().maxShownFilterCount"
        [isFilterOnSide]="config().isFilterOnSide"
        class="shrink-0"
    />

    <div class="flex grow flex-col gap-4">
        @if ((tableController()!.items$ | async) === undefined) {
            <div class="mb-4 flex flex-row justify-center gap-2">
                <lib-loading-ring />
                <div>{{ 'translate_loading' | translate }}</div>
            </div>
        } @else if ((tableController()!.items$ | async)?.length === 0) {
            <lib-no-items-were-found />
        } @else {
            <!-- List -->
            <lib-dynamic-list class="grow" [config]="listConfig()">
                <ng-container top>
                    <!-- Bulk buttons -->
                    @if (bulkActionButtons().length) {
                        <div
                            class="flex flex-col gap-1 bg-primary-50 p-2 md:p-6"
                        >
                            <!-- Label -->
                            @if (enableTotalSelection()) {
                                @if (
                                    selectedItems().length ===
                                        loadedItems()?.length &&
                                    loadedItems()?.length !==
                                        (tableController().filteredCount$
                                            | async) &&
                                    !allItemsSelected()
                                ) {
                                    <div
                                        class="flex flex-row items-center gap-1 self-center"
                                    >
                                        <span class="text-xs text-gray-400">
                                            {{
                                                'translate_count_items_have_been_selected'
                                                    | translate
                                                        : {
                                                              count: selectedItems()
                                                                  .length
                                                          }
                                            }}
                                        </span>
                                        <button
                                            (click)="selectAllItems()"
                                            class="btn btn-sm btn-outline-info"
                                        >
                                            {{
                                                'translate_select_all_count_instead'
                                                    | translate
                                                        : {
                                                              count:
                                                                  tableController()
                                                                      .filteredCount$
                                                                  | async
                                                          }
                                            }}
                                        </button>
                                    </div>
                                } @else if (allItemsSelected()) {
                                    <div
                                        class="flex flex-row items-center gap-1 self-center"
                                    >
                                        <span class="text-xs text-gray-400">
                                            {{
                                                'translate_all_count_items_in_the_table_have_been_selected'
                                                    | translate
                                                        : {
                                                              count:
                                                                  (tableController()
                                                                      .filteredCount$
                                                                  | async)
                                                          }
                                            }}
                                        </span>
                                        <button
                                            (click)="
                                                allItemsSelected.set(false)
                                            "
                                            class="btn btn-sm btn-outline-info"
                                        >
                                            {{
                                                'translate_clear_selection'
                                                    | translate
                                            }}
                                        </button>
                                    </div>
                                }
                            }

                            <!-- Button -->
                            @for (button of bulkActionButtons(); track button) {
                                @if (
                                    !allItemsSelected() ||
                                    button.enableTotalSelection
                                ) {
                                    <lib-dynamic-action-button
                                        [config]="button"
                                    />
                                }
                            }
                        </div>
                    }
                </ng-container>
            </lib-dynamic-list>

            @if (config().fetcher) {
                <!-- Paginator -->
                <lib-table-paginator [tableController]="tableController()" />
            }
        }
    </div>
</div>

<ng-template
    #actionButtonsColumnTemplate
    let-item="item"
    let-rowConfig="rowConfig"
>
    <div class="flex flex-row items-center justify-center gap-2">
        @for (button of actionButtons() | call: item; track button) {
            <lib-dynamic-action-button [config]="button" />
        }

        @if (
            !updatedConfig().isEditLinkHidden &&
            !rowConfig?.editButtonConfig?.isHidden
        ) {
            <a
                *libRbac="rowConfig?.editButtonConfig?.permissionId"
                [routerLink]="
                    rowConfig?.editButtonConfig?.link ??
                    (baseLink() | concatArrays: ['edit', item.id])
                "
                class="btn btn-sm btn-info"
            >
                <i class="fa fa-edit"></i>
            </a>
        }

        @if (
            updatedConfig().deleter && !rowConfig?.deleteButtonConfig?.isHidden
        ) {
            <button
                *libRbac="rowConfig?.deleteButtonConfig?.permissionId"
                [disabled]="
                    (currentlyProcessing() | setHas: item) ||
                    rowConfig?.deleteButtonConfig?.isDisabled ||
                    lockProcessing()
                "
                (click)="delete(item)"
                class="btn btn-sm btn-danger"
            >
                <i class="fa fa-trash"></i>
            </button>
        }
    </div>
</ng-template>

<ng-template #columnCheckboxTemplate>
    <div class="flex flex-row items-center gap-2">
        <input
            (change)="toggleAll()"
            [ngModel]="
                selectedItems().length === loadedItems()?.length ||
                allItemsSelected()
            "
            [disabled]="currentlyProcessing().size > 0 || allItemsSelected()"
            type="checkbox"
        />
    </div>
</ng-template>

<ng-template #valueCheckboxTemplate let-item="item">
    <input
        (change)="toggle(item)"
        [ngModel]="selectedItems().includes(item) || allItemsSelected()"
        [disabled]="currentlyProcessing().size > 0 || allItemsSelected()"
        type="checkbox"
    />
</ng-template>
