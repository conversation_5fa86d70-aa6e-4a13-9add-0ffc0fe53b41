<lib-page-container
    pageTitle="{{ updatedConfig().title | translate }}"
    pageSubtitle="{{ updatedConfig().subtitle | translate }}"
>
    <!-- List button -->
    <ng-container tools class="flex flex-row items-center gap-2">
        <!-- Custom buttons -->
        <ng-content select="[tools]" />

        <!-- List button -->
        <a
            [routerLink]="updatedConfig().listButtonConfig?.link ?? baseLink()"
            *libRbac="updatedConfig().listButtonConfig?.permissionIds"
            class="btn btn-lg btn-primary flex flex-row items-center gap-2"
        >
            <i class="fa fa-bars"> </i>
            <span>
                {{
                    updatedConfig().listButtonConfig?.title ??
                        updatedConfig().title | translate
                }}
            </span>
        </a>

        <!-- Edit button -->
        @if (!updatedConfig().editButtonConfig?.isHidden) {
            <a
                *libRbac="updatedConfig().editButtonConfig?.permissionId"
                [routerLink]="baseLink() | concatArrays: ['edit', itemId()]"
                class="btn btn-lg btn-primary flex flex-row items-center gap-2"
            >
                <i class="fa fa-edit"></i>
                <span>
                    {{ 'translate_edit' | translate }}
                </span>
            </a>
        }
    </ng-container>

    @if (dynamicDetailConfig()) {
        <lib-dynamic-detail
            #dynamicDetailComponent
            content
            [config]="dynamicDetailConfig()!"
            (itemLoad)="setItem($event)"
        />
    }
</lib-page-container>
