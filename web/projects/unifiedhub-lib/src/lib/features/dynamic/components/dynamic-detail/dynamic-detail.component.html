<!-- Sections -->
<ng-container
    content
    [ngTemplateOutlet]="sectionsTemplate"
    [ngTemplateOutletContext]="{ sections: sectionConfigs() }"
/>

<!-- Section list -->
<ng-template #sectionsTemplate let-sections="sections">
    <div class="flex flex-col gap-8">
        @for (section of sections; track section.title) {
            <ng-container *libRbac="(section | asSection).permissionId">
                <ng-container
                    [ngTemplateOutlet]="sectionTemplate"
                    [ngTemplateOutletContext]="{ section }"
                />
            </ng-container>
        }
    </div>
</ng-template>

<!-- Section template -->
<ng-template #sectionTemplate let-section="section">
    <!-- Remove the content container from these section types: -->
    @if (
        section.noContainer ||
        section.type === 'linker' ||
        section.type === 'list_with_create' ||
        section.type === 'row' ||
        section.type === 'tab_list' ||
        (section.type === 'list' &&
            (section | asList).hideIfEmpty &&
            (section | asList).items?.length === 0) ||
        ((section | asList).items$ | async)?.length === 0
    ) {
        <ng-container
            content
            [ngTemplateOutlet]="sectionContentTemplate"
            [ngTemplateOutletContext]="{ section }"
        ></ng-container>
    } @else {
        <lib-content-container contentTitle="{{ section.title | translate }}">
            <ng-container tools>
                @for (
                    button of (section | asSection).toolButtonsConfig;
                    track button.label
                ) {
                    <button
                        (click)="button.onClickFn()"
                        class="btn btn-sm btn-outline-white"
                    >
                        {{ button.label | translate }}
                    </button>
                }
            </ng-container>

            <ng-container
                content
                [ngTemplateOutlet]="sectionContentTemplate"
                [ngTemplateOutletContext]="{ section }"
            ></ng-container>
        </lib-content-container>
    }
</ng-template>

<!-- Section content -->
<ng-template #sectionContentTemplate let-section="section">
    @switch (section.type) {
        @case ('row') {
            <ng-container
                [ngTemplateOutlet]="rowTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('tab_list') {
            <ng-container
                [ngTemplateOutlet]="tabListTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('detail') {
            <ng-container
                [ngTemplateOutlet]="detailTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('list') {
            <ng-container
                [ngTemplateOutlet]="listTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('list_with_create') {
            <ng-container
                [ngTemplateOutlet]="listWithCreateTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('list_full') {
            <ng-container
                [ngTemplateOutlet]="listFullTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('template') {
            <ng-container
                [ngTemplateOutlet]="section.templateRef"
                [ngTemplateOutletContext]="{ item: item()! }"
            />
        }
        @case ('component') {
            <ng-container
                [ngTemplateOutlet]="componentTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
        @case ('linker') {
            <ng-container
                [ngTemplateOutlet]="linkerTemplate"
                [ngTemplateOutletContext]="{ section }"
            />
        }
    }
</ng-template>

<!-- Row template -->
<ng-template #rowTemplate let-section="section">
    <div class="flex flex-col gap-8 xl:flex-row">
        @for (
            childSection of (section | asRow).sectionConfigs;
            track childSection
        ) {
            <div [ngStyle]="{ 'flex': childSection.size ?? 1 }">
                <ng-container
                    [ngTemplateOutlet]="sectionTemplate"
                    [ngTemplateOutletContext]="{ section: childSection }"
                />
            </div>
        }
    </div>
</ng-template>

<!-- Tab list template -->
<ng-template #tabListTemplate let-section="section">
    <lib-tab-list [isVertical]="(section | asTabList).isVertical">
        @for (tab of (section | asTabList).tabs; track tab) {
            <lib-tab
                [tabTitle]="tab.name | translate"
                [badgeCount]="tab.badgeCount"
                *libRbac="tab.permissionId"
            >
                <ng-container
                    [ngTemplateOutlet]="sectionsTemplate"
                    [ngTemplateOutletContext]="{ sections: tab.sectionConfigs }"
                />
            </lib-tab>
        }
    </lib-tab-list>
</ng-template>

<!-- Details template -->
<ng-template #detailTemplate let-section="section">
    <lib-dynamic-detail-section
        [fieldConfigFn]="(section | asDetail).fieldConfigFn"
        [valueConfigFn]="(section | asDetail).valueConfigFn"
        [item]="item()"
    />
</ng-template>

<!-- List template -->
<ng-template #listTemplate let-section="section">
    @if (
        (section | asList).hideIfEmpty && (section | asList).items?.length === 0
    ) {
        <!-- Empty section -->
    } @else {
        <lib-dynamic-list
            *libWaitUntilLoaded="item()"
            [config]="{
                columnConfigFn: (section | asList).columnConfigFn,
                valueConfigFn: (section | asList).valueConfigFn,
                items: (section | asList).items,
                items$: (section | asList).items$
            }"
        />
    }
</ng-template>

<!-- List with create template -->
<ng-template #listWithCreateTemplate let-section="section">
    <lib-dynamic-list-with-create
        *libWaitUntilLoaded="item()"
        [config]="(section | asListWithCreate).config"
        (listChange)="section.reloadOnListChange ? reload() : undefined"
    />
</ng-template>

<!-- List full template -->
<ng-template #listFullTemplate let-section="section">
    <lib-dynamic-list-full *libWaitUntilLoaded="item()" [config]="section" />
</ng-template>

<!-- Linker template -->
<ng-template #linkerTemplate let-section="section">
    <lib-dynamic-linker
        *libWaitUntilLoaded="item()"
        [config]="(section | asLinker).config"
        [linkedListTitle]="section.title"
    />
</ng-template>

<!-- Component template -->
<ng-template #componentTemplate let-section="section">
    <ng-container
        *ngComponentOutlet="section.component; inputs: section.inputs"
    />
</ng-template>
