@if (config()?.length) {
    <div
        class="flex flex-col gap-4 rounded border border-primary-100 bg-primary-50 p-4 md:p-6"
        [ngClass]="{
            'md:h-full md:w-80': isFilterOnSide()
        }"
    >
        <div
            class="flex w-full flex-row items-center gap-2 font-bold text-primary-950"
        >
            <div class="flex grow flex-row items-center gap-2">
                <i class="fa fa-filter-list"></i>
                <span>{{ 'translate_filter_results' | translate }}</span>
            </div>

            <button
                (click)="reset()"
                class="flex shrink-0 flex-row items-center gap-1 rounded-full bg-transparent text-xs font-normal text-gray-400 transition-colors hover:text-gray-500"
            >
                <i class="fa fa-times"></i>
                <span>
                    {{ 'translate_reset_filter' | translate }}
                </span>
            </button>
        </div>
        <div
            class="grid grid-cols-1 gap-2"
            [ngClass]="{
                'lg:grid-cols-3': !isFilterOnSide()
            }"
        >
            @for (
                filter of isCollapsed() && maxShownFilterCount()
                    ? (config() ?? [] | take: maxShownFilterCount()!)
                    : config();
                track filter.id
            ) {
                @if (!filter?.isHidden) {
                    @switch (filter.type) {
                        @case ('text') {
                            <input
                                type="text"
                                (keyup)="
                                    updateAttr(
                                        filter.id,
                                        $any($event.target).value
                                    )
                                "
                                [placeholder]="filter.label ?? '' | translate"
                                [ngModel]="
                                    $any(
                                        (tableController().filter$ | async)
                                            ?.attrs
                                    )?.[filter.id]
                                "
                            />
                        }
                        @case ('number') {
                            <input
                                type="number"
                                (keyup)="
                                    updateAttr(
                                        filter.id,
                                        $any($event.target).value
                                    )
                                "
                                [placeholder]="filter.label ?? '' | translate"
                                [ngModel]="
                                    $any(
                                        (tableController().filter$ | async)
                                            ?.attrs
                                    )?.[filter.id]
                                "
                            />
                        }
                        @case ('checkbox') {
                            <label class="flex flex-row items-center gap-2">
                                <input
                                    type="checkbox"
                                    (ngModelChange)="
                                        updateAttr(filter.id, $event)
                                    "
                                    [placeholder]="
                                        filter.label ?? '' | translate
                                    "
                                    [ngModel]="
                                        $any(
                                            (tableController().filter$ | async)
                                                ?.attrs
                                        )?.[filter.id]
                                    "
                                />

                                <span>
                                    {{ filter.label ?? '' | translate }}
                                </span>
                            </label>
                        }
                        @case ('datetime') {
                            <lib-datetime-input
                                [enableTime]="$any(filter).enableTime ?? false"
                                (datetimeChange)="
                                    updateAttr(
                                        filter.id,
                                        $any($event)?.toISOString()
                                    )
                                "
                                [placeholder]="filter.label ?? ''"
                                [ngModel]="
                                    $any(
                                        (tableController().filter$ | async)
                                            ?.attrs
                                    )?.[filter.id]
                                "
                            />
                        }
                        @case ('select') {
                            <lib-select-input
                                [placeholder]="filter.label"
                                [loaderFetcher]="$any(filter).loaderFetcher"
                                [bindLabel]="$any(filter).bindLabel"
                                [bindLabelSecond]="$any(filter).bindLabelSecond"
                                [bindLabelThird]="$any(filter).bindLabelThird"
                                [bindValue]="$any(filter).bindValue"
                                [isMulti]="$any(filter).isMulti ?? false"
                                [items$]="$any(filter).items$"
                                [items]="$any(filter).items ?? []"
                                [loadOnOpen]="true"
                                [preselectedItems]="
                                    selectPreloadedItems()[filter.id]
                                "
                                [ngModel]="
                                    $any(
                                        (tableController().filter$ | async)
                                            ?.attrs
                                    )?.[filter.id]
                                "
                                (ngModelChange)="updateAttr(filter.id, $event)"
                                (change)="
                                    updateSelectFilterSelection(
                                        filter.id,
                                        $event
                                    )
                                "
                            />
                        }
                    }
                }
            }
        </div>

        @if (maxShownFilterCount()) {
            <button
                (click)="toggle()"
                class="flex shrink-0 flex-row items-center gap-1 self-center rounded-full bg-transparent text-xs font-normal text-gray-400 transition-colors hover:text-gray-500"
            >
                <i
                    class="fa"
                    [ngClass]="{
                        'fa-chevrons-down': isCollapsed(),
                        'fa-chevrons-up': !isCollapsed()
                    }"
                ></i>
                <span>
                    {{
                        (isCollapsed()
                            ? 'translate_expand'
                            : 'translate_collapse'
                        ) | translate
                    }}
                </span>
            </button>
        }
    </div>
}
