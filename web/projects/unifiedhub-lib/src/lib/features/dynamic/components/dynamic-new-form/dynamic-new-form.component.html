<div class="flex flex-col gap-8">
    @if (errors().length) {
        <lib-alert
            alertType="danger"
            alertTitle="{{ 'translate_an_error_has_occurred' | translate }}"
            [alertList]="errors()"
        />
    }

    <lib-dynamic-form
        [config]="config().formConfig"
        [onValuesChange]="config().onValuesChange"
        (formGroupChange)="form.set($event)"
        [(item)]="item"
    >
        @if (!hideSubmitButton()) {
            <lib-loading-button
                submitButton
                (click)="submit()"
                [isLoading]="isSubmitting()"
                [label]="
                    (config().itemId
                        ? config().submitButtonLabels?.editLabel ??
                          'translate_save'
                        : config().submitButtonLabels?.createLabel ??
                          'translate_create'
                    ) | translate
                "
                icon="fa fa-save"
            />
        }
    </lib-dynamic-form>
</div>
