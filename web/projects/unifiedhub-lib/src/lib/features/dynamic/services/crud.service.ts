import { map, Observable } from 'rxjs';
import {
    Filter,
    mapFilterToQueryParams,
    mapObjectToQueryParams,
    PaginatedResult,
} from '../../../common';
import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import { CACHE_INTERCEPTOR } from '../../../core';

export abstract class CrudService<T, A = unknown, Sa = unknown> {
    protected constructor(protected readonly httpClient: HttpClient) {}

    public list(filter: Filter<A>): Observable<PaginatedResult<T>> {
        const params = mapFilterToQueryParams(filter);

        const url = this.getBaseEndpoint();
        return this.httpClient.get<PaginatedResult<T>>(url, { params });
    }

    public get(id: string): Observable<T> {
        const url = this.getBaseEndpoint();
        return this.httpClient.get<T>(`${url}/${id}`);
    }

    public create(item: T): Observable<T> {
        const url = this.getBaseEndpoint();
        return this.httpClient.post<T>(url, item);
    }

    public update(id: string, item: T): Observable<T> {
        const url = this.getBaseEndpoint();
        return this.httpClient.put<T>(`${url}/${id}`, item);
    }

    public delete(id: string): Observable<any> {
        const url = this.getBaseEndpoint();
        return this.httpClient.delete(`${url}/${id}`);
    }

    public simpleList(filter: Filter<Sa>): Observable<PaginatedResult<T>> {
        const params = mapFilterToQueryParams(filter);

        const url = this.getBaseEndpoint();
        return this.httpClient.get<PaginatedResult<T>>(`${url}/simple`, {
            params,
        });
    }

    public listAll(attrs?: A, cache: boolean = false): Observable<T[]> {
        const filter = {
            attrs,
            pageNumber: 0,
            pageSize: -1,
        } satisfies Filter<A>;
        const url = this.getBaseEndpoint();
        const params = mapFilterToQueryParams(filter);
        return this.httpClient
            .get<
                PaginatedResult<T>
            >(url, { params, context: cache ? new HttpContext().set(CACHE_INTERCEPTOR, true) : undefined })
            .pipe(map(result => result.items));
    }

    public simpleListAll(attrs?: Sa, cache: boolean = false): Observable<T[]> {
        const filter = {
            attrs,
            pageNumber: 0,
            pageSize: -1,
        } satisfies Filter<Sa>;
        const params = mapFilterToQueryParams(filter);

        const url = this.getBaseEndpoint();
        return this.httpClient
            .get<PaginatedResult<T>>(`${url}/simple`, {
                params,
                context: cache
                    ? new HttpContext().set(CACHE_INTERCEPTOR, true)
                    : undefined,
            })
            .pipe(map(result => result.items));
    }

    public export(ids?: string[], filterAttrs?: A): Observable<Blob> {
        let params = new HttpParams();
        ids?.forEach(id => (params = params.append('ids', id)));
        if (filterAttrs) {
            params = mapObjectToQueryParams(filterAttrs, params);
        }

        const url = this.getBaseEndpoint();
        return this.httpClient.get(`${url}/export`, {
            params,
            responseType: 'blob',
        });
    }

    protected abstract getBaseEndpoint(): string;
}
