import {
    DynamicActionButtonFn,
    DynamicActionButtonMaterializedFn,
} from '../types';
import { ModelSignal } from '@angular/core';

export const materializeActionButtonFn = <T>(
    config: DynamicActionButtonFn<T>,
    currentlyProcessing: ModelSignal<Set<T>>,
    lockProcessing: ModelSignal<boolean>,
    reload: () => void,
): DynamicActionButtonMaterializedFn<T> => {
    const currentlyProcessingValue = currentlyProcessing();
    const isProcessingLockedValue = lockProcessing();

    return (item: T) => {
        const buttons =
            config(
                item,
                {
                    value: currentlyProcessingValue,
                    signal: currentlyProcessing,
                },
                {
                    value: isProcessingLockedValue,
                    signal: lockProcessing,
                },
                reload,
            ) ?? [];

        return buttons.map(button =>
            button.type === 'component'
                ? {
                      ...button,
                      isDisabled: button.isDisabledFn?.() ?? false,
                      isHidden: button.isHiddenFn?.() ?? false,
                  }
                : {
                      ...button,
                      onClickFn: () => button.onClickFn?.(),
                      isDisabled: button.isDisabledFn?.() ?? false,
                      isHidden: button.isHiddenFn?.() ?? false,
                  },
        );
    };
};
