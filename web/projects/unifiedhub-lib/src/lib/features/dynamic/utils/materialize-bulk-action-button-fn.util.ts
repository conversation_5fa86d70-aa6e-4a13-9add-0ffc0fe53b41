import {
    DynamicBulkActionButtonFn,
    DynamicBulkActionButtonMaterializedFn,
} from '../types';
import { ModelSignal } from '@angular/core';

export const materializeBulkActionButtonFn = <T, F>(
    config: DynamicBulkActionButtonFn<T, F>,
    currentlyProcessing: ModelSignal<Set<T>>,
    lockProcessing: ModelSignal<boolean>,
    reload: () => void,
    clearSelection: () => void,
): DynamicBulkActionButtonMaterializedFn<T, F> => {
    const currentlyProcessingValue = currentlyProcessing();
    const isProcessingLockedValue = lockProcessing();

    return (items: T[], filter: F | undefined) => {
        const buttons =
            config(
                items,
                filter,
                {
                    value: currentlyProcessingValue,
                    signal: currentlyProcessing,
                },
                {
                    value: isProcessingLockedValue,
                    signal: lockProcessing,
                },
                reload,
                clearSelection,
            ) ?? [];

        return buttons.map(button =>
            button.type === 'component'
                ? {
                      ...button,
                      isDisabled: button.isDisabledFn?.() ?? false,
                      isHidden: button.isHiddenFn?.() ?? false,
                  }
                : {
                      ...button,
                      onClickFn: () => button.onClickFn?.(),
                      isDisabled: button.isDisabledFn?.() ?? false,
                      isHidden: button.isHiddenFn?.() ?? false,
                  },
        );
    };
};
