import { finalize, Observable } from 'rxjs';
import { SmartAlertService } from '../../smart-alert';
import { WritableSignal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../core';

export const deleteItemFromDynamicList = <T extends { id: string }>(
    item: T,
    smartAlertService: SmartAlertService,
    currentlyProcessing: WritableSignal<Set<T>>,
    deleter: (id: string) => Observable<unknown>,
    reload: () => void,
    translateService: TranslateService,
    toastService: ToastService,
    callback?: () => void,
): void => {
    smartAlertService
        .confirm(
            'translate_delete_item_qm',
            'translate_are_you_sure_you_want_to_delete_this_item_qm',
        )
        .subscribe(confirmed => {
            if (!confirmed) return;
            currentlyProcessing.update(value => {
                return new Set(value).add(item);
            });
            deleter(item.id)
                .pipe(
                    finalize(() => {
                        currentlyProcessing.update(value => {
                            value = new Set(value);
                            value.delete(item);
                            return value;
                        });
                    }),
                )
                .subscribe(() => {
                    reload();
                    callback?.();
                    translateService
                        .get('translate_item_deleted_successfully')
                        .subscribe(str => {
                            toastService.success(str);
                        });
                });
        });
};
