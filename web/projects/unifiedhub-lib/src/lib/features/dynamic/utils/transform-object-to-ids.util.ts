import { getSingularForm } from '../../../common';

export const transformObjectToIdsUtil = (
    values: Record<string, unknown>,
): void => {
    Object.keys(values).forEach(key => {
        const isFile = (value: unknown): boolean => {
            return (
                !!value &&
                typeof value === 'object' &&
                Object.keys(value).some(x => x === 'sizeInBytes')
            );
        };

        const value = values[key];
        if (Array.isArray(value)) {
            if (value.length === 0 || !value[0]['id'] || isFile(value[0]))
                return;
            values = {
                ...values,
                [`${getSingularForm(key)}Ids`]: value.map(
                    (x: { id: string }) => x.id,
                ),
            };

            delete values[key];
            return;
        }

        if (typeof value === 'object' && value && 'id' in value) {
            if (!value['id'] || isFile(values[key])) return;
            values = { ...values, [`${key}Id`]: value['id'] };

            delete values[key];
            return;
        }
    });
};
