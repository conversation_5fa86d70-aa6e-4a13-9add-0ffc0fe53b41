import { ListRowConfig } from './list-row-config.type';

export type ListFullRowConfigFn<T> = (item: T) => ListFullRowConfig;

export type ListFullRowConfig = ListRowConfig & {
    editButtonConfig?: {
        isHidden?: boolean;
        permissionId?: string;
        link?: string[];
    };

    deleteButtonConfig?: {
        isDisabled?: boolean;
        isHidden?: boolean;
        permissionId?: string;
    };
};
