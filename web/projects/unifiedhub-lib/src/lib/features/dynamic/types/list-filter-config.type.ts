import { Filter } from '../../../common';
import { Observable } from 'rxjs';

type BaseFilterConfig<F = unknown> = {
    id: string;
    label?: string;
    onChange?: (
        value: unknown,
        filterConfigs: ListFilterConfig<F>[],
        updateAttrs: (id: string, value: unknown) => void,
    ) => void;
    isHidden?: boolean;
    isDisabled?: boolean;
};

export type ListFilterConfigFn<F = unknown> = (
    filter: Filter<F>,
) => ListFilterConfig<F>[];

export type TextFilterConfig<F = unknown> = BaseFilterConfig<F> & {
    type: 'text';
};

export type NumberFilterConfig<F = unknown> = BaseFilterConfig<F> & {
    type: 'number';
};

export type DatetimeFilterConfig<F = unknown> = BaseFilterConfig<F> & {
    type: 'datetime';
    enableTime?: boolean;
};

export type CheckboxFilterConfig<F = unknown> = BaseFilterConfig<F> & {
    type: 'checkbox';
};

export type SelectFilterConfig<F = unknown> = BaseFilterConfig<F> & {
    type: 'select';
    bindLabel?: string;
    bindLabelSecond?: string;
    bindLabelThird?: string;
    bindValue?: string;
    items?: unknown[];
    items$?: Observable<unknown[]>;
    loaderFetcher?: (keyword: string) => Observable<unknown[]>;
    isMulti?: boolean;
};

export type ListFilterConfig<F = unknown> = (
    | TextFilterConfig<F>
    | NumberFilterConfig<F>
    | DatetimeFilterConfig<F>
    | CheckboxFilterConfig<F>
    | SelectFilterConfig<F>
) & {};
