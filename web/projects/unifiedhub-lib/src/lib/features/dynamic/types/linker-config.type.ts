import { DynamicActionButtonFn, DynamicListFullConfig } from '../index';
import { Observable } from 'rxjs';

export type LinkerListConfig<T, F = unknown> = DynamicListFullConfig<T, F> & {
    actionsFn?: DynamicActionButtonFn<T>;
    transfer: (items: T[], filter: F | undefined) => Observable<unknown>;
    transferPermissionId?: string;
    enableTotalSelection?: boolean;
};

export type LinkerConfig<L, U = L, F = any, G = any> = {
    linked: LinkerListConfig<L, F>;
    unlinked: LinkerListConfig<U, G>;
};
