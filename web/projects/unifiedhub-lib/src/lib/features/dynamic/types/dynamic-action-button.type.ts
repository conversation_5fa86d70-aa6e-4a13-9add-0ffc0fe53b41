import { ModelSignal, Type } from '@angular/core';
import { LinkConfig } from './link-config.type';

export type DynamicBulkActionButtonFn<T, F> = (
    items: T[],
    filter: F | undefined,
    currentlyProcessing: {
        value: Set<T>;
        signal: ModelSignal<Set<T>>;
    },
    lockProcessing: {
        value: boolean;
        signal: ModelSignal<boolean>;
    },
    reload: () => void,
    clearSelection: () => void,
) => DynamicBulkActionButton[];

export type DynamicActionButtonFn<T> = (
    item: T,
    currentlyProcessing: {
        value: Set<T>;
        signal: ModelSignal<Set<T>>;
    },
    lockProcessing: {
        value: boolean;
        signal: ModelSignal<boolean>;
    },
    reload: () => void,
) => DynamicActionButton[];

type BaseDynamicActionButton = {
    isDisabledFn?: () => boolean;
    isHiddenFn?: () => boolean;
    permissionId?: string;
};

export type ComponentDynamicActionButton = {
    type: 'component';
    component: Type<unknown>;
    inputs?: Record<string, unknown>;
    outputs?: { [key: string]: (event: unknown) => void };
};

export type DefaultDynamicActionButton = {
    type: 'primary' | 'success' | 'info' | 'warning' | 'danger';
    label?: string;
    iconClasses?: string;
    onClickFn?: () => void;
    linkConfig?: LinkConfig;
};

export type DynamicActionButton = BaseDynamicActionButton &
    (ComponentDynamicActionButton | DefaultDynamicActionButton);

export type DynamicBulkActionButton = DynamicActionButton & {
    enableTotalSelection?: boolean;
};

export type DynamicActionButtonMaterializedFn<T> = (
    item: T,
) => DynamicActionButtonMaterialized[];

export type DynamicBulkActionButtonMaterializedFn<T, F> = (
    items: T[],
    filter?: F,
) => DynamicActionButtonMaterialized[];

export type DynamicActionButtonMaterialized = DynamicActionButton & {
    onClickFn?: () => void;
    isDisabled?: boolean;
    isHidden?: boolean;
};

export type DynamicBulkActionButtonMaterialized =
    DynamicActionButtonMaterialized & DynamicBulkActionButton;
