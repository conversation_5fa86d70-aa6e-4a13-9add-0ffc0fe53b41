import { TemplateRef, Type } from '@angular/core';
import { ValueType } from './value-type.type';
import { LinkConfig } from './link-config.type';
import { Observable } from 'rxjs';
import { File, Item, MultilingualString } from '../../../common';

export type ValueConfigFn<T> = (item: T) => ValueConfig[];

export type BaseValueConfig = {
    linkConfig?: LinkConfig;
    template?: TemplateRef<any>;
    direction?: 'ltr' | 'rtl' | 'auto';
    alignment?: 'start' | 'center' | 'end';
    noTextWrap?: boolean;
    whitespacePreWrap?: boolean;
    classes?: string;
    translateParams?: Record<string, unknown>;
    isObservable?: boolean;
};

export type IdToNameValueConfig = BaseValueConfig & {
    type: 'id_to_name';
    value?: string;
    idToName$: Observable<Item[]>;
};

export type NumberValueConfig = BaseValueConfig & {
    type: 'number';
    value?: number;
    round?: number;
};

export type CurrencyValueConfig = BaseValueConfig & {
    type: 'currency';
    value?: number;
};

export type MultiLevelValueConfig = BaseValueConfig & {
    type: 'multi_line';
    value?: {
        line1?: ValueConfig[];
        line2?: ValueConfig[];
        line3?: ValueConfig[];
    };
};

export type FileValueConfig = BaseValueConfig & {
    type: 'file';
    value?: File;
    downloader: () => Observable<Blob>;
};

export type ComponentValueConfig = BaseValueConfig & {
    type: 'component';
    value: Type<unknown>;
    inputs?: Record<string, unknown>;
};

export type BulletListValueConfig = BaseValueConfig & {
    type: 'bullet_list';
    value?: (string | MultilingualString)[];
};

export type ValueConfig =
    | IdToNameValueConfig
    | NumberValueConfig
    | MultiLevelValueConfig
    | CurrencyValueConfig
    | FileValueConfig
    | ComponentValueConfig
    | BulletListValueConfig
    | (BaseValueConfig & {
          type?: Exclude<
              ValueType,
              | 'id_to_name'
              | 'number'
              | 'multi_line'
              | 'file'
              | 'component'
              | 'bullet_list'
          >;
          value?: unknown;
      });
