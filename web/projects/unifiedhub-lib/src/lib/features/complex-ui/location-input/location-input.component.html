<div class="flex flex-row items-center gap-1">
    <input type="text" disabled class="grow" [value]="valueForDisplay()" />
    <button
        (click)="showLocationPicker(); onTouch?.()"
        type="button"
        class="btn btn-sm btn-outline-primary"
    >
        <i class="fa fa-location-dot"></i>
    </button>

    @if (value() && !isDisabled()) {
        <button
            (click)="setValue(undefined)"
            type="button"
            class="btn btn-sm btn-outline-danger"
        >
            <i class="fa fa-trash"></i>
        </button>
    }
</div>
