import {
    ChangeDetectionStrategy,
    Component,
    computed,
    forwardRef,
    signal,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { LocationViewerComponent } from '../../../ui';
import { Location } from '../../../common';
import { ModalService } from '../../modal';

@Component({
    selector: 'lib-location-input',
    templateUrl: './location-input.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => LocationInputComponent),
        },
    ],
})
export class LocationInputComponent implements ControlValueAccessor {
    protected value = signal<Location | undefined>(undefined);
    protected onTouch?: () => void;
    protected isDisabled = signal<boolean>(false);
    protected valueForDisplay = computed(() => {
        const value = this.value();
        if (!value) return '';
        return `${value.latitude},${value.longitude}`;
    });

    private onChange?: (value: Location | undefined) => void;

    public constructor(
        private readonly modalService: ModalService,
        private readonly translateService: TranslateService,
    ) {}

    public writeValue(value: any): void {
        this.value.set(value);
    }

    public registerOnChange(fn: (value: Location | undefined) => void): void {
        this.onChange = fn;
    }

    public registerOnTouched(fn: () => void): void {
        this.onTouch = fn;
    }

    public setDisabledState(isDisabled: boolean): void {
        this.isDisabled.set(isDisabled);
    }

    public setValue(value: Location | undefined): void {
        this.value.set(value);
        this.onChange?.(value);
    }

    protected showLocationPicker(): void {
        forkJoin([
            this.translateService.get('translate_select_location_on_map'),
            this.translateService.get('translate_show_location_on_map'),
        ]).subscribe(async ([selectTitle, showTitle]) => {
            const component = await this.modalService.show(
                LocationViewerComponent,
                {
                    title: this.isDisabled() ? showTitle : selectTitle,
                    inputs: {
                        location: this.value(),
                        height: '400px',
                        mode: this.isDisabled() ? 'view' : 'edit',
                    },
                },
            );

            component.location.subscribe(location => this.setValue(location));
        });
    }
}
