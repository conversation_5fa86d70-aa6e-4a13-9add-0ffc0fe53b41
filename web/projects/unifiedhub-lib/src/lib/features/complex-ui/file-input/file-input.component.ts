import {
    ChangeDetectionStrategy,
    Component,
    forwardRef,
    input,
    signal,
} from '@angular/core';
import { File, inputFileToBase64Util } from '../../../common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateMultilingualStringPipe } from '../../../core';
import { Observable } from 'rxjs';
import { FileDownloaderComponent } from '../file-downloader/file-downloader.component';

@Component({
    selector: 'lib-file-input',
    templateUrl: './file-input.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => FileInputComponent),
        },
    ],
    imports: [TranslateMultilingualStringPipe, FileDownloaderComponent],
})
export class FileInputComponent implements ControlValueAccessor {
    public allowedMimeTypes = input('*/*');
    public downloader = input<(file: File) => Observable<Blob>>();

    protected readonly value = signal<File | undefined>(undefined);
    protected readonly isDisabled = signal<boolean>(false);
    protected onTouch?: () => void;
    protected readonly isProcessing = signal<boolean>(false);

    private onChange?: (value?: File) => void;

    public writeValue(value?: File): void {
        this.value.set(value);
    }

    public registerOnChange(fn: (value?: File) => void): void {
        this.onChange = fn;
    }

    public registerOnTouched(fn: () => void): void {
        this.onTouch = fn;
    }

    public setDisabledState?(isDisabled: boolean): void {
        this.isDisabled.set(isDisabled);
    }

    protected async loadFile(event: Event): Promise<void> {
        const fileInput = event.target as HTMLInputElement;
        if (fileInput.files?.length !== 1) return;
        const inputFile = fileInput.files[0];
        const result = await inputFileToBase64Util(inputFile);
        this.updateValue({
            name: { ar: result.name, en: result.name },
            bytes: result.bytes,
        });
    }

    protected updateValue(value: File | undefined): void {
        this.value.set(value);
        this.onChange?.(value);
    }
}
