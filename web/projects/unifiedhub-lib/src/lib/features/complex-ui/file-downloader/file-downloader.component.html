@if (buttonTemplate()) {
    <ng-container
        [ngTemplateOutlet]="buttonTemplate()!"
        [ngTemplateOutletContext]="{
            downloadFn: download.bind(this),
            isProcessing: isProcessing()
        }"
    >
    </ng-container>
} @else {
    <ng-container [ngTemplateOutlet]="defaultButtonTemplate"></ng-container>
}

<ng-template #defaultButtonTemplate>
    <button
        class="btn btn-sm btn-primary inline-flex flex-row items-center gap-2"
        [disabled]="isProcessing()"
        (click)="download()"
    >
        <i class="fa fa-download"></i>
        <span>
            {{ 'translate_download_file' | translate }}
        </span>
    </button>
</ng-template>
