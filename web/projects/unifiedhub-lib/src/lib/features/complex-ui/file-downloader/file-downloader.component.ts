import {
    ChangeDetectionStrategy,
    Component,
    input,
    output,
    signal,
    TemplateRef,
} from '@angular/core';
import { finalize, Observable, Subject } from 'rxjs';
import { File, saveBlobToFileUtil } from '../../../common';
import { MultilingualStringTranslatorService } from '../../../core';
import { TranslateModule } from '@ngx-translate/core';
import { NgTemplateOutlet } from '@angular/common';
import { FileViewerComponent } from '../../../ui';
import { ModalService } from '../../modal';

@Component({
    selector: 'lib-file-downloader',
    templateUrl: 'file-downloader.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, NgTemplateOutlet],
})
export class FileDownloaderComponent {
    public downloader = input.required<(file: File) => Observable<Blob>>();
    public file = input.required<File>();
    public buttonTemplate = input<TemplateRef<unknown> | undefined>();
    public processing = output<boolean>();

    protected readonly isProcessing = signal<boolean>(false);

    public constructor(
        private readonly multilingualStringTranslatorService: MultilingualStringTranslatorService,
        private readonly modalService: ModalService,
    ) {}

    protected download(): void {
        const downloader = this.downloader()!;

        const fileName =
            this.multilingualStringTranslatorService.get(this.file()?.name) ??
            'Untitled';

        this.isProcessing.set(true);
        this.processing.emit(true);
        downloader(this.file())
            .pipe(
                finalize(() => {
                    this.isProcessing.set(false);
                    this.processing.emit(false);
                }),
            )
            .subscribe(async blob => {
                if (
                    this.file()?.contentType === 'application/pdf' ||
                    this.file()?.contentType?.split('/')[0] === 'image'
                ) {
                    const src = URL.createObjectURL(blob);
                    await this.showFileViewer(src, fileName);
                } else {
                    saveBlobToFileUtil(blob, fileName);
                }
            });
    }

    private async showFileViewer(src: string, title?: string): Promise<void> {
        let subject = new Subject();
        await this.modalService.show(FileViewerComponent, {
            title,
            onDismiss: () => {
                window.URL.revokeObjectURL(src);
                subject.next(undefined);
                subject.complete();
            },
            inputs: {
                fileSrc: src,
            },
            size: {
                width: '100%',
                height: '100%',
            },
        });
    }
}
