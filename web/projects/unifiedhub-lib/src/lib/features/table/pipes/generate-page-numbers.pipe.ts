import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'generatePageNumbers',
    standalone: true,
})
export class GeneratePageNumbersPipe implements PipeTransform {
    public transform(
        pageNumber: number,
        pageCount: number,
        length: number = 7,
    ): number[] {
        let start = pageNumber - Math.ceil(length / 2);
        let end = pageNumber + Math.floor(length / 2);

        if (length - end >= 0) {
            end = length;
        }
        end = Math.min(end, pageCount);

        if (end - start < length) {
            start -= length - (end - start);
        }
        start = Math.max(start, 0);

        return Array(end - start)
            .fill(0)
            .map((_, idx) => idx + start);
    }
}
