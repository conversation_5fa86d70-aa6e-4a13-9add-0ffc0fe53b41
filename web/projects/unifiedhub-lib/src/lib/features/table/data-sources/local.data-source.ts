import { Observable, of } from 'rxjs';
import {
    evaluateObjectProperty,
    Filter,
    PaginatedResult,
} from '../../../common';
import { DataSource } from '../interfaces';

export class LocalDataSource<T, F = unknown> implements DataSource<T, F> {
    private static readonly defaultPageNumber = 0;
    private static readonly defaultPageSize = 20;

    public constructor(private readonly items: T[]) {}

    public static create<T>(items: T[]): LocalDataSource<T> {
        return new LocalDataSource<T>(items);
    }

    public load(filter: Filter<F>): Observable<PaginatedResult<T>> {
        const filteredItems = this.applyFilter(filter.attrs);

        const pageNumber =
            filter.pageNumber ?? LocalDataSource.defaultPageNumber;
        const pageSize = filter.pageSize ?? LocalDataSource.defaultPageSize;

        return of({
            items: filteredItems.slice(
                pageNumber * pageSize,
                pageNumber * pageSize + pageSize,
            ),
            count: this.items.length,
            filteredCount: filteredItems.length,
        });
    }

    private applyFilter(attrs?: F): T[] {
        if (!attrs) return this.items;

        let filteredItems = this.items;
        Object.keys(attrs as any).forEach(key => {
            const value = (attrs as any)[key];
            filteredItems = filteredItems.filter(x => {
                const currentValue = evaluateObjectProperty(x, key);

                if (Array.isArray(value)) {
                    return value.includes(currentValue);
                } else if (
                    typeof value === 'string' &&
                    typeof currentValue === 'string'
                ) {
                    return currentValue
                        .toLowerCase()
                        .trim()
                        .includes(value.toLowerCase().trim());
                }

                return value === currentValue;
            });
        });
        return filteredItems;
    }
}
