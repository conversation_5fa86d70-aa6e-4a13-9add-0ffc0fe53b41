<div class="flex flex-row items-center gap-2">
    <div class="grow">
        <ul
            class="inline-flex list-none flex-row items-center gap-1 self-start justify-self-start overflow-hidden text-sm"
        >
            <!-- First page -->
            <ng-container
                [ngTemplateOutlet]="navButtonTemplate"
                [ngTemplateOutletContext]="{
                    direction: 'start',
                    isReset: true,
                    disablePageNumberValue: 0
                }"
            />

            <!-- Previous -->
            <ng-container
                [ngTemplateOutlet]="navButtonTemplate"
                [ngTemplateOutletContext]="{
                    direction: 'start',
                    delta: -1,
                    disablePageNumberValue: 0
                }"
            />

            <!-- Page numbers -->
            @for (
                pageNumber of (tableController().pageNumber$ | async)!
                    | generatePageNumbers
                        : (tableController().pageCount$ | async)!;
                track pageNumber
            ) {
                @if ((tableController().pageNumber$ | async) !== pageNumber) {
                    <button
                        (click)="tableController().updatePageNumber(pageNumber)"
                    >
                        <ng-container
                            [ngTemplateOutlet]="pageNumberTemplate"
                        ></ng-container>
                    </button>
                } @else {
                    <ng-container
                        [ngTemplateOutlet]="pageNumberTemplate"
                    ></ng-container>
                }

                <ng-template #pageNumberTemplate>
                    <li
                        class="rounded"
                        [ngClass]="{
                            'bg-primary-100 hover:bg-primary-200':
                                (tableController().pageNumber$ | async) !==
                                pageNumber,
                            'bg-primary-900 text-white':
                                (tableController().pageNumber$ | async) ===
                                pageNumber
                        }"
                    >
                        {{ pageNumber + 1 }}
                    </li>
                </ng-template>
            }

            <!-- Next -->
            <ng-container
                [ngTemplateOutlet]="navButtonTemplate"
                [ngTemplateOutletContext]="{
                    direction: 'end',
                    delta: 1,
                    disablePageNumberValue:
                        (tableController().pageCount$ | async)! - 1
                }"
            />

            <!-- Last page -->
            <ng-container
                [ngTemplateOutlet]="navButtonTemplate"
                [ngTemplateOutletContext]="{
                    direction: 'end',
                    isReset: true,
                    pageCount: (tableController().pageCount$ | async)!,
                    disablePageNumberValue:
                        (tableController().pageCount$ | async)! - 1
                }"
            />
        </ul>
    </div>
    <div
        class="flex shrink-0 flex-row items-center gap-2 text-sm font-semibold text-primary-600"
    >
        <span>
            @if (
                (tableController().count$ | async) !==
                (tableController().filteredCount$ | async)
            ) {
                {{
                    'translate_filtered_filtered_out_of_total'
                        | translate
                            : {
                                  filtered:
                                      (tableController().filteredCount$
                                      | async),
                                  total: (tableController().count$ | async)
                              }
                }}
            } @else {
                {{
                    'translate_total_results_number'
                        | translate
                            : { number: (tableController().count$ | async) }
                }}
            }
        </span>

        <lib-select-input
            [isNotClearable]="true"
            [isNotSearchable]="true"
            [items]="[10, 20, 50, 100]"
            [ngModel]="tableController().pageSize$ | async"
            (change)="tableController().updatePageSize($any($event))"
        />
    </div>
</div>

<!-- Nav button template -->
<ng-template
    #navButtonTemplate
    let-direction="direction"
    let-delta="delta"
    let-isReset="isReset"
    let-pageCount="pageCount"
    let-disablePageNumberValue="disablePageNumberValue"
>
    @if ((tableController().pageNumber$ | async) !== disablePageNumberValue) {
        <button
            (click)="
                isReset
                    ? tableController().updatePageNumber(
                          direction === 'start' ? 0 : pageCount - 1
                      )
                    : moveByDelta(delta)
            "
        >
            <ng-container
                [ngTemplateOutlet]="navLabelTemplate"
                [ngTemplateOutletContext]="{
                    enabled: true
                }"
            ></ng-container>
        </button>
    } @else {
        <ng-container
            [ngTemplateOutlet]="navLabelTemplate"
            [ngTemplateOutletContext]="{
                enabled: false
            }"
        ></ng-container>
    }
    <ng-template #navLabelTemplate let-enabled="enabled">
        <li
            class="rounded"
            [ngClass]="{
                'bg-primary-100 hover:bg-primary-200': enabled,
                'cursor-not-allowed bg-primary-50 text-primary-400': !enabled
            }"
        >
            <i
                class="fa fa-chevron{{ isReset ? 's' : '' }}-{{
                    direction === 'start' ? 'left' : 'right'
                }} hidden ltr:block"
            ></i>
            <i
                class="fa fa-chevron{{ isReset ? 's' : '' }}-{{
                    direction === 'start' ? 'right' : 'left'
                }} hidden rtl:block"
            ></i>
        </li>
    </ng-template>
</ng-template>
