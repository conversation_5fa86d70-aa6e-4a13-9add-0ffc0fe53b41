import { BehaviorSubject, merge, Observable, of, Subject } from 'rxjs';
import { catchError, debounceTime, switchMap, tap } from 'rxjs/operators';

export class NgSelectLoader<T> {
    public items$!: Observable<T[]>;
    public itemInput$ = new Subject<string>();
    public itemsLoading = false;

    protected readonly itemsSubject = new BehaviorSubject<T[]>([]);

    private isInitialLoadDone = false;

    public constructor(
        private readonly fetcher: (keyword: string) => Observable<T[]>,
        protected readonly defaultList: T[] = [],
    ) {
        this.initLoader();
    }

    public loadInitialList(keyword: string = ''): void {
        if (this.isInitialLoadDone) return;
        this.isInitialLoadDone = true;
        this.itemInput$.next(keyword);
    }

    public dispose(): void {
        this.itemInput$.complete();
    }

    protected initLoader(): void {
        this.items$ = merge(
            of(this.defaultList), // default items
            this.itemsSubject.asObservable(),
            this.itemInput$.pipe(
                debounceTime(100),
                tap(() => this.itemsSubject.next([])),
                tap(() => (this.itemsLoading = true)),
                switchMap(keyword =>
                    this.fetcher(keyword).pipe(
                        catchError(() => of([])), // empty list on error
                        tap(() => (this.itemsLoading = false)),
                    ),
                ),
            ),
        );
    }
}
