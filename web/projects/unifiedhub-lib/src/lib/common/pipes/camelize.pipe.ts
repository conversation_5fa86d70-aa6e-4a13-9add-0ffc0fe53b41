import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'camelize',
    standalone: true,
})
export class CamelizePipe implements PipeTransform {
    public transform(str: string | undefined): string | undefined {
        return str
            ?.replace(/^\w|[A-Z]|\b\w/g, (word, index) => {
                return index === 0 ? word.toLowerCase() : word.toUpperCase();
            })
            .replace(/\s+|[-_]/g, '');
    }
}
