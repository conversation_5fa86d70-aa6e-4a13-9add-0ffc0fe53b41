import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'formatBytes',
    standalone: true,
})
export class FormatBytesPipe implements PipeTransform {
    private units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    public transform(bytes: number, decimals: number = 2): string {
        if (bytes === 0) {
            return '0 B';
        }

        if (!Number.isFinite(bytes)) {
            return 'N/A';
        }

        // Get the appropriate unit index
        const unitIndex = Math.floor(
            Math.log(Math.abs(bytes)) / Math.log(1024),
        );

        // Make sure we don't exceed available units
        const clampedUnitIndex = Math.min(unitIndex, this.units.length - 1);

        // Convert to the appropriate unit
        const value = bytes / Math.pow(1024, clampedUnitIndex);

        // Format the number with proper decimals
        const formattedValue = value.toFixed(decimals);

        // Remove trailing zeros after decimal point
        const cleanValue = parseFloat(formattedValue).toString();

        return `${cleanValue} ${this.units[clampedUnitIndex]}`;
    }
}
