import { Filter } from '../types';
import { HttpParams } from '@angular/common/http';

export const mapFilterToQueryParams = <T>(filter?: Filter<T>): HttpParams => {
    if (!filter) return new HttpParams();

    let params = new HttpParams();

    if (filter.attrs) {
        Object.keys(filter.attrs as any).forEach(attr => {
            const value = (filter.attrs as any)[attr];

            if (!value) {
                return;
            }

            if (Array.isArray(value)) {
                value.forEach(x => (params = params.append(attr, x)));
            } else {
                params = params.append(attr, value);
            }
        });
    }

    if (filter.orderBy?.length! > 0) {
        filter.orderBy!.forEach((x, i) => {
            params = params.append(
                `orderByProperties[${i}].property`,
                x.property,
            );
            params = params.append(
                `orderByProperties[${i}].direction`,
                x.direction,
            );
        });
    }

    params = params
        .append('pageNumber', `${filter.pageNumber ?? 0}`)
        .append('pageSize', `${filter.pageSize ?? 20}`);

    return params;
};
