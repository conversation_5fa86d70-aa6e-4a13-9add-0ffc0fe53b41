import {
    BehaviorSubject,
    catchError,
    filter,
    first,
    Observable,
    throwError,
} from 'rxjs';

export const cacheObservable = <T>(
    observable: Observable<T>,
    cache: Map<string, Observable<T>>,
    cacheKey: string,
): Observable<T> => {
    if (cache.has(cacheKey)) return cache.get(cacheKey)!;

    const subject = new BehaviorSubject<T | undefined>(undefined);
    cache.set(cacheKey, subject.pipe(filter((x): x is T => !!x)));

    observable
        .pipe(
            first(),
            catchError(error => {
                cache.delete(cacheKey);
                return throwError(() => error);
            }),
        )
        .subscribe({
            next: data => {
                subject.next(data);
            },
            error: error => {
                subject.error(error);
                cache.delete(cacheKey);
            },
        });

    return cache.get(cacheKey)!;
};
