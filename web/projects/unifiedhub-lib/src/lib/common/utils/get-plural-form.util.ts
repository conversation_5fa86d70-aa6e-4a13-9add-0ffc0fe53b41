export const getPluralForm = (word: string): string => {
    if (!word) return word;

    const original = word;
    word = word.trim();
    const lowercase = word.toLowerCase();

    // Irregular singulars mapping
    const irregularSingulars: Record<string, string> = {
        'child': 'children',
        'man': 'men',
        'woman': 'women',
        'person': 'people',
        'tooth': 'teeth',
        'foot': 'feet',
        'mouse': 'mice',
        'goose': 'geese',
        'phenomenon': 'phenomena',
        'criterion': 'criteria',
        'datum': 'data',
        'analysis': 'analyses',
        'diagnosis': 'diagnoses',
        'thesis': 'theses',
        'crisis': 'crises',
        'radius': 'radii',
        'curriculum': 'curricula',
        'cactus': 'cacti',
        'fungus': 'fungi',
        'nucleus': 'nuclei',
        'syllabus': 'syllabi',
        'focus': 'foci',
        'stimulus': 'stimuli',
        'ox': 'oxen',
        'die': 'dice',
        'penny': 'pence',
        'quiz': 'quizzes',
        'country': 'countries',
    };

    // Check for irregular singulars
    if (irregularSingulars[lowercase]) {
        const plural = irregularSingulars[lowercase];

        // Preserve capitalization
        if (word === word.toUpperCase()) return plural.toUpperCase();
        if (word[0] === word[0].toUpperCase())
            return plural.charAt(0).toUpperCase() + plural.slice(1);
        return plural;
    }

    // Words that are the same in singular and plural
    const unchanging = [
        'deer',
        'fish',
        'sheep',
        'moose',
        'series',
        'species',
        'scissors',
        'corps',
        'means',
        'offspring',
    ];
    if (unchanging.includes(lowercase)) return original;

    // Regular rules

    // Words ending in 'y' preceded by a consonant
    if (
        lowercase.endsWith('y') &&
        lowercase.length > 1 &&
        !/[aeiou]/.test(lowercase.charAt(lowercase.length - 2)) // cspell: disable-line
    ) {
        return (
            word.slice(0, -1) +
            (word[0] === word[0].toUpperCase() ? 'I' : 'i') +
            'es'
        );
    }

    // Words ending in 's', 'x', 'z', 'ch', 'sh'
    if (
        /[sxz]$/.test(lowercase) ||
        /ch$/.test(lowercase) ||
        /sh$/.test(lowercase)
    ) {
        return word + 'es';
    }

    // Words ending in 'f' or 'fe'
    if (lowercase.endsWith('f')) {
        const exceptions = ['roof', 'belief', 'chef', 'chief'];
        return exceptions.includes(lowercase)
            ? word + 's'
            : word.slice(0, -1) + 'ves';
    }
    if (lowercase.endsWith('fe')) {
        return word.slice(0, -2) + 'ves';
    }

    // Default: add 's'
    return word + 's';
};
