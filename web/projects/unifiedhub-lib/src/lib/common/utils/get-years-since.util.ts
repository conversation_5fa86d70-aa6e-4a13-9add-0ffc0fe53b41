export const getYearsSince = (date: string | Date): number => {
    const currentDate = new Date();
    const pastDate = new Date(date);

    const yearsDifference = currentDate.getFullYear() - pastDate.getFullYear();

    const monthDifference = currentDate.getMonth() - pastDate.getMonth();
    const dayDifference = currentDate.getDate() - pastDate.getDate();

    if (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)) {
        return yearsDifference - 1;
    }

    return yearsDifference;
};
