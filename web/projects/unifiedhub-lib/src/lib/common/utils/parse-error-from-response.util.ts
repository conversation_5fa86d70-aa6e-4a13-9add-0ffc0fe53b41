import { HttpErrorResponse } from '@angular/common/http';

const parseErrorFromBlob = async (errorBlob: Blob): Promise<unknown> => {
    const errorText = await errorBlob.text();
    try {
        return JSON.parse(errorText);
    } catch (e) {
        return undefined;
    }
};

export const parseErrorFromResponse = async (
    res: HttpErrorResponse,
): Promise<{ title: string; detail?: string; type?: string } | undefined> => {
    const error =
        res.error instanceof Blob
            ? await parseErrorFromBlob(res.error)
            : res.error;

    if (error?.['title']) {
        return {
            title: error['title'],
            detail: error['detail'],
            type: error['type'],
        };
    }

    if (error?.['error_description']) {
        return { title: error['error'], detail: error['error_description'] };
    }

    return undefined;
};
