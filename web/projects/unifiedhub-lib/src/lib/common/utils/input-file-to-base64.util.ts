export const inputFileToBase64Util = (
    file: File,
): Promise<{
    name: string;
    bytes: string;
    mimeType: string;
    format: string;
    readerResult: string | ArrayBuffer | null;
}> => {
    return new Promise((resolve, reject) => {
        const tokens = file.name.split('.');
        const format = tokens.length > 1 ? tokens[tokens.length - 1] : '';
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const tokens = (reader.result as string).split(',');
            const mimeType = tokens[0]
                .replace('data:', '')
                .replace(';base64', '');
            resolve({
                name: file.name,
                bytes: tokens[1],
                mimeType,
                format,
                readerResult: reader.result,
            });
        };
        reader.onerror = () => reject();
    });
};
