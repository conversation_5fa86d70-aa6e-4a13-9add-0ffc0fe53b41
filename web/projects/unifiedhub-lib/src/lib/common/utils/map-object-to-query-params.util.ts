import { HttpParams } from '@angular/common/http';

export const mapObjectToQueryParams = (
    obj?: unknown,
    params?: HttpParams,
): HttpParams => {
    params ??= new HttpParams();

    if (obj) {
        Object.keys(obj as Record<string, unknown>).forEach(attr => {
            const value = (obj as Record<string, unknown>)[attr];

            if (!value) {
                return;
            }

            if (Array.isArray(value)) {
                value.forEach(x => (params = params!.append(attr, x)));
            } else {
                params = params!.append(
                    attr,
                    value as string | number | boolean,
                );
            }
        });
    }

    return params;
};
