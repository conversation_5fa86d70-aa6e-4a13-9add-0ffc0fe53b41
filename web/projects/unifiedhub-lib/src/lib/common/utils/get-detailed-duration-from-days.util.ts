import { formatDuration, Locale } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { TranslateService } from '@ngx-translate/core';

export const getDetailedDurationFromDays = (
    days: number,
    translateService: TranslateService,
): string => {
    const locales: Record<string, Locale> = {
        'ar': ar,
        'en': enUS,
    };

    // Create an interval from number of days
    const years = Math.floor(days / 365.2425);
    const remainingDaysAfterYears = days - years * 365.2425;
    const months = Math.floor(remainingDaysAfterYears / 30.4166);
    const remainingDays = Math.floor(
        remainingDaysAfterYears - months * 30.4166,
    );

    const interval = {
        years,
        months,
        days: remainingDays,
    };

    return formatDuration(interval, {
        format: ['years', 'months', 'days'],
        locale: locales[translateService.currentLang] ?? enUS,
        delimiter: ' - ',
    });
};
