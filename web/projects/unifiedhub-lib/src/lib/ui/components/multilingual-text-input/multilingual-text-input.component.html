<div class="flex flex-col gap-2 lg:flex-row">
    @for (lang of translateService.langs; track lang) {
        <div class="flex grow flex-col">
            @switch (type()) {
                @case ('short') {
                    <input
                        class="z-[1]"
                        [value]="$any(value())[lang]"
                        (keyup)="updateValue($any($event.target).value, lang)"
                        type="text"
                        [ngStyle]="
                            $any(lang === 'en' && { 'direction': 'ltr' })
                        "
                        (blur)="onTouch?.()"
                        [disabled]="isDisabled()"
                    />
                }
                @case ('long') {
                    <textarea
                        class="z-[1]"
                        [value]="$any(value())[lang]"
                        (change)="updateValue($any($event.target).value, lang)"
                        [ngStyle]="
                            $any(lang === 'en' && { 'direction': 'ltr' })
                        "
                        (blur)="onTouch?.()"
                        [disabled]="isDisabled()"
                    ></textarea>
                }
            }

            <div
                class="lang-label self-start rounded-b border-b border-l border-r border-gray-300 bg-gray-200 px-4 py-0.5 text-xs text-gray-400 ltr:ml-2 rtl:mr-2"
            >
                {{ lang }}
            </div>
        </div>
    }
</div>
