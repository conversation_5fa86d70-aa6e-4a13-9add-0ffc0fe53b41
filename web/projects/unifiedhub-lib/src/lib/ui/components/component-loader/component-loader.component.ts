import {
    ChangeDetectionStrategy,
    Component,
    ComponentRef,
    effect,
    input,
    OnD<PERSON>roy,
    OutputEmitterRef,
    OutputRefSubscription,
    Type,
    viewChild,
    ViewContainerRef,
} from '@angular/core';

@Component({
    selector: 'lib-component-loader',
    templateUrl: 'component-loader.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComponentLoaderComponent implements OnDestroy {
    public readonly config = input.required<
        () => {
            component: Type<any>;
            inputs?: Record<string, unknown>;
            outputs?: { [key: string]: (event: unknown) => void };
        }
    >();

    private readonly container = viewChild('container', {
        read: ViewContainerRef,
    });

    private componentRef: ComponentRef<Record<string, unknown>> | undefined =
        undefined;

    private readonly subscriptions: OutputRefSubscription[] = [];

    public constructor() {
        effect(() => {
            // Clear previous component
            this.container()!.clear();

            const { component, inputs, outputs } = this.config()();

            // Create component
            this.componentRef = this.container()!.createComponent(component);

            // Set inputs
            if (inputs) {
                Object.keys(inputs).forEach(key => {
                    this.componentRef!.setInput(key, inputs[key]);
                });
            }

            // Subscribe to outputs
            if (outputs) {
                Object.keys(outputs).forEach(key => {
                    if (this.componentRef!.instance[key]) {
                        this.subscriptions.push(
                            (
                                this.componentRef!.instance[
                                    key
                                ] as OutputEmitterRef<unknown>
                            ).subscribe(outputs[key]),
                        );
                    }
                });
            }
        });
    }

    public ngOnDestroy(): void {
        if (this.componentRef) {
            this.componentRef.destroy();
        }
        this.subscriptions.forEach(x => x.unsubscribe());
    }
}
