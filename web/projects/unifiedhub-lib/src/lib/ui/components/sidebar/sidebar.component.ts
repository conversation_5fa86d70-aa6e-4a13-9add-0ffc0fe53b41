import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    input,
    OnInit,
    signal,
} from '@angular/core';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { animations } from './animations';
import { NavItem } from './types';
import { uuid } from '../../../common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RbacDirective } from '../../../core';

@Component({
    selector: 'lib-sidebar',
    templateUrl: './sidebar.component.html',
    styleUrls: ['./sidebar.component.scss'],
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [animations],
    imports: [
        NgTemplateOutlet,
        TranslateModule,
        NgClass,
        RouterLink,
        RbacDirective,
    ],
})
export class SidebarComponent implements OnInit, AfterViewInit {
    public navItems = input.required<NavItem[]>();
    public displayMode = input<'dark' | 'light'>('light');
    public enabledFilter = input<boolean>(false);
    public scrollToItemOnNavigation = input<boolean>(true);

    public uuid: string = `sidebar-${uuid()}`;

    protected filteredItems = signal<NavItem[]>([]);

    // Used as bases for the item's children, since
    // during filtering the children property will
    // be overwritten with the filtered children.
    // When we perform the search again we need to filter
    // items' children based on the original children list.
    // However, the children list will have been overwritten
    // by previous filtering.
    private itemToChildren: Map<NavItem, NavItem[]> = new Map<
        NavItem,
        NavItem[]
    >();

    public constructor(
        private readonly router: Router,
        private readonly translateService: TranslateService,
        private readonly changeDetectorRef: ChangeDetectorRef,
        private readonly elementRef: ElementRef,
        private readonly destroyRef: DestroyRef,
    ) {}

    public ngOnInit(): void {
        this.mapParentChildRelationships();
        this.translateTitles();
        this.filterItems('');

        // Because we are listening inside the ngOnInit, the first router
        // event won't be fired. We need to check before listening to account
        // for the first refresh.
        setTimeout(() => this.updateActiveStatus());

        this.router.events
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(event => {
                if (!(event instanceof NavigationEnd)) return;

                this.updateActiveStatus();
            });
    }

    public ngAfterViewInit(): void {
        this.scrollIntoActiveLink();
    }

    public toggleExpandableItem(item: NavItem): void {
        // Look for all items that should
        // not be closed by the toggle, basically
        // all the upper level parents.
        const parents: NavItem[] = [];
        const traverser = (items: NavItem[]): boolean => {
            const foundItem = items.find(x => {
                const children = this.itemToChildren.get(x);
                return (
                    x === item ||
                    ((x.type === 'expandable' || x.type == 'section') &&
                        children &&
                        children.length > 0 &&
                        traverser(children))
                );
            });

            if (!foundItem) return false;

            parents.push(foundItem);
            return true;
        };
        traverser(this.navItems());

        // Close all items that are not in the path.
        const closer = (items: NavItem[]): void => {
            items.forEach(x => {
                if (!parents.includes(x)) {
                    x.isOpen = false;
                }

                const children = this.itemToChildren.get(x);

                if (
                    (x.type === 'expandable' || x.type === 'section') &&
                    children &&
                    children.length > 0
                ) {
                    closer(children);
                }
            });
        };
        closer(this.navItems());

        // Toggle the item.
        item.isOpen = !item.isOpen;
    }

    public filterItems(keyword: string): void {
        const deepFilter = (items: NavItem[]): NavItem[] => {
            return items.filter(item => {
                switch (item.type) {
                    case 'item':
                        return item.title.includes(keyword);
                    case 'expandable':
                    case 'section':
                        const children = this.itemToChildren.get(item);
                        if (!children || children.length === 0) return false;
                        item.children = deepFilter(children);
                        return item.children.length > 0;
                }
            });
        };

        this.filteredItems.set(deepFilter(this.navItems()));
    }

    private scrollIntoActiveLink(): void {
        if (!this.scrollToItemOnNavigation()) return;

        const activeElement = this.elementRef.nativeElement.querySelector(
            `#${this.uuid} .nav-item-link.active`,
        );

        activeElement?.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest',
        });
    }

    private translateTitles(): void {
        const deepTranslate = (items: NavItem[]): void => {
            items.forEach(item => {
                item.title = this.translateService.instant(item.title);

                const children = this.itemToChildren.get(item);

                if (children && children.length > 0) {
                    deepTranslate(children);
                }
            });
        };

        deepTranslate(this.navItems());
    }

    private updateActiveStatus(): void {
        const check = (items: NavItem[]): boolean => {
            const segments =
                this.router.url === '/'
                    ? ['']
                    : this.router.url.split('?')[0].split('/');

            // First mark appropriate items as
            // active based on the current route.
            items.forEach(x => {
                if (x.type === 'item') {
                    x.isActive =
                        (x.link?.length === segments.length &&
                            x.link?.every((x, i) => x === segments[i])) ||
                        false;
                } else if (x.type === 'expandable' || x.type === 'section') {
                    const children = this.itemToChildren.get(x)!;

                    // Set isOpen to true if any child is open or if the key matches the second segment of the current route
                    // XXX: disabled because sometimes we do not want this behavior.
                    // E.g., human resources settings section items where the first
                    // url segment matches the one from the employees primary pages.
                    // This leads to both employees and human resources sections be
                    // open at the same time.
                    // x.isOpen =
                    //     check(children) ||
                    //     children[0]?.link?.[1] === segments[1];

                    x.isOpen = check(children);
                }
            });
            // Then return true/false based on either
            // the item or its children.
            return items.some(x => {
                if (x.type === 'item') {
                    return x.isActive;
                } else if (x.type === 'expandable' || x.type === 'section') {
                    return x.isOpen;
                }
                return false;
            });
        };

        check(this.navItems());

        // We need to detect changes to let angular
        // update the active class before performing
        // the scrolling.
        this.changeDetectorRef.detectChanges();
        this.scrollIntoActiveLink();
    }

    private mapParentChildRelationships(): void {
        const processNavigationLevel = (items: NavItem[]): void => {
            items.forEach(item => {
                if (!item.children?.length) return;
                this.itemToChildren.set(item, item.children);
                processNavigationLevel(item.children);
            });
        };

        this.itemToChildren.clear();
        processNavigationLevel(this.navItems());
    }
}
