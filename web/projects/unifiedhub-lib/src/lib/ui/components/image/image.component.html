<img
    [src]="imageToShow()"
    class="z-[1]"
    [ngStyle]="{
        'object-fit': isObjectFitContain() ? 'contain' : 'cover',
        'transition': 'opacity 150ms ease-in',
        'opacity': isImageLoaded() ? 1 : 0
    }"
/>

<div
    class="flex items-center justify-center transition-colors"
    [ngClass]="{
        'bg-gray-200': !isImageLoaded(),
        'bg-transparent': isImageLoaded()
    }"
>
    @if (isErrorLoading()) {
        <i class="fa fa-times text-gray-400"></i>
    } @else if (!isImageLoaded()) {
        <lib-loading-ring />
    }
</div>
