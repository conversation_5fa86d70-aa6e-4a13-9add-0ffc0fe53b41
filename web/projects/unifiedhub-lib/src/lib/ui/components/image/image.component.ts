import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    input,
    signal,
} from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { NgClass, NgStyle } from '@angular/common';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { LoadingRingComponent } from '../loading-ring/loading-ring.component';

@Component({
    selector: 'lib-image',
    templateUrl: './image.component.html',
    styleUrl: './image.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgStyle, LoadingRingComponent, NgClass],
})
export class ImageComponent {
    public readonly imageObservable =
        input.required<Observable<Blob | string>>();
    public readonly isObjectFitContain = input<boolean>(true);

    protected readonly isImageLoaded = signal<boolean>(false);
    protected readonly imageToShow = signal<string | undefined>(undefined);
    protected readonly isErrorLoading = signal<boolean>(false);

    private readonly clearBlobUrlOnDestroy = signal<boolean>(false);

    public constructor(destroyRef: DestroyRef) {
        toObservable(this.imageObservable)
            .pipe(takeUntilDestroyed(destroyRef))
            .subscribe(imageObservable => {
                this.isImageLoaded.set(false);
                this.isErrorLoading.set(false);
                imageObservable
                    .pipe(
                        catchError(e => {
                            this.isErrorLoading.set(true);

                            return throwError(() => e);
                        }),
                    )
                    .subscribe(data => {
                        if (!data) return;

                        if (data instanceof Blob) {
                            this.clearBlobUrlOnDestroy.set(true);
                            this.imageToShow.set(URL.createObjectURL(data));
                        } else {
                            this.imageToShow.set(data);
                        }
                        this.isImageLoaded.set(true);
                    });
            });
    }
}
