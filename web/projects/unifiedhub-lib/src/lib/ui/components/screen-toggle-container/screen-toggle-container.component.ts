import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostListener,
    input,
    signal,
    viewChild,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { TranslateMultilingualStringPipe } from '../../../core';
import { NgClass } from '@angular/common';

@Component({
    selector: 'lib-screen-toggle-container',
    templateUrl: './screen-toggle-container.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, TranslateMultilingualStringPipe, NgClass],
})
export class ScreenToggleContainerComponent {
    public toggleSize = input<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('md');

    protected readonly isToggled = signal<boolean>(false);
    private readonly container =
        viewChild<ElementRef<HTMLElement>>('container');

    @HostListener('window:resize')
    public onResize(): void {
        this.isToggled.set(false);
    }

    @HostListener('document:click', ['$event'])
    public onClick(event: Event): void {
        if (
            !this.isToggled() ||
            this.container()?.nativeElement.contains(event.target as Node)
        )
            return;

        this.isToggled.set(false);
    }

    @HostListener('window:keyup', ['$event'])
    public async onEscape(event: KeyboardEvent): Promise<void> {
        if (event.key !== 'Escape' || !this.isToggled()) return;

        this.isToggled.set(false);
    }
}
