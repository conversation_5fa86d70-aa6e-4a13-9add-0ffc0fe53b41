import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostListener,
    input,
    signal,
} from '@angular/core';
import { MultilingualString } from '../../../common';
import { TranslateMultilingualStringPipe } from '../../../core';
import { Ng<PERSON>lass, NgTemplateOutlet } from '@angular/common';
import { data } from './data';

type TreeNode = {
    id: string;
    name: MultilingualString | string;
    childCount: number;
    children: TreeNode[];
    parent?: TreeNode;
};

@Component({
    selector: 'lib-tree-select-input',
    templateUrl: './tree-select-input.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateMultilingualStringPipe, NgTemplateOutlet, NgClass],
})
export class TreeSelectInputComponent {
    public readonly nodes = input<TreeNode[]>(data);

    protected isNodeListShown = signal<boolean>(false);
    protected selection = signal<TreeNode[]>([]);
    protected expandedNodes = signal<Set<TreeNode>>(new Set());

    public constructor(private readonly elementRef: ElementRef) {}

    @HostListener('document:click', ['$event'])
    public onClick(event: Event): void {
        const contained =
            !!(event.target as HTMLElement).getAttribute('data-contained') ||
            this.elementRef?.nativeElement.contains(event.target);
        if (!this.isNodeListShown() || contained) return;

        this.isNodeListShown.set(false);
    }

    @HostListener('window:keyup', ['$event'])
    public async onEscape(event: KeyboardEvent): Promise<void> {
        if (event.key !== 'Escape' || !this.isNodeListShown()) return;

        this.isNodeListShown.set(false);
    }

    protected add(node: TreeNode): void {
        if (this.selection().includes(node)) return;
        this.selection.update(x => [...x, node]);
    }

    protected remove(node: TreeNode): void {
        if (!this.selection().includes(node)) return;
        this.selection.update(x => x.filter(y => y !== node));
    }

    protected expand(node: TreeNode): void {
        this.expandedNodes.update(x => {
            x.add(node);
            return new Set(x);
        });
    }

    protected collapse(node: TreeNode): void {
        this.expandedNodes.update(x => {
            x.delete(node);
            return new Set(x);
        });
    }
}
