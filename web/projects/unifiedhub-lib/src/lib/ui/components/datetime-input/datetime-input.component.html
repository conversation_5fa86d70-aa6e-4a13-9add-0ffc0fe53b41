<div class="flex flex-row items-center gap-1">
    <div class="relative grow">
        <input
            [disabled]="isDisabled()"
            (blur)="onTouch?.()"
            placeholder="{{ placeholder() | translate }}"
            class="w-full"
            #input
        />
        <i
            class="fa fa-calendar-days pointer-events-none absolute end-4 top-1/2 -translate-y-1/2 text-gray-400"
        ></i>
    </div>

    @if (value() && !isDisabled()) {
        <button
            type="button"
            class="btn btn-sm btn-outline-danger"
            (click)="setValue(null)"
        >
            <i class="fa fa-times"></i>
        </button>
    }
</div>
