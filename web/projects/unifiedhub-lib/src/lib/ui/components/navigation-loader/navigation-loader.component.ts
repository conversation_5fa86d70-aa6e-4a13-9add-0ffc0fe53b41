import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { animate, style, transition, trigger } from '@angular/animations';
import { LoadingRingComponent } from '../loading-ring/loading-ring.component';

@Component({
    selector: 'lib-navigation-loader',
    templateUrl: './navigation-loader.component.html',
    styleUrl: './navigation-loader.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('isNavigating', [
            transition(':enter', [
                style({ opacity: 0 }),
                animate('100ms', style({ opacity: 1 })),
            ]),
            transition(':leave', [animate('100ms', style({ opacity: 0 }))]),
        ]),
    ],
    imports: [LoadingRingComponent],
})
export class NavigationLoaderComponent {
    public isNavigating = input.required<boolean>();
}
