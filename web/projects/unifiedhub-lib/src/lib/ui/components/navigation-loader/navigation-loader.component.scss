/* HTML: <div class="loader"></div> */
.loader {
    display: inline-flex;
    animation: l2-0 1s infinite;
    gap: 5px;
}

.loader::before,
.loader::after {
    width: 25px;
    animation: l2-1 1s infinite;
    aspect-ratio: 1;
    box-shadow: 0 0 0 3px inset #fff;
    content: '';
}

.loader::after {
    --s: -1;
}

@keyframes l2-0 {
    0%,
    50% {
        transform: rotate(0deg);
    }

    80%,
    100% {
        transform: rotate(180deg);
    }
}

@keyframes l2-1 {
    0% {
        transform: translate(0);
    }

    50%,
    80% {
        transform: translate(calc(var(--s, 1) * 2.5px));
    }

    100% {
        transform: translate(0);
    }
}
