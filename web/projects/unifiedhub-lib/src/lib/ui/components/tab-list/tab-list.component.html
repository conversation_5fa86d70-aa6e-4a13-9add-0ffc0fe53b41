<div
    [ngClass]="{
        'flex-row gap-2': isVertical(),
        'flex-col': !isVertical()
    }"
    class="flex"
>
    <!-- Tabs -->
    <ul
        [ngClass]="{
            'flex-col gap-2 overflow-y-auto': isVertical(),
            'flex-row gap-5 overflow-x-auto border-b': !isVertical()
        }"
        class="flex shrink-0 items-center border-primary-500/20 text-xs md:text-sm"
    >
        @for (item of tabs(); track item.id()) {
            <li
                (click)="setTabAsActive(item)"
                class="box-border flex flex-row items-center gap-4 text-nowrap p-4"
                [ngClass]="{
                    'relative self-stretch overflow-hidden rounded border':
                        isVertical(),
                    'border-primary text-primary after:absolute after:top-0 after:h-full after:w-1 after:bg-primary after:ltr:left-0 after:rtl:right-0':
                        item.getIsActive() && isVertical(),
                    'border-b-4 border-primary-500/80 font-bold text-black':
                        item.getIsActive() && !isVertical(),
                    'text-primary-500/70': !item.getIsActive() && !isVertical(),
                    'cursor-pointer transition-colors hover:bg-primary-100/30':
                        !item.getIsActive()
                }"
            >
                <span [ngClass]="{ 'grow': isVertical() }">
                    {{ item.tabTitle() | translate }}
                </span>

                @if (item.badgeCount()) {
                    <div
                        class="hidden h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-white md:flex"
                    >
                        {{ item.badgeCount() }}
                    </div>
                }
            </li>
        }
    </ul>

    <!-- Tab contents -->
    <div
        class="relative flex grow flex-col gap-8 overflow-x-auto rounded-b bg-primary-200/10 p-4"
    >
        @if (this.isVertical()) {
            <div
                class="-m-4 rounded-t bg-gradient-to-l from-primary-100 to-transparent p-4 font-bold text-primary-900"
            >
                {{ activeTab()?.tabTitle() ?? '' | translate }}
            </div>
        }

        <div>
            <ng-content select="lib-tab"></ng-content>
        </div>
    </div>
</div>
