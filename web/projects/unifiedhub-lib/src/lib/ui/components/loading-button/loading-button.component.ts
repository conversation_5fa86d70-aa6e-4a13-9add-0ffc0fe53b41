import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { LoadingRingComponent } from '../loading-ring/loading-ring.component';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';

@Component({
    selector: 'lib-loading-button',
    templateUrl: './loading-button.component.html',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [LoadingRingComponent, TranslateModule, NgClass],
})
export class LoadingButtonComponent {
    public readonly isLoading = input.required<boolean>();
    public readonly label = input<string>();
    public readonly icon = input<string>();
    public readonly type = input<
        'primary' | 'success' | 'info' | 'warning' | 'danger'
    >('primary');
    public readonly size = input<'small' | 'default' | 'large'>('default');
}
