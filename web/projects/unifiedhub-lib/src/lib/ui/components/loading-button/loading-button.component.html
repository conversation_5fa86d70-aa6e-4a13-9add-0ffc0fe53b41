<button
    [disabled]="isLoading()"
    class="btn flex flex-row items-center gap-2"
    [ngClass]="{
        'btn-primary': type() === 'primary',
        'btn-success': type() === 'success',
        'btn-info': type() === 'info',
        'btn-warning': type() === 'warning',
        'btn-danger': type() === 'danger',
        'btn-sm': size() === 'small',
        'btn-lg': size() === 'large'
    }"
>
    @if (icon()) {
        <i class="{{ icon() }}"></i>
    }
    @if (label()) {
        <span>{{ label()! | translate }}</span>
    }
    @if (isLoading()) {
        <lib-loading-ring />
    }
</button>
