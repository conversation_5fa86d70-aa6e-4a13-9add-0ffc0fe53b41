import {
    EnvironmentProviders,
    importProvidersFrom,
    LOCALE_ID,
    Provider,
} from '@angular/core';
import {
    authInterceptor,
    AuthService,
    cacheInterceptor,
    cancelOnNavigationInterceptor,
    errorInterceptor,
    langInterceptor,
    LocalStorageService,
    MultilingualStringTranslatorService,
    RbacService,
    SignalrService,
    timezoneInterceptor,
    TitleService,
    ToastService,
} from './core';
import {
    ModalService,
    ongoingRequestLoaderInterceptor,
    OngoingRequestLoaderService,
    SmartAlertService,
} from './features';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { FORMLY_CONFIG, FormlyModule } from '@ngx-formly/core';
import {
    formlyConfig,
    registerTranslateExtension,
} from './features/formly/config';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslationModule } from '@ng-omar/translation';
import { APP_CONFIG, AppConfig, translationConfig } from './config';
import { ServicingService } from './modules/servicing';
import { FlowsService } from './features/flows/services/flows.service';

export const appProviders = (
    config: AppConfig,
): (Provider | EnvironmentProviders)[] => [
    provideAnimations(),
    provideHttpClient(
        withInterceptors([
            errorInterceptor,
            langInterceptor,
            timezoneInterceptor,
            authInterceptor,
            cacheInterceptor,
            cancelOnNavigationInterceptor,
            ongoingRequestLoaderInterceptor,
        ]),
    ),
    importProvidersFrom(
        FormlyModule.forRoot(formlyConfig),
        TranslateModule.forRoot(),
        TranslationModule.forRoot(translationConfig(config.translation)),
    ),
    ModalService,
    ToastService,
    SmartAlertService,
    AuthService,
    RbacService,
    TitleService,
    LocalStorageService,
    MultilingualStringTranslatorService,
    SignalrService,
    OngoingRequestLoaderService,

    { provide: LOCALE_ID, useValue: localStorage.getItem('lang') || 'en' },

    {
        provide: FORMLY_CONFIG,
        multi: true,
        useFactory: registerTranslateExtension,
        deps: [TranslateService],
    },

    {
        provide: APP_CONFIG,
        useValue: config,
    },

    // Shared services:
    ServicingService,
    FlowsService,
];
